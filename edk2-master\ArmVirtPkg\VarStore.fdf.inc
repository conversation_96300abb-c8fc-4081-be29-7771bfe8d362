## @file
#  FDF include file with FD definition that defines an empty variable store.
#
#  Copyright (c) 2016, Linaro, Ltd. All rights reserved.<BR>
#  Copyright (C) 2014, Red Hat, Inc.<BR>
#  Copyright (c) 2006 - 2013, Intel Corporation. All rights reserved.<BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[FD.QEMU_VARS]
BaseAddress   = 0x00000000
Size          = 0xc0000
ErasePolarity = 1
BlockSize     = 0x40000
NumBlocks     = 3


0x00000000|0x00040000
#NV_VARIABLE_STORE
DATA = {
  ## This is the EFI_FIRMWARE_VOLUME_HEADER
  # ZeroVector []
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  # FileSystemGuid: gEfiSystemNvDataFvGuid         =
  #   { 0xFFF12B8D, 0x7696, 0x4C8B,
  #     { 0xA9, 0x85, 0x27, 0x47, 0x07, 0x5B, 0x4F, 0x50 }}
  0x8D, 0x2B, 0xF1, 0xFF, 0x96, 0x76, 0x8B, 0x4C,
  0xA9, 0x85, 0x27, 0x47, 0x07, 0x5B, 0x4F, 0x50,
  # FvLength: 0xC0000
  0x00, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00,
  # Signature "_FVH"       # Attributes
  0x5f, 0x46, 0x56, 0x48, 0xff, 0xfe, 0x04, 0x00,
  # HeaderLength # CheckSum # ExtHeaderOffset #Reserved #Revision
  0x48, 0x00, 0x28, 0x09, 0x00, 0x00, 0x00, 0x02,
  # Blockmap[0]: 0x3 Blocks * 0x40000 Bytes / Block
  0x3, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00,
  # Blockmap[1]: End
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  ## This is the VARIABLE_STORE_HEADER
  # It is compatible with SECURE_BOOT_ENABLE == FALSE as well.
  # Signature: gEfiAuthenticatedVariableGuid =
  #   { 0xaaf32c78, 0x947b, 0x439a,
  #     { 0xa1, 0x80, 0x2e, 0x14, 0x4e, 0xc3, 0x77, 0x92 }}
  0x78, 0x2c, 0xf3, 0xaa, 0x7b, 0x94, 0x9a, 0x43,
  0xa1, 0x80, 0x2e, 0x14, 0x4e, 0xc3, 0x77, 0x92,
  # Size: 0x40000 (gEfiMdeModulePkgTokenSpaceGuid.PcdFlashNvStorageVariableSize) -
  #         0x48 (size of EFI_FIRMWARE_VOLUME_HEADER) = 0x3ffb8
  # This can speed up the Variable Dispatch a bit.
  0xB8, 0xFF, 0x03, 0x00,
  # FORMATTED: 0x5A #HEALTHY: 0xFE #Reserved: UINT16 #Reserved1: UINT32
  0x5A, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
}


0x00040000|0x00040000
#NV_FTW_WORKING
DATA = {
  # EFI_FAULT_TOLERANT_WORKING_BLOCK_HEADER->Signature = gEdkiiWorkingBlockSignatureGuid         =
  #  { 0x9e58292b, 0x7c68, 0x497d, { 0xa0, 0xce, 0x65,  0x0, 0xfd, 0x9f, 0x1b, 0x95 }}
  0x2b, 0x29, 0x58, 0x9e, 0x68, 0x7c, 0x7d, 0x49,
  0xa0, 0xce, 0x65,  0x0, 0xfd, 0x9f, 0x1b, 0x95,
  # Crc:UINT32            #WorkingBlockValid:1, WorkingBlockInvalid:1, Reserved
  0x5b, 0xe7, 0xc6, 0x86, 0xFE, 0xFF, 0xFF, 0xFF,
  # WriteQueueSize: UINT64
  0xE0, 0xFF, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00
}

0x00080000|0x00040000
#NV_FTW_SPARE

## @file
#  The CPU specific programming for PiSmmCpuDxeSmm module when STM support
#  is included.
#
#  Copyright (c) 2009 - 2023, Intel Corporation. All rights reserved.<BR>
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = SmmCpuFeaturesLibStm
  MODULE_UNI_FILE                = SmmCpuFeaturesLib.uni
  FILE_GUID                      = 374DE830-81C5-4CC8-B2AB-28F0AB73710B
  MODULE_TYPE                    = DXE_SMM_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = SmmCpuFeaturesLib
  CONSTRUCTOR                    = SmmCpuFeaturesLibStmConstructor

[Sources]
  CpuFeaturesLib.h
  IntelSmmCpuFeaturesLib.c
  SmmCpuFeaturesLibCommon.c
  SmmStm.c
  SmmStm.h
  TraditionalMmCpuFeaturesLib.c

[Sources.Ia32]
  Ia32/SmmStmSupport.c


  Ia32/SmiEntry.nasm
  Ia32/SmiException.nasm

[Sources.X64]
  X64/SmmStmSupport.c


  X64/SmiEntry.nasm
  X64/SmiException.nasm

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  UefiCpuPkg/UefiCpuPkg.dec

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  PcdLib
  HobLib
  MemoryAllocationLib
  DebugLib
  UefiBootServicesTableLib
  SmmServicesTableLib
  TpmMeasurementLib

[Protocols]
  gEfiMpServiceProtocolGuid                ## CONSUMES
  gEfiSmmEndOfDxeProtocolGuid              ## CONSUMES
  gEfiSmMonitorInitProtocolGuid            ## PRODUCES

[Guids]
  gMsegSmramGuid                           ## SOMETIMES_CONSUMES ## HOB
  gEfiAcpi20TableGuid                      ## SOMETIMES_CONSUMES ## SystemTable
  gEfiAcpi10TableGuid                      ## SOMETIMES_CONSUMES ## SystemTable
  gSmmBaseHobGuid                          ## CONSUMES

[Pcd]
  gUefiCpuPkgTokenSpaceGuid.PcdCpuMaxLogicalProcessorNumber        ## SOMETIMES_CONSUMES
  gUefiCpuPkgTokenSpaceGuid.PcdCpuMsegSize                         ## SOMETIMES_CONSUMES
  gUefiCpuPkgTokenSpaceGuid.PcdCpuSmmStmExceptionStackSize         ## SOMETIMES_CONSUMES
  gUefiCpuPkgTokenSpaceGuid.PcdCpuSmmStackGuard                    ## CONSUMES

[FeaturePcd]
  gUefiCpuPkgTokenSpaceGuid.PcdSmrrEnable  ## CONSUMES
  gUefiCpuPkgTokenSpaceGuid.PcdSmmFeatureControlEnable  ## CONSUMES

[Depex]
  gEfiMpServiceProtocolGuid

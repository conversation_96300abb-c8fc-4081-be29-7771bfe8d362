## @file
# CI configuration for UnitTestFrameworkPkg
#
# Copyright (c) Microsoft Corporation
# SPDX-License-Identifier: BSD-2-Clause-Patent
##
{
    "PrEval": {
        "DscPath": "UnitTestFrameworkPkg.dsc",
    },
    ## options defined .pytool/Plugin/LicenseCheck
    "LicenseCheck": {
        "IgnoreFiles": []
    },
    "EccCheck": {
        ## Exception sample looks like below:
        ## "ExceptionList": [
        ##     "<ErrorID>", "<KeyWord>"
        ## ]
        "ExceptionList": [
            "9005", "@MRT",
            "7007", "_UNIT_TEST_FAILURE_TYPE_STRING"
        ],
        ## Both file path and directory path are accepted.
        "IgnoreFiles": [
            "Library/CmockaLib/cmocka",
            "Library/UnitTestLib/RunTestsCmocka.c"
        ]
    },
    ## options defined .pytool/Plugin/CompilerPlugin
    "CompilerPlugin": {
        "DscPath": "UnitTestFrameworkPkg.dsc"
    },
    ## options defined .pytool/Plugin/HostUnitTestCompilerPlugin
    "HostUnitTestCompilerPlugin": {
        "DscPath": "Test/UnitTestFrameworkPkgHostTest.dsc"
    },
    ## options defined .pytool/Plugin/CharEncodingCheck
    "CharEncodingCheck": {
        "IgnoreFiles": []
    },

    ## options defined .pytool/Plugin/DependencyCheck
    "DependencyCheck": {
        "AcceptableDependencies": [
            "MdePkg/MdePkg.dec",
            "UnitTestFrameworkPkg/UnitTestFrameworkPkg.dec"
        ],
        # For host based unit tests
        "AcceptableDependencies-HOST_APPLICATION":[],
        # For UEFI shell based apps
        "AcceptableDependencies-UEFI_APPLICATION":[
            "MdeModulePkg/MdeModulePkg.dec",
            "ShellPkg/ShellPkg.dec"
        ],
        "IgnoreInf": []
    },
    ## options defined .pytool/Plugin/DscCompleteCheck
    "DscCompleteCheck": {
        "DscPath": "UnitTestFrameworkPkg.dsc",
        "IgnoreInf": []
    },
    ## options defined .pytool/Plugin/HostUnitTestDscCompleteCheck
    "HostUnitTestDscCompleteCheck": {
        "IgnoreInf": [
            "UnitTestFrameworkPkg/Test/GoogleTest/Sample/SampleGoogleTestExpectFail/SampleGoogleTestHostExpectFail.inf",
            "UnitTestFrameworkPkg/Test/GoogleTest/Sample/SampleGoogleTestGenerateException/SampleGoogleTestHostGenerateException.inf",
            "UnitTestFrameworkPkg/Test/UnitTest/Sample/SampleUnitTestExpectFail/SampleUnitTestHostExpectFail.inf",
            "UnitTestFrameworkPkg/Test/UnitTest/Sample/SampleUnitTestGenerateException/SampleUnitTestHostGenerateException.inf",
            "UnitTestFrameworkPkg/Test/UnitTest/Sample/SampleUnitTestDoubleFree/SampleUnitTestDoubleFree.inf",
            "UnitTestFrameworkPkg/Test/UnitTest/Sample/SampleUnitTestBufferOverflow/SampleUnitTestBufferOverflow.inf",
            "UnitTestFrameworkPkg/Test/UnitTest/Sample/SampleUnitTestBufferUnderflow/SampleUnitTestBufferUnderflow.inf",
            "UnitTestFrameworkPkg/Test/UnitTest/Sample/SampleUnitTestNullAddress/SampleUnitTestNullAddress.inf",
            "UnitTestFrameworkPkg/Test/UnitTest/Sample/SampleUnitTestInvalidAddress/SampleUnitTestInvalidAddress.inf"
        ],
        "DscPath": "Test/UnitTestFrameworkPkgHostTest.dsc"
    },
    ## options defined .pytool/Plugin/GuidCheck
    "GuidCheck": {
        "IgnoreGuidName": [],
        "IgnoreGuidValue": [],
        "IgnoreFoldersAndFiles": [],
        "IgnoreDuplicates": []
    },
    ## options defined .pytool/Plugin/LibraryClassCheck
    "LibraryClassCheck": {
        "IgnoreHeaderFile": []
    },

    ## options defined .pytool/Plugin/SpellCheck
    "SpellCheck": {
        "AuditOnly": False,           # Fails test but run in AuditOnly mode to collect log
        "IgnoreFiles": [             # use gitignore syntax to ignore errors in matching files
            "Library/CmockaLib/cmocka/**/*.*",  # not going to spell check a submodule
            "Library/GoogleTestLib/googletest/**/*.*",  # not going to spell check a submodule
            "Library/SubhookLib/subhook/**/*.*"  # not going to spell check a submodule
        ],
        "ExtendWords": [             # words to extend to the dictionary for this package
            "noreplace",
            "Pointee",
            "gmock",
            "GMOCK",
            "DSUBHOOK",
            "testcase",
            "testsuites",
            "cmocka",
            "buildmodule",
            "criterium",
            "pytool",
            "pytools",
            "NOFAILURE",
            "cmockery",
            "cobertura",
            "DHAVE", # build flag for cmocka in the INF
            "gtest", # file name in GoogleTestLib.inf
            "defiapi",      # build flag for gtest
            "fexceptions",  # build flag for gtest
            "corthon",      # Contact GitHub account in Readme
            "mdkinney",     # Contact GitHub account in Readme
            "spbrogan"      # Contact GitHub account in Readme
        ],
        "IgnoreStandardPaths": [],   # Standard Plugin defined paths that should be ignore
        "AdditionalIncludePaths": [] # Additional paths to spell check (wildcards supported)
    },

    # options defined in .pytool/Plugin/UncrustifyCheck
    "UncrustifyCheck": {
        "IgnoreFiles": [
            "Library/CmockaLib/cmocka/**",
            "Library/GoogleTestLib/googletest/**",
            "Library/SubhookLib/subhook/**"
        ]
    }
}

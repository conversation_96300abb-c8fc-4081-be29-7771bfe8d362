// /** @file
// Instance of Memory Allocation Library based on POSIX APIs
//
// Uses POSIX APIs malloc() and free() to allocate and free memory.
//
// Copyright (c) 2020, Intel Corporation. All rights reserved.<BR>
//
// SPDX-License-Identifier: BSD-2-Clause-Patent
//
// **/

#string STR_MODULE_ABSTRACT             #language en-US "Instance of Memory Allocation Library based on POSIX APIs"

#string STR_MODULE_DESCRIPTION          #language en-US "Uses POSIX APIs malloc() and free() to allocate and free memory."

## @file
#  SmmStore library for coreboot
#
#  Copyright (c) 2022 9elements GmbH.<BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = SmmStoreLib
  FILE_GUID                      = 40A2CBC6-CFB8-447b-A90E-298E88FD345E
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = SmmStoreLib

[Sources]
  SmmStore.c
  SmmStore.h

[Sources.X64]
  X64/SmmStore.nasm

[LibraryClasses]
  BaseMemoryLib
  DebugLib
  DxeServicesTableLib
  HobLib
  MemoryAllocationLib
  UefiBootServicesTableLib

[Guids]
  gEfiSmmStoreInfoHobGuid           ## CONSUMES
  gEfiEventVirtualAddressChangeGuid ## CONSUMES

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  UefiPayloadPkg/UefiPayloadPkg.dec

## @file
#  Early Platform Hook Library instance for 16550 Uart.
#
#  Copyright (c) 2020, ARM Ltd. All rights reserved.<BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x0001001B
  BASE_NAME                      = EarlyFdt16550SerialPortHookLib
  MODULE_UNI_FILE                = Fdt16550SerialPortHookLib.uni
  FILE_GUID                      = FFB19961-79CC-4684-84A8-C31B0A2BBE82
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = PlatformHookLib|SEC PEI_CORE PEIM

[Sources]
  EarlyFdt16550SerialPortHookLib.c

[LibraryClasses]
  BaseLib
  PcdLib
  FdtSerialPortAddressLib
  HobLib

[Packages]
  ArmVirtPkg/ArmVirtPkg.dec
  MdeModulePkg/MdeModulePkg.dec
  MdePkg/MdePkg.dec
  OvmfPkg/OvmfPkg.dec

[Pcd]
  gUefiOvmfPkgTokenSpaceGuid.PcdDeviceTreeInitialBaseAddress
  gEfiMdeModulePkgTokenSpaceGuid.PcdSerialRegisterBase

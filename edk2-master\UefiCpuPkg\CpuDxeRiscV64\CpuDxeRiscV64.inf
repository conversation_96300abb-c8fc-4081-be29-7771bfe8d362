## @file
#  RISC-V CPU DXE module.
#
#  Copyright (c) 2022, Ventana Micro Systems Inc. All rights reserved.<BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x0001001B
  BASE_NAME                      = CpuDxeRiscV64
  MODULE_UNI_FILE                = CpuDxeRiscV64.uni
  FILE_GUID                      = BDEA19E2-778F-473C-BF82-5E38D6A27765
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = InitializeCpu

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  UefiCpuPkg/UefiCpuPkg.dec

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  CpuLib
  DebugLib
  DxeServicesTableLib
  MemoryAllocationLib
  UefiBootServicesTableLib
  UefiDriverEntryPoint
  UefiLib
  CpuExceptionHandlerLib
  HobLib
  ReportStatusCodeLib
  TimerLib
  PeCoffGetEntryPointLib
  RiscVSbiLib
  RiscVMmuLib
  CacheMaintenanceLib
  TimerLib

[Sources]
  CpuDxe.c
  CpuDxe.h

[Protocols]
  gEfiCpuArchProtocolGuid                       ## PRODUCES
  gRiscVEfiBootProtocolGuid                     ## PRODUCES

[Guids]
  gIdleLoopEventGuid                            ## CONSUMES           ## Event

[Ppis]
  gEfiSecPlatformInformation2PpiGuid            ## UNDEFINED # HOB
  gEfiSecPlatformInformationPpiGuid             ## UNDEFINED # HOB

[Pcd]
  gEfiMdeModulePkgTokenSpaceGuid.PcdCpuStackGuard                       ## CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdHeapGuardPropertyMask               ## CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdNullPointerDetectionPropertyMask    ## CONSUMES
  gUefiCpuPkgTokenSpaceGuid.PcdCpuStackSwitchExceptionList              ## CONSUMES
  gUefiCpuPkgTokenSpaceGuid.PcdCpuKnownGoodStackSize                    ## CONSUMES

[Depex]
  TRUE

[UserExtensions.TianoCore."ExtraFiles"]
  CpuDxeRiscV64Extra.uni

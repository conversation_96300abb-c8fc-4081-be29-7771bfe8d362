## @file
#  CPU driver installs CPU Architecture Protocol and CPU MP protocol.
#
#  Copyright (c) 2008 - 2019, Intel Corporation. All rights reserved.<BR>
#  Copyright (c) 2017, AMD Incorporated. All rights reserved.<BR>
#  Copyright (c) 2024, Loongson Technology Corporation Limited. All rights reserved.<BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = CpuDxe
  MODULE_UNI_FILE                = CpuDxe.uni
  FILE_GUID                      = 1A1E4886-9517-440e-9FDE-3BE44CEE2136
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = InitializeCpu

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  UefiCpuPkg/UefiCpuPkg.dec

[LibraryClasses.common]
  BaseLib
  BaseMemoryLib
  CpuExceptionHandlerLib
  CpuLib
  DebugLib
  DxeServicesTableLib
  HobLib
  MemoryAllocationLib
  MpInitLib
  PeCoffGetEntryPointLib
  ReportStatusCodeLib
  TimerLib
  UefiBootServicesTableLib
  UefiDriverEntryPoint
  UefiLib

[LibraryClasses.IA32, LibraryClasses.X64]
  LocalApicLib
  MtrrLib

[LibraryClasses.LoongArch64]
  CacheMaintenanceLib
  CpuMmuLib

[Sources.IA32, Sources.X64]
  CpuDxe.c
  CpuDxe.h
  CpuGdt.c
  CpuGdt.h
  CpuMp.c
  CpuMp.h
  CpuPageTable.h
  CpuPageTable.c

[Sources.IA32]
  Ia32/CpuAsm.nasm
  Ia32/PagingAttribute.c

[Sources.X64]
  X64/CpuAsm.nasm
  X64/PagingAttribute.c

[Sources.LoongArch64]
  CpuMp.h
  LoongArch64/CpuDxe.c
  LoongArch64/CpuMp.c
  LoongArch64/Exception.c
  LoongArch64/CpuDxe.h

[Protocols]
  gEfiCpuArchProtocolGuid                       ## PRODUCES
  gEfiMemoryAttributeProtocolGuid               ## PRODUCES
  gEfiMpServiceProtocolGuid                     ## PRODUCES
  gEfiSmmBase2ProtocolGuid                      ## SOMETIMES_CONSUMES

[Guids]
  gIdleLoopEventGuid                            ## CONSUMES           ## Event
  gEfiVectorHandoffTableGuid                    ## SOMETIMES_CONSUMES ## SystemTable

[Ppis]
  gEfiSecPlatformInformation2PpiGuid            ## UNDEFINED # HOB
  gEfiSecPlatformInformationPpiGuid             ## UNDEFINED # HOB

[Pcd]
  gEfiMdeModulePkgTokenSpaceGuid.PcdPteMemoryEncryptionAddressOrMask    ## CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdCpuStackGuard                       ## CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdHeapGuardPropertyMask               ## CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdNullPointerDetectionPropertyMask    ## CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdTdxSharedBitMask                    ## CONSUMES
  gUefiCpuPkgTokenSpaceGuid.PcdCpuStackSwitchExceptionList              ## CONSUMES
  gUefiCpuPkgTokenSpaceGuid.PcdCpuKnownGoodStackSize                    ## CONSUMES

[Pcd.LoongArch64]
  gUefiCpuPkgTokenSpaceGuid.PcdLoongArchExceptionVectorBaseAddress      ## CONSUMES

[Depex]
  TRUE

[UserExtensions.TianoCore."ExtraFiles"]
  CpuDxeExtra.uni

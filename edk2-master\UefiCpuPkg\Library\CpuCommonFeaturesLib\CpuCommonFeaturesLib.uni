// /** @file
// Dxe Crc32 Guided Section Extract library.
//
// This library doesn't produce any library class. The constructor function uses
// ExtractGuidedSectionLib service to register CRC32 guided section handler
// that parses CRC32 encapsulation section and extracts raw data.
//
// It uses UEFI boot service CalculateCrc32 to authenticate 32 bit CRC value.
//
// Copyright (c) 2006 - 2014, Intel Corporation. All rights reserved.<BR>
//
// SPDX-License-Identifier: BSD-2-Clause-Patent
//
// **/


#string STR_MODULE_ABSTRACT             #language en-US "Dxe Crc32 Guided Section Extract library."

#string STR_MODULE_DESCRIPTION          #language en-US "This library doesn't produce any library class. The constructor function uses ExtractGuidedSectionLib service to register CRC32 guided section handler that parses CRC32 encapsulation section and extracts raw data. It uses UEFI boot service CalculateCrc32 to authenticate 32 bit CRC value."


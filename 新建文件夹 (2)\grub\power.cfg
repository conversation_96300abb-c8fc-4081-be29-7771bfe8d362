submenu "$VTLANG_POWER" --class=debug_power --class=F5tool {
    menuentry "$VTLANG_POWER_REBOOT" --class=debug_reboot --class=debug_power --class=F5tool {
        echo -e '\n\nSystem is rebooting ... \n'
        sleep 1
        reboot
    }

    menuentry "$VTLANG_POWER_HALT" --class=debug_halt --class=debug_power --class=F5tool {
        echo -e '\n\nSystem is halting ... \n'
        sleep 1
        halt
    }
    
    if [ "$grub_platform" != "pc" ]; then
        menuentry "$VTLANG_POWER_BOOT_EFIFW" --class=debug_efisetup --class=debug_power --class=F5tool {            
            echo -e '\n\nRebooting to enter UEFI firmware setup ... \n'
            sleep 1
            fwsetup
        }
    fi

    menuentry "$VTLANG_RETURN_PREVIOUS" --class=vtoyret VTOY_RET {
        echo "Return ..."
    }
}

#/** @file
#
#  Copyright (c) 2018 - 2021, Intel Corporation. All rights reserved.<BR>
#  Copyright (c) 2008 - 2010, Apple Inc. All rights reserved.<BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#
#**/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = HobLib
  FILE_GUID                      = AD6B4D55-8DBE-48C8-88E3-CFDBB6E9D193
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = HobLib

#
#  VALID_ARCHITECTURES           = IA32 X64 EBC
#

[Sources.common]
  Hob.c

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  DebugLib

[Guids]
  gEfiHobMemoryAllocModuleGuid
  gEfiHobMemoryAllocStackGuid

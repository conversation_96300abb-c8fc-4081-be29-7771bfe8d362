#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
密钥生成逻辑分析工具
尝试找到5F1CE76E1FAF0386的生成规律
"""

import hashlib
import struct
import time
import datetime
import platform
import uuid
import os

def analyze_target_key():
    target_key = "5F1CE76E1FAF0386"
    target_bytes = bytes.fromhex(target_key)
    
    print("=== 目标密钥分析 ===")
    print(f"密钥: {target_key}")
    print(f"字节: {target_bytes.hex().upper()}")
    print(f"长度: {len(target_key)} 字符 ({len(target_bytes)} 字节)")
    print()
    
    # 分析字节模式
    print("字节模式分析:")
    for i, b in enumerate(target_bytes):
        print(f"  字节{i}: 0x{b:02X} ({b:3d}) '{chr(b) if 32 <= b <= 126 else '.'}'")
    print()
    
    # 尝试各种解释
    print("可能的解释:")
    
    # 1. 时间戳相关
    print("1. 时间戳分析:")
    try:
        # Unix时间戳（秒）
        timestamp_s = struct.unpack('>Q', target_bytes)[0]
        if timestamp_s < 2**32:  # 合理的时间戳范围
            dt = datetime.datetime.fromtimestamp(timestamp_s)
            print(f"   Unix时间戳(秒): {timestamp_s} -> {dt}")
    except:
        pass
    
    try:
        # Unix时间戳（毫秒）
        timestamp_ms = struct.unpack('>Q', target_bytes)[0]
        if timestamp_ms > 1000000000000:  # 毫秒级时间戳
            dt = datetime.datetime.fromtimestamp(timestamp_ms / 1000)
            print(f"   Unix时间戳(毫秒): {timestamp_ms} -> {dt}")
    except:
        pass
    
    # 2. 哈希分析
    print("\n2. 哈希分析:")
    test_inputs = [
        "BOOT.EFI",
        "VENTOY", 
        "WinPE",
        "5F1CE76E1FAF0386",  # 自引用
        "8GB",
        "USB",
        "UEFI",
        "EFI",
        "Microsoft",
        "Windows",
        "PE",
        "Boot",
        "Key",
        "Auth",
        "License",
        "Serial",
        "GUID",
        "UUID",
        # 组合
        "BOOT.EFI_VENTOY",
        "WinPE_8GB",
        "USB_UEFI",
    ]
    
    for test_input in test_inputs:
        # MD5
        md5_full = hashlib.md5(test_input.encode()).hexdigest().upper()
        md5_8 = md5_full[:16]
        
        # SHA1
        sha1_full = hashlib.sha1(test_input.encode()).hexdigest().upper()
        sha1_8 = sha1_full[:16]
        
        # SHA256
        sha256_full = hashlib.sha256(test_input.encode()).hexdigest().upper()
        sha256_8 = sha256_full[:16]
        
        if md5_8 == target_key:
            print(f"   *** MD5匹配: '{test_input}' -> {md5_8}")
        if sha1_8 == target_key:
            print(f"   *** SHA1匹配: '{test_input}' -> {sha1_8}")
        if sha256_8 == target_key:
            print(f"   *** SHA256匹配: '{test_input}' -> {sha256_8}")
    
    # 3. 数学模式
    print("\n3. 数学模式分析:")
    value = int(target_key, 16)
    print(f"   十进制值: {value}")
    print(f"   二进制: {bin(value)}")
    print(f"   八进制: {oct(value)}")
    
    # 检查是否是特殊数字
    import math
    sqrt_val = math.sqrt(value)
    if sqrt_val == int(sqrt_val):
        print(f"   是完全平方数: {int(sqrt_val)}²")
    
    # 4. 系统相关
    print("\n4. 系统信息分析:")
    system_info = {
        'system': platform.system(),
        'machine': platform.machine(),
        'processor': platform.processor(),
        'node': platform.node(),
        'version': platform.version(),
    }
    
    for key, value in system_info.items():
        if value:
            test_hash = hashlib.md5(value.encode()).hexdigest()[:16].upper()
            if test_hash == target_key:
                print(f"   *** 系统信息匹配: {key}='{value}' -> {test_hash}")
    
    # 5. 特殊模式检查
    print("\n5. 特殊模式:")
    
    # 检查是否是某种编码
    try:
        # Base64解码尝试
        import base64
        decoded = base64.b64decode(target_key + "==")  # 添加padding
        print(f"   Base64解码尝试: {decoded}")
    except:
        pass
    
    # 检查XOR模式
    print(f"   XOR分析:")
    for i in range(1, len(target_bytes)):
        xor_result = target_bytes[0] ^ target_bytes[i]
        print(f"     字节0 XOR 字节{i}: 0x{xor_result:02X}")

def generate_possible_keys():
    """生成可能的密钥"""
    print("\n=== 生成可能的密钥 ===")
    
    # 基于当前时间的各种变体
    now = time.time()
    current_time = datetime.datetime.now()
    
    time_variants = [
        int(now),  # 当前Unix时间戳
        int(now) // 3600,  # 小时级时间戳
        int(now) // 86400,  # 天级时间戳
        current_time.year,
        current_time.month,
        current_time.day,
        current_time.hour,
        current_time.year * 10000 + current_time.month * 100 + current_time.day,
    ]
    
    print("基于时间的可能密钥:")
    for variant in time_variants:
        key = hashlib.md5(str(variant).encode()).hexdigest()[:16].upper()
        print(f"   时间变体 {variant}: {key}")

if __name__ == "__main__":
    analyze_target_key()
    generate_possible_keys()

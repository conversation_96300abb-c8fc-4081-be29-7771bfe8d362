## @file
# Sample UnitTest built for execution on a Host/Dev machine.
# All test case are always expected to fail to demonstrate the format of the log
# file and reports when failures occur.
#
# Copyright (c) 2024, Intel Corporation. All rights reserved.<BR>
# SPDX-License-Identifier: BSD-2-Clause-Patent
##

[Defines]
  INF_VERSION    = 0x00010005
  BASE_NAME      = SampleUnitTestHostExpectFail
  FILE_GUID      = C419E68B-D5C6-4F35-AE99-470946328A1F
  MODULE_TYPE    = HOST_APPLICATION
  VERSION_STRING = 1.0

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources]
  SampleUnitTestExpectFail.c

[Packages]
  MdePkg/MdePkg.dec

[LibraryClasses]
  BaseLib
  DebugLib
  UnitTestLib

[Pcd]
  gEfiMdePkgTokenSpaceGuid.PcdDebugPropertyMask

## @file
#  ACPI Timer Library Instance.
#
#  Copyright (c) 2014 - 2018, Intel Corporation. All rights reserved.<BR>
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = AcpiTimerLib
  FILE_GUID                      = A41BF616-EF77-4658-9992-D813071C34CF
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = TimerLib

  CONSTRUCTOR                    = AcpiTimerLibConstructor

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 EBC
#

[Sources]
  AcpiTimerLib.c

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  UefiPayloadPkg/UefiPayloadPkg.dec

[LibraryClasses]
  BaseLib
  IoLib
  HobLib
  DebugLib

[Guids]
  gUefiAcpiBoardInfoGuid

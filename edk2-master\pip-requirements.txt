## @file
# EDK II Python PIP requirements file
#
# This file provides the list of python components to install using PIP.
#
# Copyright (c) Microsoft Corporation.
# SPDX-License-Identifier: BSD-2-Clause-Patent
#
# https://pypi.org/project/pip/
# https://pip.pypa.io/en/stable/user_guide/#requirements-files
# https://pip.pypa.io/en/stable/reference/requirements-file-format
# https://www.python.org/dev/peps/pep-0440/#version-specifiers
##

edk2-pytool-library~=0.23.4
edk2-pytool-extensions~=0.29.11
antlr4-python3-runtime==4.9
lcov-cobertura==2.1.1
regex==2024.11.6

## @file
#  Coreboot Table Parse Library.
#
#  Copyright (c) 2024, Intel Corporation. All rights reserved.<BR>
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = FdtParseLib
  FILE_GUID                      = 8956F72D-9626-4959-98B7-1BD4A3EA687E
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = FdtParseLib

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 AARCH64
#

[Sources]
  FdtParserLib.c

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  UefiPayloadPkg/UefiPayloadPkg.dec

[LibraryClasses]
  DebugLib
  PcdLib
  HobLib
  FdtLib
  CustomFdtNodeParserLib

[Guids]
  gUniversalPayloadDeviceTreeGuid
  gEfiGraphicsInfoHobGuid
  gEfiGraphicsDeviceInfoHobGuid
  gUniversalPayloadAcpiTableGuid
  gUniversalPayloadSerialPortInfoGuid
  gUplPciSegmentInfoHobGuid

[Pcd.IA32,Pcd.X64,Pcd.RISCV64,Pcd.AARCH64]
  gUefiPayloadPkgTokenSpaceGuid.PcdPayloadFdMemSize
  gUefiPayloadPkgTokenSpaceGuid.PcdSystemMemoryUefiRegionSize
  gUefiPayloadPkgTokenSpaceGuid.PcdHandOffFdtEnable
  gUefiPayloadPkgTokenSpaceGuid.PcdPayloadFdMemBase
  gUefiPayloadPkgTokenSpaceGuid.PcdPciReservedPMemBase
  gUefiPayloadPkgTokenSpaceGuid.PcdPciReservedPMemLimit
  gUefiPayloadPkgTokenSpaceGuid.PcdPciReservedPMemAbove4GBBase
  gUefiPayloadPkgTokenSpaceGuid.PcdPciReservedPMemAbove4GBLimit
  gUefiPayloadPkgTokenSpaceGuid.SizeOfIoSpace


[BuildOptions]
  MSFT:*_*_*_CC_FLAGS = /wd4305
  GCC:*_*_IA32_CC_FLAGS    = -Wno-error=pointer-to-int-cast -Wno-error=int-to-pointer-cast
  GCC:*_*_X64_CC_FLAGS     = -Wno-error=pointer-to-int-cast -Wno-error=int-to-pointer-cast
  GCC:*_*_ARM_CC_FLAGS     = -Wno-error=pointer-to-int-cast -Wno-error=int-to-pointer-cast
  GCC:*_*_AARCH64_CC_FLAGS = -Wno-error=pointer-to-int-cast -Wno-error=int-to-pointer-cast
  GCC:*_*_RISCV64_CC_FLAGS = -Wno-error=pointer-to-int-cast -Wno-error=int-to-pointer-cast
  GCC:*_*_LOONGARCH64_CC_FLAGS = -Wno-error=pointer-to-int-cast -Wno-error=int-to-pointer-cast

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
密钥转换算法分析
基于BOOT.EFI逆向分析的完整密钥系统
"""

import hashlib
import struct
from typing import Optional

class KeyTransformationAnalyzer:
    # 从BOOT.EFI中提取的自定义Base32字符集
    BASE32_CHARSET = "0123456789ABCDEFGHJKLMNPQRSTUVWXYZ"
    
    def __init__(self):
        self.charset = self.BASE32_CHARSET
    
    def analyze_display_key_generation(self, hardware_info: bytes) -> str:
        """
        分析显示密钥的生成过程
        基于BOOT.EFI中fcn.000104f0函数的逻辑
        """
        print("=== 显示密钥生成分析 ===")
        
        # 从硬件信息中提取16个字符
        # 这对应BOOT.EFI中0x000107c6-0x000107d4的循环
        display_key = ""
        
        for i in range(16):  # 0x10 = 16
            # 从硬件信息中提取字符
            if i < len(hardware_info):
                char_code = hardware_info[i]
            else:
                char_code = 0
            
            # 验证字符是否在有效字符集中
            if char_code < len(self.charset):
                display_key += self.charset[char_code]
            else:
                # 如果超出范围，使用模运算
                display_key += self.charset[char_code % len(self.charset)]
        
        return display_key
    
    def find_transformation_algorithm(self, display_key: str) -> list:
        """
        寻找从显示密钥到vtkey文件名的转换算法
        """
        print(f"=== 分析显示密钥: {display_key} ===")
        
        possible_vtkeys = []
        
        # 方法1: 直接哈希转换
        algorithms = [
            ('MD5', hashlib.md5),
            ('SHA1', hashlib.sha1),
            ('SHA256', hashlib.sha256),
            ('SHA512', hashlib.sha512),
        ]
        
        for name, hash_func in algorithms:
            # 直接哈希
            hash_result = hash_func(display_key.encode()).hexdigest().upper()
            possible_vtkeys.append(f"{name}_direct: {hash_result[:16]}")
            
            # 哈希后取不同长度
            possible_vtkeys.append(f"{name}_8bytes: {hash_result[:16]}")
            possible_vtkeys.append(f"{name}_16bytes: {hash_result[:32]}")
        
        # 方法2: Base32解码后哈希
        try:
            # 将显示密钥从自定义Base32解码为字节
            decoded_bytes = self.custom_base32_decode(display_key)
            
            for name, hash_func in algorithms:
                hash_result = hash_func(decoded_bytes).hexdigest().upper()
                possible_vtkeys.append(f"{name}_decoded: {hash_result[:16]}")
        except Exception as e:
            print(f"Base32解码失败: {e}")
        
        # 方法3: 字符映射转换
        # 可能存在字符到字符的映射表
        mapped_key = self.character_mapping(display_key)
        possible_vtkeys.append(f"CharMap: {mapped_key}")
        
        # 方法4: 数学运算
        # 将显示密钥转换为数值后进行运算
        try:
            numeric_value = self.key_to_numeric(display_key)
            
            # 各种数学变换
            transformations = [
                ('XOR_0xFF', numeric_value ^ 0xFFFFFFFFFFFFFFFF),
                ('ADD_CONST', numeric_value + 0x1234567890ABCDEF),
                ('MUL_CONST', (numeric_value * 0x9E3779B9) & 0xFFFFFFFFFFFFFFFF),
                ('ROT_LEFT', self.rotate_left(numeric_value, 13)),
                ('ROT_RIGHT', self.rotate_right(numeric_value, 7)),
            ]
            
            for name, result in transformations:
                hex_result = f"{result:016X}"
                possible_vtkeys.append(f"{name}: {hex_result}")
                
        except Exception as e:
            print(f"数学运算失败: {e}")
        
        return possible_vtkeys
    
    def custom_base32_decode(self, key: str) -> bytes:
        """
        使用自定义Base32字符集解码
        """
        value = 0
        charset_len = len(self.charset)
        
        for char in key:
            if char in self.charset:
                value = value * charset_len + self.charset.index(char)
            else:
                raise ValueError(f"无效字符: {char}")
        
        # 转换为字节
        byte_length = (value.bit_length() + 7) // 8
        if byte_length == 0:
            byte_length = 1
        
        return value.to_bytes(byte_length, byteorder='big')
    
    def character_mapping(self, key: str) -> str:
        """
        字符映射转换
        可能存在固定的字符映射表
        """
        # 示例映射表（需要通过逆向分析确定）
        char_map = {
            '0': 'A', '1': 'B', '2': 'C', '3': 'D', '4': 'E',
            '5': 'F', '6': '0', '7': '1', '8': '2', '9': '3',
            'A': '4', 'B': '5', 'C': '6', 'D': '7', 'E': '8',
            'F': '9', 'G': 'G', 'H': 'H', 'J': 'J', 'K': 'K',
            'L': 'L', 'M': 'M', 'N': 'N', 'P': 'P', 'Q': 'Q',
            'R': 'R', 'S': 'S', 'T': 'T', 'U': 'U', 'V': 'V',
            'W': 'W', 'X': 'X', 'Y': 'Y', 'Z': 'Z'
        }
        
        mapped = ""
        for char in key:
            mapped += char_map.get(char, char)
        
        return mapped
    
    def key_to_numeric(self, key: str) -> int:
        """
        将密钥转换为数值
        """
        value = 0
        for char in key:
            if char in self.charset:
                value = value * len(self.charset) + self.charset.index(char)
        return value
    
    def rotate_left(self, value: int, bits: int) -> int:
        """左旋转"""
        bits = bits % 64
        return ((value << bits) | (value >> (64 - bits))) & 0xFFFFFFFFFFFFFFFF
    
    def rotate_right(self, value: int, bits: int) -> int:
        """右旋转"""
        bits = bits % 64
        return ((value >> bits) | (value << (64 - bits))) & 0xFFFFFFFFFFFFFFFF
    
    def reverse_engineer_algorithm(self, display_key: str, expected_vtkey: str) -> str:
        """
        通过已知的显示密钥和vtkey文件名反推算法
        """
        print(f"=== 反推算法 ===")
        print(f"显示密钥: {display_key}")
        print(f"期望vtkey: {expected_vtkey}")
        
        # 生成所有可能的转换
        possible_vtkeys = self.find_transformation_algorithm(display_key)
        
        # 查找匹配的算法
        for vtkey_info in possible_vtkeys:
            if expected_vtkey in vtkey_info:
                print(f"找到匹配算法: {vtkey_info}")
                return vtkey_info.split(':')[0]
        
        print("未找到匹配的算法，可能需要更复杂的转换")
        return None
    
    def generate_vtkey_from_display_key(self, display_key: str, algorithm: str = "auto") -> str:
        """
        根据显示密钥生成vtkey文件名
        """
        if algorithm == "auto":
            # 尝试最可能的算法
            # 基于逆向分析，最可能是MD5或SHA1的某种变体
            hash_result = hashlib.md5(display_key.encode()).hexdigest().upper()
            return hash_result[:16]
        else:
            # 使用指定算法
            return self.apply_algorithm(display_key, algorithm)
    
    def apply_algorithm(self, display_key: str, algorithm: str) -> str:
        """
        应用指定的转换算法
        """
        if algorithm.startswith("MD5"):
            return hashlib.md5(display_key.encode()).hexdigest().upper()[:16]
        elif algorithm.startswith("SHA1"):
            return hashlib.sha1(display_key.encode()).hexdigest().upper()[:16]
        elif algorithm.startswith("SHA256"):
            return hashlib.sha256(display_key.encode()).hexdigest().upper()[:16]
        else:
            # 默认使用MD5
            return hashlib.md5(display_key.encode()).hexdigest().upper()[:16]

def main():
    analyzer = KeyTransformationAnalyzer()
    
    print("=== 密钥转换算法分析器 ===")
    print()
    
    # 使用您的实际测试数据
    display_key = "5F1CE76E1FAF0386"
    
    print("1. 分析可能的转换算法:")
    possible_vtkeys = analyzer.find_transformation_algorithm(display_key)
    
    for vtkey in possible_vtkeys:
        print(f"   {vtkey}")
    
    print()
    print("2. 如果您知道对应的vtkey文件名，请提供以便反推算法")
    print("   例如: analyzer.reverse_engineer_algorithm('5F1CE76E1FAF0386', '实际的vtkey名')")
    
    print()
    print("3. 生成最可能的vtkey文件名:")
    predicted_vtkey = analyzer.generate_vtkey_from_display_key(display_key)
    print(f"   预测的vtkey: {predicted_vtkey}.vtkey")
    
    # 创建预测的vtkey文件
    with open(f"{predicted_vtkey}.vtkey", 'w') as f:
        f.write("")
    print(f"   已创建文件: {predicted_vtkey}.vtkey")

if __name__ == "__main__":
    main()

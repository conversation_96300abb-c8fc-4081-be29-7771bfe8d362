## @file
#  CI configuration for StandaloneMmPkg
#
#  Copyright (c) 2024, Intel Corporation. All rights reserved.<BR>
#  Copyright (c) 2020 - 2021, Arm Limited. All rights reserved.<BR>
#  SPDX-License-Identifier: BSD-2-Clause-Patent
##
{
    "PrEval": {
        "DscPath": "StandaloneMmPkg.dsc",
    },
    "EccCheck": {
        ## Exception sample looks like below:
        ## "ExceptionList": [
        ##     "<ErrorID>", "<KeyWord>"
        ## ]
        "ExceptionList": [
            "4002", "HobConstructor"
        ],
        ## Both file path and directory path are accepted.
        "IgnoreFiles": [
        ]
    },
    ## options defined .pytool/Plugin/CompilerPlugin
    "CompilerPlugin": {
        "DscPath": "StandaloneMmPkg.dsc"
    },

    ## options defined .pytool/Plugin/HostUnitTestCompilerPlugin
    "HostUnitTestCompilerPlugin": {
        "DscPath": "" # Don't support this test
    },

    ## options defined .pytool/Plugin/CharEncodingCheck
    "CharEncodingCheck": {
        "IgnoreFiles": []
    },

    ## options defined .pytool/Plugin/DependencyCheck
    "DependencyCheck": {
        "AcceptableDependencies": [
            "ArmPkg/ArmPkg.dec",
            "EmbeddedPkg/EmbeddedPkg.dec",
            "StandaloneMmPkg/StandaloneMmPkg.dec",
            "MdeModulePkg/MdeModulePkg.dec",
            "MdePkg/MdePkg.dec",
            "UefiCpuPkg/UefiCpuPkg.dec"
        ],
        # For host based unit tests
        "AcceptableDependencies-HOST_APPLICATION":[
            "UnitTestFrameworkPkg/UnitTestFrameworkPkg.dec"
        ],
        # For UEFI shell based apps
        "AcceptableDependencies-UEFI_APPLICATION":[],
        "IgnoreInf": []
    },

    ## options defined .pytool/Plugin/DscCompleteCheck
    "DscCompleteCheck": {
        "IgnoreInf": [],
        "DscPath": "StandaloneMmPkg.dsc"
    },

    ## options defined .pytool/Plugin/HostUnitTestDscCompleteCheck
    "HostUnitTestDscCompleteCheck": {
        "IgnoreInf": [""],
        "DscPath": "" # Don't support this test
    },

    ## options defined .pytool/Plugin/GuidCheck
    "GuidCheck": {
        "IgnoreGuidName": [],
        "IgnoreGuidValue": [],
        "IgnoreFoldersAndFiles": [],
        "IgnoreDuplicates": [],
    },

    ## options defined .pytool/Plugin/LibraryClassCheck
    "LibraryClassCheck": {
        "IgnoreHeaderFile": []
    },

    ## options defined .pytool/Plugin/SpellCheck
    "SpellCheck": {
        "AuditOnly": False,
        "IgnoreFiles": [],           # use gitignore syntax to ignore errors
                                     # in matching files
        "ExtendWords": [
            "Bsymbolic",
            "FwVol",
            "mpidr",
            "mstrict",
            "schedulable",
            "StandaloneMMCore",
        ],           # words to extend to the dictionary for this package
        "IgnoreStandardPaths": [],   # Standard Plugin defined paths that
                                     # should be ignore
        "AdditionalIncludePaths": [] # Additional paths to spell check
                                     # (wildcards supported)
    }
}

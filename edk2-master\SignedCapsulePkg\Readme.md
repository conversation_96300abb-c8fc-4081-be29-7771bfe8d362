# SignedCapsulePkg

This package provides a signed capsule solution in EDKII to support a secure capsule update and recovery solution.

Source Repository: https://github.com/tianocore/edk2/tree/master/SignedCapsulePkg

A whitepaper to describe the capsule design: https://github.com/tianocore-docs/Docs/raw/master/White_Papers/A_Tour_Beyond_BIOS_Capsule_Update_and_Recovery_in_EDK_II.pdf

Wiki pages to provides more detail on how to enable: https://github.com/tianocore/tianocore.github.io/wiki/Capsule-Based-Firmware-Update-and-Firmware-Recovery



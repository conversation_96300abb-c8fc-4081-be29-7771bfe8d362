## @file
#  Debug Communication Library instance based on serial port.
#
#  Copyright (c) 2010 - 2014, Intel Corporation. All rights reserved.<BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = DebugCommunicationLibSerialPort
  MODULE_UNI_FILE                = DebugCommunicationLibSerialPort.uni
  FILE_GUID                      = 8CC435C5-6330-4269-B0C3-E3BD05C86FB8
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 0.7
  LIBRARY_CLASS                  = DebugCommunicationLib

#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources.common]
  DebugCommunicationLibSerialPort.c

[Packages]
  MdePkg/MdePkg.dec
  SourceLevelDebugPkg/SourceLevelDebugPkg.dec

[LibraryClasses]
  SerialPortLib
  DebugLib

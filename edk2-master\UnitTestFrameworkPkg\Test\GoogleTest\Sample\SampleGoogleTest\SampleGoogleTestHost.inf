## @file
# This is a sample to demonstrates the use of GoogleTest that supports host
# execution environments.
#
# Copyright (c) 2022, Intel Corporation. All rights reserved.<BR>
# SPDX-License-Identifier: BSD-2-Clause-Patent
##

[Defines]
  INF_VERSION    = 0x00010005
  BASE_NAME      = SampleGoogleTestHost
  FILE_GUID      = 7D8BBFBB-7977-4AEE-A59F-257BF5C2F87C
  MODULE_TYPE    = HOST_APPLICATION
  VERSION_STRING = 1.0

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources]
  SampleGoogleTest.cpp

[Packages]
  MdePkg/MdePkg.dec
  UnitTestFrameworkPkg/UnitTestFrameworkPkg.dec

[LibraryClasses]
  GoogleTestLib
  BaseLib
  DebugLib

[Pcd]
  gEfiMdePkgTokenSpaceGuid.PcdDebugPropertyMask

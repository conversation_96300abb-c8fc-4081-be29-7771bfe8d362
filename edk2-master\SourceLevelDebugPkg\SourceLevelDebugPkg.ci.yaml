## @file
# Core CI configuration for SourceLevelDebugPkg
#
# Copyright (c) Microsoft Corporation
#
# SPDX-License-Identifier: BSD-2-Clause-Patent
##
{
    "PrEval": {
        "DscPath": "SourceLevelDebugPkg.dsc",
    },
    ## options defined .pytool/Plugin/LicenseCheck
    "LicenseCheck": {
        "IgnoreFiles": []
    },

    "EccCheck": {
        ## Exception sample looks like below:
        ## "ExceptionList": [
        ##     "<ErrorID>", "<KeyWord>"
        ## ]
        "ExceptionList": [
        ],
        ## Both file path and directory path are accepted.
        "IgnoreFiles": []
    },

    ## options defined .pytool/Plugin/CompilerPlugin
    "CompilerPlugin": {
        "DscPath": "SourceLevelDebugPkg.dsc"
    },

    ## options defined .pytool/Plugin/HostUnitTestCompilerPlugin
    "HostUnitTestCompilerPlugin": {
        "DscPath": "" # Don't support this test
    },

    ## options defined .pytool/Plugin/CharEncodingCheck
    "CharEncodingCheck": {
        "IgnoreFiles": []
    },

    ## options defined .pytool/Plugin/DependencyCheck
    "DependencyCheck": {
        "AcceptableDependencies": [
          "MdeModulePkg/MdeModulePkg.dec",
          "MdePkg/MdePkg.dec",
          "SecurityPkg/SecurityPkg.dec",
          "SourceLevelDebugPkg/SourceLevelDebugPkg.dec",
          "UefiCpuPkg/UefiCpuPkg.dec"
        ],
        # For host based unit tests
        "AcceptableDependencies-HOST_APPLICATION":[
          "UnitTestFrameworkPkg/UnitTestFrameworkPkg.dec"
        ],
        # For UEFI shell based apps
        "AcceptableDependencies-UEFI_APPLICATION":[],
        "IgnoreInf": []
    },

    ## options defined .pytool/Plugin/DscCompleteCheck
    "DscCompleteCheck": {
        "IgnoreInf": [""],
        "DscPath": "SourceLevelDebugPkg.dsc"
    },

    ## options defined .pytool/Plugin/HostUnitTestDscCompleteCheck
    "HostUnitTestDscCompleteCheck": {
        "IgnoreInf": [""],
        "DscPath": "" # Don't support this test
    },

    ## options defined .pytool/Plugin/GuidCheck
    "GuidCheck": {
        "IgnoreGuidName": [],
        "IgnoreGuidValue": [],
        "IgnoreFoldersAndFiles": [],
        "IgnoreDuplicates": [],
    },

    ## options defined .pytool/Plugin/LibraryClassCheck
    "LibraryClassCheck": {
        "IgnoreHeaderFile": []
    },

    ## options defined .pytool/Plugin/SpellCheck
    "SpellCheck": {
        "AuditOnly": False,          # All failures were addressed when SpellCheck was enabled in this package
        "IgnoreFiles": [],           # use gitignore syntax to ignore errors in matching files
        "ExtendWords": [             # words to extend to the dictionary for this package
          "bidir",
          "bsp's",
          "capbility",               # comes from external package
          "dcddi",
          "dcerstba",
          "dcportsc",
          "dcerstsz",
          "epring",
          "evalu",
          "fxrestor",
          "hccparams",
          "hcsparams",
          "iretd",
          "iretq",
          "isoch",
          "mfindex",
          "ompressed",
          "portsc",
          "sequenceno",
          "smmentrybreak",
          "stosd",
          "stosq",
          "ttach",
          "urb's",
          "xhc's"
        ],
        "IgnoreStandardPaths": [],   # Standard Plugin defined paths that should be ignore
        "AdditionalIncludePaths": [] # Additional paths to spell check (wildcards supported)
    }
}

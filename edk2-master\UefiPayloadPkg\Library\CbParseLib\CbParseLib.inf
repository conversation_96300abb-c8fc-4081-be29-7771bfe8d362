## @file
#  Coreboot Table Parse Library.
#
#  Copyright (c) 2014 - 2021, Intel Corporation. All rights reserved.<BR>
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = CbParseLib
  FILE_GUID                      = 49EDFC9E-5945-4386-9C0B-C9B60CD45BB1
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = BlParseLib

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources]
  CbParseLib.c

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  UefiPayloadPkg/UefiPayloadPkg.dec

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  IoLib
  DebugLib
  PcdLib

[Pcd]
  gUefiPayloadPkgTokenSpaceGuid.PcdBootloaderParameter

## @file
#  Library instance of PciHostBridgeLib library class for coreboot.
#
#  Copyright (C) 2016, Red Hat, Inc.
#  Copyright (c) 2016 - 2021, Intel Corporation. All rights reserved.<BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = PciHostBridgeLib
  FILE_GUID                      = 62EE5269-CFFD-43a3-BE3F-622FC79F467E
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = PciHostBridgeLib

#
# The following information is for reference only and not required by the build
# tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 EBC
#

[Sources]
  PciHostBridge.h
  PciHostBridgeLib.c
  PciHostBridgeSupport.c

[Packages]
  MdeModulePkg/MdeModulePkg.dec
  MdePkg/MdePkg.dec

[LibraryClasses]
  BaseMemoryLib
  DebugLib
  DevicePathLib
  MemoryAllocationLib
  PciLib

[Guids]
  gUniversalPayloadPciRootBridgeInfoGuid

[Pcd]
  gEfiMdeModulePkgTokenSpaceGuid.PcdPciDisableBusEnumeration

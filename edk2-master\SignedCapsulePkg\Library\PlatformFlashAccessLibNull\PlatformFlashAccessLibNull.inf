## @file
#  Platform flash device access library.
#
#  Platform flash device access library NULL instance.
#
#  Copyright (c) 2016 - 2018, Intel Corporation. All rights reserved.<BR>
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = PlatformFlashAccessLibNull
  MODULE_UNI_FILE                = PlatformFlashAccessLibNull.uni
  FILE_GUID                      = A0534D92-9776-4E4E-9234-C9DC1849DBB5
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = PlatformFlashAccessLib

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 EBC
#

[Sources]
  PlatformFlashAccessLibNull.c

[Packages]
  MdePkg/MdePkg.dec
  SignedCapsulePkg/SignedCapsulePkg.dec

[LibraryClasses]
  BaseMemoryLib

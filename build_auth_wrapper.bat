@echo off
REM Auth Wrapper 构建脚本
REM 此脚本用于编译Auth Wrapper验证系统

echo ========================================
echo Auth Wrapper 构建脚本
echo ========================================

REM 检查EDK2环境
if not exist "edk2-master" (
    echo 错误: 找不到edk2-master目录
    pause
    exit /b 1
)

REM 进入EDK2目录
cd edk2-master

REM 设置EDK2环境
echo 正在设置EDK2环境...
call edksetup.bat

REM 检查环境设置是否成功
if errorlevel 1 (
    echo 错误: EDK2环境设置失败
    pause
    exit /b 1
)

REM 构建BaseTools（如果需要）
echo 正在检查BaseTools...
if not exist "BaseTools\Bin\Win32\build.exe" (
    echo 正在构建BaseTools...
    cd BaseTools
    call toolsetup.bat
    cd ..
)

REM 设置目标架构和工具链
set TARGET_ARCH=X64
set TOOL_CHAIN_TAG=VS2019
set TARGET=DEBUG

REM 检查Visual Studio环境
echo 正在检查Visual Studio环境...
where cl.exe >nul 2>&1
if errorlevel 1 (
    echo 正在设置Visual Studio环境...
    call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat" 2>nul
    if errorlevel 1 (
        call "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" 2>nul
        if errorlevel 1 (
            echo 警告: 未找到Visual Studio环境，尝试使用GCC
            set TOOL_CHAIN_TAG=GCC5
        ) else (
            set TOOL_CHAIN_TAG=VS2022
        )
    )
)

echo 构建配置:
echo   目标架构: %TARGET_ARCH%
echo   工具链: %TOOL_CHAIN_TAG%
echo   构建类型: %TARGET%

REM 开始构建
echo.
echo 正在构建Auth Wrapper...
build -p AuthWrapperPkg\AuthWrapperPkg.dsc -a %TARGET_ARCH% -t %TOOL_CHAIN_TAG% -b %TARGET%

REM 检查构建结果
if errorlevel 1 (
    echo.
    echo ========================================
    echo 构建失败！
    echo ========================================
    echo.
    echo 可能的解决方案:
    echo 1. 检查Visual Studio是否正确安装
    echo 2. 检查EDK2环境是否正确设置
    echo 3. 检查源代码是否有语法错误
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo 构建成功！
    echo ========================================
    echo.
    
    REM 查找生成的EFI文件
    for /r "Build\AuthWrapperPkg" %%f in (AuthWrapper.efi) do (
        echo 生成的文件: %%f
        
        REM 复制到根目录
        copy "%%f" "..\AuthWrapper.efi" >nul 2>&1
        if not errorlevel 1 (
            echo 已复制到: ..\AuthWrapper.efi
        )
    )
    
    echo.
    echo 下一步:
    echo 1. 将AuthWrapper.efi重命名为boot.efi
    echo 2. 将原始boot.efi重命名为1boot.efi
    echo 3. 使用auth_key_generator.py生成密钥文件
    echo 4. 将密钥文件放置到C盘根目录
    echo.
)

cd ..
pause

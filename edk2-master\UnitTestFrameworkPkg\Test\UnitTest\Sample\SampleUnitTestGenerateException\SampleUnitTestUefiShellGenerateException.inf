## @file
# Sample UnitTest built for execution in UEFI Shell.
# This test case generates an exception. For some host-based environments, this
# is a fatal condition that terminates the unit tests and no additional test
# cases are executed. On other environments, this condition may be report a unit
# test failure and continue with additional unit tests.
#
# Copyright (c) 2024, Intel Corporation. All rights reserved.<BR>
# SPDX-License-Identifier: BSD-2-Clause-Patent
##

[Defines]
  INF_VERSION    = 0x00010006
  BASE_NAME      = SampleUnitTestUefiShellGenerateException
  FILE_GUID      = E854F900-6B7A-448D-8689-736EB96875BF
  MODULE_TYPE    = UEFI_APPLICATION
  VERSION_STRING = 1.0
  ENTRY_POINT    = DxeEntryPoint

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources]
  SampleUnitTestGenerateException.c

[Packages]
  MdePkg/MdePkg.dec

[LibraryClasses]
  UefiApplicationEntryPoint
  BaseLib
  DebugLib
  UnitTestLib
  PrintLib

[Pcd]
  gEfiMdePkgTokenSpaceGuid.PcdDebugPropertyMask

## @file  SourceLevelDebugPkg.dec
# This package provides target side modules to support source level debug.
# The target side components includes the Debug Agent Library instance
# to communicate with host side modules, Debug Communication Library and
# instances to provide the communication I/O functions between Debug Agent
# and host, PeCoffExtraActionLib instance to report symbol path information,
# etc.
#
# Copyright (c) 2010 - 2018, Intel Corporation. All rights reserved.<BR>
# SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  DEC_SPECIFICATION              = 0x00010005
  PACKAGE_NAME                   = SourceLevelDebugPkg
  PACKAGE_UNI_FILE               = SourceLevelDebugPkg.uni
  PACKAGE_GUID                   = DBF00C27-D8D7-443d-918B-4E85CDA1373B
  PACKAGE_VERSION                = 0.96

[Includes]
  Include

[Includes.IA32]
  Include/Ia32

[Includes.X64]
  Include/Ia32

[LibraryClasses]
  ##  @libraryclass  Provides communication I/O functions between Debug Agent and HOST.
  ##
  DebugCommunicationLib|Include/Library/DebugCommunicationLib.h

[Guids]
  ## MdeModule package token space guid
  # Include/Guid/DebugAgentGuid.h
  gEfiDebugAgentGuid       = {0x865a5a9b, 0xb85d, 0x474c, { 0x84, 0x55, 0x65, 0xd1, 0xbe, 0x84, 0x4b, 0xe2 }}
  gEfiSourceLevelDebugPkgTokenSpaceGuid = {0x865a5aab, 0xb85d, 0x474c, { 0x84, 0x55, 0x65, 0xd1, 0xbe, 0x84, 0x4b, 0xe2 }}

#
# [Error.gEfiSourceLevelDebugPkgTokenSpaceGuid]
#   0x80000001 | Invalid value provided.
#

[PcdsFixedAtBuild, PcdsPatchableInModule]
  ## The memory BAR of usb debug port, it may be different with the memory bar of ehci host controller.
  #  Note that the memory BAR address is only used before Pci bus resource allocation.
  # @Prompt Configure usb debug port memory BAR.
  gEfiSourceLevelDebugPkgTokenSpaceGuid.PcdUsbDebugPortMemorySpaceBase|0xd0000000|UINT32|0x00000001

  ## The memory BAR of ehci host controller, in which usb debug feature is enabled.
  #  Note that the memory BAR address is only used before Pci bus resource allocation.
  # @Prompt Configure ehci host controller memory BAR.
  gEfiSourceLevelDebugPkgTokenSpaceGuid.PcdUsbEhciMemorySpaceBase|0xd0000000|UINT32|0x00000002

  ## The pci address of ehci host controller, in which usb debug feature is enabled.
  #  The format of pci address is :<BR>
  #      -----------------------------------------------------------------------<BR>
  #      | Bits 28..31 | Bits 20..27 | Bits 15..19 | Bits 12..14 | Bits 00..11 |<BR>
  #      -----------------------------------------------------------------------<BR>
  #      |      0      |     Bus     |   Device    |   Function  |      0      |<BR>
  #      -----------------------------------------------------------------------<BR>
  #  For the value 0x000EF000, it means the pci address at bus 0x0, device 0x1D, function 0x7.
  # @Prompt Configure ehci host controller pci address.
  # @Expression  0x80000001 | (gEfiSourceLevelDebugPkgTokenSpaceGuid.PcdUsbEhciPciAddress & 0xF0000FFF) == 0
  gEfiSourceLevelDebugPkgTokenSpaceGuid.PcdUsbEhciPciAddress|0x000EF000|UINT32|0x00000003

  ## The mask of exception numbers whose handlers would be ignored and cannot be replaced or
  #  hooked by Debug Agent Library. Masking INT1/INT3 is invalid.
  # @Prompt Configure exception numbers not to be hooked by Debug Agent.
  # @Expression  0x80000001 | (gEfiSourceLevelDebugPkgTokenSpaceGuid.PcdExceptionsIgnoredByDebugger & 0xA) == 0
  gEfiSourceLevelDebugPkgTokenSpaceGuid.PcdExceptionsIgnoredByDebugger|0x00000000|UINT32|0x00000004

  ## The method to issue break point to Debug Agent Library when Loading/UnLoading image.<BR><BR>
  #  1: Use I/O Port 84 to issue hardware break point<BR>
  #  2: Use INT3 to issue software break point<BR>
  # @Prompt Configure Loading/UnLoading image break method.
  # @ValidRange  0x80000001 | 1 - 2
  gEfiSourceLevelDebugPkgTokenSpaceGuid.PcdDebugLoadImageMethod|0x1|UINT8|0x00000005

  ## The data buffer size used by debug port in debug communication library instances.
  #  Its value is not suggested to be changed in platform DSC file.
  # @Prompt Assign debug port buffer size.
  gEfiSourceLevelDebugPkgTokenSpaceGuid.PcdDebugPortHandleBufferSize|0x0|UINT16|0x00000006

  ## The memory BAR of xhci host controller, in which usb debug feature is enabled.
  ## Note that the memory BAR address is only used before Pci bus resource allocation.
  # @Prompt Configure xhci host controller memory BAR.
  gEfiSourceLevelDebugPkgTokenSpaceGuid.PcdUsbXhciMemorySpaceBase|0xD0000000|UINT64|0x00000007

  ## The pci address of xhci host controller, in which usb debug feature is enabled.
  #  The format of pci address is :<BR>
  #      -----------------------------------------------------------------------<BR>
  #      | Bits 28..31 | Bits 20..27 | Bits 15..19 | Bits 12..14 | Bits 00..11 |<BR>
  #      -----------------------------------------------------------------------<BR>
  #      |      0      |     Bus     |   Device    |   Function  |      0      |<BR>
  #      -----------------------------------------------------------------------<BR>
  #  For the value 0x000A0000, it means the pci address at bus 0x0, device 0x14, function 0x0.
  # @Prompt Configure xhci host controller pci address.
  # @Expression  0x80000001 | (gEfiSourceLevelDebugPkgTokenSpaceGuid.PcdUsbXhciPciAddress & 0xF0000FFF) == 0
  gEfiSourceLevelDebugPkgTokenSpaceGuid.PcdUsbXhciPciAddress|0x000A0000|UINT32|0x00000008

  ## Per XHCI spec, software shall impose a timeout between the detection of the Debug Host
  ## connection and the DbC Run transition to 1. This PCD specifies the timeout value in microsecond.
  # @Prompt Configure debug device detection timeout value.
  gEfiSourceLevelDebugPkgTokenSpaceGuid.PcdUsbXhciDebugDetectTimeout|3000000|UINT64|0x00000009

  ## Default revision of the debug agent transfer protocol.
  #  The upper 16 bits indicate the major revision and the lower 16 bits indicate the minor revision.
  #  For example, a value of 0x00000004 stands for revision 0.4.
  #  Debug packet compression and decompression is supported since revision 0.4.
  # @Prompt Default revision of the debug agent transfer protocol.
  gEfiSourceLevelDebugPkgTokenSpaceGuid.PcdTransferProtocolRevision|0x00000004|UINT32|0x0000000a

[UserExtensions.TianoCore."ExtraFiles"]
  SourceLevelDebugPkgExtra.uni

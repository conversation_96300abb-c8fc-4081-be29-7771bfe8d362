## @file
#
#  Copyright (c) 2015, Intel Corporation. All rights reserved.<BR>
#  Copyright (c) 2016 - 2018, ARM Limited. All rights reserved.<BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

################################################################################
#
# Defines Section - statements that will be processed to create a Makefile.
#
################################################################################
[Defines]
  INF_VERSION                    = 0x0001001A
  BASE_NAME                      = FvLib
  FILE_GUID                      = C20085E9-E3AB-4938-A727-C10935FEEE2B
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = FvLib

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32
#

################################################################################
#
# Sources Section - list of files that are required for the build to succeed.
#
################################################################################

[Sources]
  FvLib.c

################################################################################
#
# Package Dependency Section - list of Package files that are required for
#                              this module.
#
################################################################################

[Packages]
  MdePkg/MdePkg.dec
  StandaloneMmPkg/StandaloneMmPkg.dec

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  DebugLib

## @file
#  CPU Exception Handler library instance for SEC/PEI modules.
#
#  Copyright (c) 2012 - 2022, Intel Corporation. All rights reserved.<BR>
#  Copyright (c) 2024, Loongson Technology Corporation Limited. All rights reserved.<BR>
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = SecPeiCpuExceptionHandlerLib
  MODULE_UNI_FILE                = SecPeiCpuExceptionHandlerLib.uni
  FILE_GUID                      = CA4BBC99-DFC6-4234-B553-8B6586B7B113
  MODULE_TYPE                    = PEIM
  VERSION_STRING                 = 1.1
  LIBRARY_CLASS                  = CpuExceptionHandlerLib|SEC PEI_CORE PEIM

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#  VALID_ARCHITECTURES           = IA32 X64 LOONGARCH64
#

[Sources.Ia32]
  Ia32/ArchExceptionHandler.c
  Ia32/ArchInterruptDefs.h
  Ia32/ExceptionHandlerAsm.nasm
  Ia32/ExceptionTssEntryAsm.nasm

[Sources.X64]
  X64/ArchExceptionHandler.c
  X64/ArchInterruptDefs.h
  X64/SecPeiExceptionHandlerAsm.nasm

[Sources.Ia32, Sources.X64]
  CpuExceptionCommon.h
  CpuExceptionCommon.c
  SecPeiCpuException.c

[Sources.LoongArch64]
  LoongArch/ExceptionCommon.h
  LoongArch/ExceptionCommon.c
  LoongArch/SecPeiExceptionLib.c
  LoongArch/LoongArch64/ArchExceptionHandler.c
  LoongArch/LoongArch64/ExceptionHandlerAsm.S | GCC


[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  UefiCpuPkg/UefiCpuPkg.dec

[LibraryClasses.common]
  BaseLib
  PeCoffGetEntryPointLib
  PrintLib
  SerialPortLib

[LibraryClasses.Ia32, LibraryClasses.X64]
  CcExitLib
  LocalApicLib

[LibraryClasses.LoongArch64]
  CpuLib

[Pcd]
  gEfiMdeModulePkgTokenSpaceGuid.PcdCpuStackGuard
  gUefiCpuPkgTokenSpaceGuid.PcdCpuStackSwitchExceptionList
  gUefiCpuPkgTokenSpaceGuid.PcdCpuKnownGoodStackSize

[FeaturePcd]
  gUefiCpuPkgTokenSpaceGuid.PcdCpuSmmStackGuard                    ## CONSUMES

[BuildOptions]
  XCODE:*_*_X64_NASM_FLAGS = -D NO_ABSOLUTE_RELOCS_IN_TEXT

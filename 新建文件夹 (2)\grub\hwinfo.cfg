menuentry "$VTLANG_HWINFO" --class=debug_hwinfo --class=F5tool {    
    smbios -t 4 -s 0x10 --set=cpu_brand
    
    set system_vendor="-";
    smbios -t 1 -s 0x04 --set=system_vendor;
    set system_product="-";
    smbios -t 1 -s 0x05 --set=system_product;
    set system_version="-";
    smbios -t 1 -s 0x06 --set=system_version;

    set board_vendor="-";
    smbios -t 2 -s 0x04 --set=board_vendor;
    set board_product="-";
    smbios -t 2 -s 0x05 --set=board_product;
    set board_version="-";
    smbios -t 2 -s 0x06 --set=board_version;

    set bios_vendor="-";
    smbios -t 0 -s 0x04 --set=bios_vendor;
    set bios_version="-";
    smbios -t 0 -s 0x05 --set=bios_ver;
    set bios_date="-";
    smbios -t 0 -s 0x08 --set=bios_date;
    set bios_size="-";
    smbios -t 0 -b 0x09 --set=bios_size;
    
    
    echo "Platform                $grub_cpu-$grub_platform"    
    if [ "$grub_platform" != "pc" ]; then
        echo "UEFI Version            $grub_uefi_version"
        if vt_check_secureboot_var; then
            echo "Secure Boot             Enabled"
        else
            echo "Secure Boot             Disabled"
        fi
    fi
    
    echo ""    
    echo "CPU Model               $cpu_brand"
    echo "Physical RAM            $grub_total_ram MB"
                                  
    echo ""                       
    echo "Manufacture             $system_vendor"
    echo "Product Name            $system_product"
    echo "Version                 $system_version"
                                  
    echo ""                       
    echo "Board Manufacture       $board_vendor"
    echo "Board Name              $board_product"
    echo "Board Version           $board_version"
                                  
    echo ""                       
    echo "BIOS Manufacture        $bios_vendor"
    echo "BIOS Version            $bios_ver"
    echo "BIOS Date               $bios_date"
    echo "BIOS ROM Size           $bios_size"
    
    
    echo -en "\n\n\n$VTLANG_ENTER_EXIT ..."
    read vtInputKey
}

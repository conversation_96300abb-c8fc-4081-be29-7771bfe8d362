##  @file
# Provides shell network2 functions
#
# Copyright (c) 2016 - 2019, Intel Corporation. All rights reserved. <BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#
##

[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = UefiShellNetwork2CommandsLib
  FILE_GUID                      = D94E3B82-908E-46bf-A7B9-C7B7F17B1B7D
  MODULE_TYPE                    = UEFI_APPLICATION
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = NULL|UEFI_APPLICATION UEFI_DRIVER
  CONSTRUCTOR                    = ShellNetwork2CommandsLibConstructor
  DESTRUCTOR                     = ShellNetwork2CommandsLibDestructor

[Sources.common]
  UefiShellNetwork2CommandsLib.uni
  UefiShellNetwork2CommandsLib.c
  UefiShellNetwork2CommandsLib.h
  Ping6.c
  Ifconfig6.c

[Packages]
  MdePkg/MdePkg.dec
  ShellPkg/ShellPkg.dec
  MdeModulePkg/MdeModulePkg.dec
  NetworkPkg/NetworkPkg.dec

[LibraryClasses]
  MemoryAllocationLib
  BaseLib
  BaseMemoryLib
  DebugLib
  ShellCommandLib
  ShellLib
  UefiLib
  UefiRuntimeServicesTableLib
  UefiBootServicesTableLib
  PcdLib
  HiiLib
  FileHandleLib
  NetLib

[Pcd]
  gEfiShellPkgTokenSpaceGuid.PcdShellProfileMask ## CONSUMES

[Protocols]
  gEfiCpuArchProtocolGuid                       ## CONSUMES
  gEfiTimerArchProtocolGuid                     ## CONSUMES
  gEfiIp6ProtocolGuid                           ## SOMETIMES_CONSUMES
  gEfiIp6ServiceBindingProtocolGuid             ## SOMETIMES_CONSUMES
  gEfiIp6ConfigProtocolGuid                     ## SOMETIMES_CONSUMES

[Guids]
  gShellNetwork2HiiGuid                         ## SOMETIMES_CONSUMES ## HII

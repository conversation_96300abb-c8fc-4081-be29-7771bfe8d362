##  @file
#  Provides shell level 1 functions
#
#  Copyright (c) 2009-2015, Intel Corporation. All rights reserved. <BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#
##
[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = UefiShellLevel1CommandsLib
  FILE_GUID                      = 50cb6037-1102-47af-b2dd-9944b6eb1abe
  MODULE_TYPE                    = UEFI_APPLICATION
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = NULL|UEFI_APPLICATION UEFI_DRIVER
  CONSTRUCTOR                    = ShellLevel1CommandsLibConstructor
  DESTRUCTOR                     = ShellLevel1CommandsLibDestructor

[Sources.common]
  UefiShellLevel1CommandsLib.c
  UefiShellLevel1CommandsLib.h
  UefiShellLevel1CommandsLib.uni
  Exit.c
  Goto.c
  If.c
  For.c
  Shift.c
  Stall.c

[Packages]
  MdePkg/MdePkg.dec
  ShellPkg/ShellPkg.dec
  MdeModulePkg/MdeModulePkg.dec

[LibraryClasses]
  MemoryAllocationLib
  BaseLib
  BaseMemoryLib
  DebugLib
  ShellCommandLib
  ShellLib
  UefiLib
  UefiRuntimeServicesTableLib
  UefiBootServicesTableLib
  SortLib
  PrintLib

[Pcd.common]
  gEfiShellPkgTokenSpaceGuid.PcdShellSupportLevel ## CONSUMES

[Guids]
  gShellLevel1HiiGuid       ## SOMETIMES_CONSUMES ## HII

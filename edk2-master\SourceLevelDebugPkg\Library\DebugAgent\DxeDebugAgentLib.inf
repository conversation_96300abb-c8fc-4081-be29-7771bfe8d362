## @file
#  Debug Agent library instance for Dxe Core and Dxe modules.
#
#  Copyright (c) 2010 - 2018, Intel Corporation. All rights reserved.<BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = DxeDebugAgentLib
  MODULE_UNI_FILE                = DxeDebugAgentLib.uni
  FILE_GUID                      = BA6BAD25-B814-4747-B0B0-0FBB61D40B90
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 0.8
  LIBRARY_CLASS                  = DebugAgentLib|DXE_CORE DXE_DRIVER

  CONSTRUCTOR                    = DxeDebugAgentLibConstructor

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources.common]
  DxeDebugAgent/DxeDebugAgentLib.c
  DxeDebugAgent/DxeDebugAgentLib.h
  DxeDebugAgent/SerialIo.c
  DebugAgentCommon/DebugAgent.c
  DebugAgentCommon/DebugAgent.h
  DebugAgentCommon/DebugTimer.c
  DebugAgentCommon/DebugTimer.h
  DebugAgentCommon/DebugMp.c
  DebugAgentCommon/DebugMp.h

[Sources.Ia32]
  DebugAgentCommon/Ia32/AsmFuncs.nasm
  DebugAgentCommon/Ia32/ArchDebugSupport.h
  DebugAgentCommon/Ia32/ArchDebugSupport.c
  DebugAgentCommon/Ia32/DebugException.h

[Sources.X64]
  DebugAgentCommon/X64/AsmFuncs.nasm
  DebugAgentCommon/X64/ArchDebugSupport.h
  DebugAgentCommon/X64/ArchDebugSupport.c
  DebugAgentCommon/X64/DebugException.h

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  UefiCpuPkg/UefiCpuPkg.dec
  SourceLevelDebugPkg/SourceLevelDebugPkg.dec

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  ResetSystemLib
  IoLib
  HobLib
  DebugCommunicationLib
  UefiBootServicesTableLib
  UefiLib
  PcdLib
  SynchronizationLib
  MemoryAllocationLib
  LocalApicLib
  TimerLib
  PrintLib
  PeCoffGetEntryPointLib
  PeCoffExtraActionLib
  MemoryAllocationLib

[Guids]
  ## PRODUCES ## SystemTable
  ## CONSUMES ## HOB
  gEfiDebugAgentGuid
  ## SOMETIMES_CONSUMES ## SystemTable
  ## SOMETIMES_PRODUCES ## SystemTable
  gEfiVectorHandoffTableGuid

[Ppis]
  gEfiVectorHandoffInfoPpiGuid                  ## UNDEFINED

[Protocols]
  gEfiSerialIoProtocolGuid                      ## SOMETIMES_PRODUCES
  gEfiDevicePathProtocolGuid                    ## SOMETIMES_PRODUCES

[Pcd]
  gEfiMdePkgTokenSpaceGuid.PcdFSBClock                                  ## SOMETIMES_CONSUMES
  gEfiSourceLevelDebugPkgTokenSpaceGuid.PcdExceptionsIgnoredByDebugger  ## SOMETIMES_CONSUMES
  gEfiSourceLevelDebugPkgTokenSpaceGuid.PcdDebugPortHandleBufferSize    ## CONSUMES
  gEfiSourceLevelDebugPkgTokenSpaceGuid.PcdTransferProtocolRevision     ## CONSUMES


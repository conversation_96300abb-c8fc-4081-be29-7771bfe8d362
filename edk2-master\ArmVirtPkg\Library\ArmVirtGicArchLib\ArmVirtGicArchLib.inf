#/** @file
#
#  Component description file for ArmVirtGicArchLib module
#
#  Copyright (c) 2015, Linaro Ltd. All rights reserved.<BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#**/

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = ArmVirtGicArchLib
  FILE_GUID                      = 87b0dc84-4661-4deb-a789-97977ff636ed
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = NULL|DXE_DRIVER UEFI_DRIVER UEFI_APPLICATION
  CONSTRUCTOR                    = ArmVirtGicArchLibConstructor

[Sources]
  ArmVirtGicArchLib.c

[LibraryClasses]
  BaseLib
  DebugLib
  PcdLib
  UefiBootServicesTableLib

[Packages]
  ArmPkg/ArmPkg.dec
  ArmVirtPkg/ArmVirtPkg.dec
  EmbeddedPkg/EmbeddedPkg.dec
  MdePkg/MdePkg.dec

[Protocols]
  gFdtClientProtocolGuid                                ## CONSUMES

[Pcd]
  gArmTokenSpaceGuid.PcdGicDistributorBase
  gArmTokenSpaceGuid.PcdGicRedistributorsBase
  gArmTokenSpaceGuid.PcdGicInterruptInterfaceBase

[Depex]
  gFdtClientProtocolGuid

## @file
# UEFI Boot Services Table Library implementation.
#
# Copyright (c) 2021, Intel Corporation. All rights reserved.<BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = UefiPayloadInitHobLib
  FILE_GUID                      = ff5c7a21-ab7a-4366-8616-11c6e53247b6
  MODULE_TYPE                    = UEFI_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = DxeHobListLib|DXE_DRIVER  DXE_RUNTIME_DRIVER DXE_SMM_DRIVER UEFI_APPLICATION UEFI_DRIVER SMM_CORE

  CONSTRUCTOR                    = DxeHobListLibConstructor

#
#  VALID_ARCHITECTURES           = IA32 X64 EBC
#

[Sources]
  DxeHobListLib.c

[Packages]
  MdePkg/MdePkg.dec
  UefiPayloadPkg/UefiPayloadPkg.dec

[Guids]
  gEfiHobListGuid                               ## CONSUMES



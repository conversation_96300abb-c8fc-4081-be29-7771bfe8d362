## @file
# This package provides EDKII capsule related support.
#
# Copyright (c) 2016, Intel Corporation. All rights reserved.<BR>
# SPDX-License-Identifier: BSD-2-Clause-Patent
#
##


[Defines]
  DEC_SPECIFICATION              = 0x00010005
  PACKAGE_NAME                   = SignedCapsulePkg
  PACKAGE_GUID                   = 75AA5D82-7BC4-44A9-82FB-0820EBC79BED
  PACKAGE_VERSION                = 0.1

[Includes]
  Include

[LibraryClasses]
  ## @libraryclass  Provides services for EDKII system FMP capsule.
  #
  EdkiiSystemCapsuleLib|Include/Library/EdkiiSystemCapsuleLib.h

  ## @libraryclass  Provides services to parse the INI configuration file.
  #
  IniParsingLib|Include/Library/IniParsingLib.h

  ## @libraryclass  Provides services to access flash device.
  #
  PlatformFlashAccessLib|Include/Library/PlatformFlashAccessLib.h

[Guids]
  gEfiSignedCapsulePkgTokenSpaceGuid      = { 0xe1eb612f, 0x1c6c, 0x485d, { 0x9d, 0x6, 0x65, 0x8, 0x44, 0x88, 0x15, 0x69 }}

  ## Include/Guid/EdkiiSystemFmpCapsule.h
  gEdkiiSystemFirmwareImageDescriptorFileGuid = {0x90b2b846, 0xca6d, 0x4d6e, {0xa8, 0xd3, 0xc1, 0x40, 0xa8, 0xe1, 0x10, 0xac}}
  gEdkiiSystemFmpCapsuleConfigFileGuid        = {0x812136d3, 0x4d3a, 0x433a, {0x94, 0x18, 0x29, 0xbb, 0x9b, 0xf7, 0x8f, 0x6e}}
  gEdkiiSystemFmpCapsuleDriverFvFileGuid      = {0xce57b167, 0xb0e4, 0x41e8, {0xa8, 0x97, 0x5f, 0x4f, 0xeb, 0x78, 0x1d, 0x40}}

[PcdsFixedAtBuild, PcdsPatchableInModule, PcdsDynamic, PcdsDynamicEx]
  ## This is the GUID of the FFS which contains the Rsa2048Sha256TestPublicKeyFile as a RAW section.
  # @Prompt GUID of the FFS which contains the Rsa2048Sha256TestPublicKeyFile.
  gEfiSignedCapsulePkgTokenSpaceGuid.PcdEdkiiRsa2048Sha256TestPublicKeyFileGuid|{0x04, 0xe1, 0xfe, 0xc4, 0x57, 0x66, 0x36, 0x49, 0xa6, 0x11, 0x13, 0x8d, 0xbc, 0x2a, 0x76, 0xad}|VOID*|0xA0010001

  ## This is the GUID of the FFS which contains the Pkcs7TestPublicKeyFile as a RAW section.
  # @Prompt GUID of the FFS which contains the Pkcs7TestPublicKeyFile.
  gEfiSignedCapsulePkgTokenSpaceGuid.PcdEdkiiPkcs7TestPublicKeyFileGuid|{0xba, 0xf5, 0x93, 0xf0, 0x37, 0x6f, 0x16, 0x48, 0x9e, 0x52, 0x91, 0xbe, 0xa0, 0xf7, 0xe0, 0xb8}|VOID*|0xA0010002

  ## This is the lowest supported version number that can be upgraded to, as exposed via the System Firmware descriptor.
  # @Prompt Lowest support version number that can be upgraded to via capsule update
  gEfiSignedCapsulePkgTokenSpaceGuid.PcdLowestSupportedFirmwareVersion|0x1|UINT32|0xA0010003

[PcdsDynamicEx]
  ## This dynamic PCD holds the EDKII system firmware image descriptor.
  #  This information can be used for version check in EDKII system FMP capsule.
  #  Only if the new EdkiiSystemFirmwareImageDescriptor.Version is not less than
  #  the current PcdEdkiiSystemFirmwareImageDescriptor.LowestSupportedVersion,
  #  the EDKII system FmpCapsule will be processed.
  #  The data structure of this PCD is EDKII_SYSTEM_FIRMWARE_IMAGE_DESCRIPTOR,
  #  SignedCapsulePkg/Include/Guid/EdkiiSystemFmpCapsule.h.
  #  It must be in [PcdsDynamicEx], because the EDKII system firmware update module may
  #  consume the PCD produced in current system firmware.
  # @Prompt EDKII system firmware image descriptor.
  gEfiSignedCapsulePkgTokenSpaceGuid.PcdEdkiiSystemFirmwareImageDescriptor|{0x0}|VOID*|0x00000037

  ## This dynamic PCD hold the GUID of a firmware FFS which includes EDKII
  #  system firmware image.
  #  An EDKII system firmware update module need consume this PCD to extract
  #  the EDKII system firmware from the capsule image.
  #  It must be in [PcdsDynamicEx], because the EDKII system firmware update module may
  #  consume the PCD produced in current system firmware image.
  # @Prompt EDKII system firmware image FFS GUID.
  gEfiSignedCapsulePkgTokenSpaceGuid.PcdEdkiiSystemFirmwareFileGuid|{0x0}|VOID*|0x00001010


## @file
# Memory Allocation Library instance standalone MM modules.
#
# Copyright (c) 2010 - 2015, Intel Corporation. All rights reserved.<BR>
# Copyright (c) 2016 - 2018, ARM Limited. All rights reserved.<BR>
# Copyright (c) 2018, Linaro, Ltd. All rights reserved.<BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x0001001B
  BASE_NAME                      = MemoryAllocationLib
  FILE_GUID                      = 54646378-A9DC-473F-9BE1-BD027C4C76DE
  MODULE_TYPE                    = MM_STANDALONE
  VERSION_STRING                 = 1.0
  PI_SPECIFICATION_VERSION       = 0x00010032
  LIBRARY_CLASS                  = MemoryAllocationLib|MM_STANDALONE

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 ARM AARCH64
#

[Sources]
  StandaloneMmMemoryAllocationLib.c

[Packages]
  MdePkg/MdePkg.dec
  StandaloneMmPkg/StandaloneMmPkg.dec

[LibraryClasses]
  BaseMemoryLib
  DebugLib
  MmServicesTableLib

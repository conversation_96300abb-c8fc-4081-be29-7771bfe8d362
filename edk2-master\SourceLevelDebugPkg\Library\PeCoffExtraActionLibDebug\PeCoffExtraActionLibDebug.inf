## @file
#  PeCoffExtraAction Library to support source level debug.
#
#  Copyright (c) 2010 - 2018, Intel Corporation. All rights reserved.<BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = PeCoffExtraActionLib
  MODULE_UNI_FILE                = PeCoffExtraActionLib.uni
  FILE_GUID                      = 8F01CBD5-E069-44d7-90C9-35F0318603AD
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 0.8
  LIBRARY_CLASS                  = PeCoffExtraActionLib

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources.common]
  PeCoffExtraActionLib.h
  PeCoffExtraActionLib.c

[Sources.IA32]
  Ia32/IntHandlerFuncs.c
  Ia32/IntHandler.nasm

[Sources.X64]
  X64/IntHandlerFuncs.c
  X64/IntHandler.nasm

[Packages]
  MdePkg/MdePkg.dec
  SourceLevelDebugPkg/SourceLevelDebugPkg.dec

[LibraryClasses]
  BaseLib
  DebugLib
  IoLib
  PcdLib

[Pcd]
  gEfiSourceLevelDebugPkgTokenSpaceGuid.PcdDebugLoadImageMethod    ## CONSUMES


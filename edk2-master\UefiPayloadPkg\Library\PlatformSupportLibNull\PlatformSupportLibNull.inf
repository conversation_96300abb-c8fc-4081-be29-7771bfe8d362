## @file
#  Include all platform specific features which can be customized by IBV/OEM.
#
#  Copyright (c) 2016 - 2019, Intel Corporation. All rights reserved.<BR>
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = PlatformSupportLib
  FILE_GUID                      = B42AA265-00CA-4d4b-AC14-DBD5268E1BC7
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = PlatformSupportLib

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 EBC
#

[Sources]
  PlatformSupportLibNull.c

[Packages]
  MdePkg/MdePkg.dec
  UefiPayloadPkg/UefiPayloadPkg.dec

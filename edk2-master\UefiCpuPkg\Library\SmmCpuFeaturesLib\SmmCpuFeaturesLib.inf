## @file
#  The CPU specific programming for PiSmmCpuDxeSmm module.
#
#  Copyright (c) 2009 - 2023, Intel Corporation. All rights reserved.<BR>
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = SmmCpuFeaturesLib
  MODULE_UNI_FILE                = SmmCpuFeaturesLib.uni
  FILE_GUID                      = FC3DC10D-D271-422a-AFF3-CBCF70344431
  MODULE_TYPE                    = DXE_SMM_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = SmmCpuFeaturesLib
  CONSTRUCTOR                    = SmmCpuFeaturesLibConstructor

[Sources]
  CpuFeaturesLib.h
  IntelSmmCpuFeaturesLib.c
  SmmCpuFeaturesLib.c
  SmmCpuFeaturesLibCommon.c
  SmmCpuFeaturesLibNoStm.c
  TraditionalMmCpuFeaturesLib.c

[Packages]
  MdePkg/MdePkg.dec
  UefiCpuPkg/UefiCpuPkg.dec

[LibraryClasses]
  BaseLib
  PcdLib
  MemoryAllocationLib
  DebugLib
  HobLib

[Guids]
  gSmmBaseHobGuid                ## CONSUMES

[Pcd]
  gUefiCpuPkgTokenSpaceGuid.PcdCpuMaxLogicalProcessorNumber        ## SOMETIMES_CONSUMES

[FeaturePcd]
  gUefiCpuPkgTokenSpaceGuid.PcdSmrrEnable  ## CONSUMES
  gUefiCpuPkgTokenSpaceGuid.PcdSmmFeatureControlEnable  ## CONSUMES

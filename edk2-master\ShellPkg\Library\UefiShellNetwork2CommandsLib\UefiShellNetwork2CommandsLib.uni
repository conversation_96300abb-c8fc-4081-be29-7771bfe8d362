/** @file

  String definitions for UEFI Shell network 2 commands
  Copyright (c) 2016, Intel Corporation. All rights reserved.<BR>
  (C) Copyright 2017 Hewlett Packard Enterprise Development LP<BR>

  SPDX-License-Identifier: BSD-2-Clause-Patent

  Module Name:

   UefiShellNetwork2CommandsLib.uni

  Abstract:

   String definitions for UEFI Shell 2.0 network 2 commands
**/

#langdef   en-US "english"

#string STR_GEN_ERR_AD                     #language en-US  "%H%s%N: Access denied.\r\n"
#string STR_GEN_OUT_MEM                    #language en-US  "%H%s%N: Memory allocation was not successful.\r\n"

#string STR_PING6_INVALID_IP               #language en-US  "%Ping6: Invalid IP6 address, %s\r\n"
#string STR_PING6_INVALID_INPUT            #language en-US  "%Ping6: Invalid input, please type 'Ping6 -?'for help\r\n"
#string STR_PING6_INVALID_SEND_NUMBER      #language en-US  "%Ping6: Invalid send number, %s\r\n"
#string STR_PING6_INVALID_BUFFER_SIZE      #language en-US  "%Ping6: Invalid buffer size, %s\r\n"
#string STR_PING6_INVALID_SOURCE           #language en-US  "%Ping6: Require source interface option\r\n"
#string STR_PING6_IP6_CONFIG               #language en-US  "%Ping6: The process of Ip6 Configure %r\r\n"
#string STR_PING6_IP6_RECEIVE              #language en-US  "%Ping6: Place the receive token %r\r\n"
#string STR_PING6_IP6CFG_GETDATA           #language en-US  "%Ping6: Get data of the interface information %r\r\n"
#string STR_PING6_SEND_REQUEST             #language en-US  "Echo request sequence %d fails.\r\n"
#string STR_PING6_CONFIGD_NIC_NF           #language en-US  "%Ping6: No configured interfaces were found.\r\n"
#string STR_PING6_NOSOURCE_INDOMAIN        #language en-US  "No sources in %s's multicast domain.\r\n"
#string STR_PING6_START                    #language en-US  "Ping %s %d data bytes\r\n"
#string STR_PING6_TIMEOUT                  #language en-US  "Echo request sequence %d timeout.\r\n"
#string STR_PING6_REPLY_INFO               #language en-US  "%d bytes from %s : icmp_seq=%d ttl=%d time=%d~%dms\r\n"
#string STR_PING6_STAT                     #language en-US  "\n%d packets transmitted, %d received, %d%% packet loss, time %dms\r\n"
#string STR_PING6_RTT                      #language en-US  "\nRtt(round trip time) min=%d~%dms max=%d~%dms avg=%d~%dms\r\n"

#string STR_IFCONFIG6_ERR_IP6CFG_GETDATA       #language en-US    "Get data of the interface information %hr\r\n"
#string STR_IFCONFIG6_INFO_BREAK               #language en-US    "-----------------------------------------------------------------"
#string STR_IFCONFIG6_INFO_COLON               #language en-US    ":"
#string STR_IFCONFIG6_INFO_JOINT               #language en-US    " >> "
#string STR_IFCONFIG6_INFO_NEWLINE             #language en-US    "\r\n"
#string STR_IFCONFIG6_INFO_IF_NAME             #language en-US    "\n%Hname         : %s%N\r\n"
#string STR_IFCONFIG6_INFO_POLICY_AUTO         #language en-US    "%Hpolicy       : automatic%N\r\n"
#string STR_IFCONFIG6_INFO_POLICY_MAN          #language en-US    "%Hpolicy       : manual%N\r\n"
#string STR_IFCONFIG6_INFO_DAD_TRANSMITS       #language en-US    "%Hdad xmits    : %d%N\r\n"
#string STR_IFCONFIG6_INFO_INTERFACE_ID_HEAD   #language en-US    "%Hinterface id : %N"
#string STR_IFCONFIG6_INFO_MAC_ADDR_HEAD       #language en-US    "%Hmac addr     : %N"
#string STR_IFCONFIG6_INFO_MAC_ADDR_BODY       #language en-US    "%02x"
#string STR_IFCONFIG6_INFO_IP_ADDR_HEAD        #language en-US    "\n%Hhost addr    : %N\r\n"
#string STR_IFCONFIG6_INFO_DNS_ADDR_HEAD       #language en-US    "\n%Hdns server   : %N\r\n"
#string STR_IFCONFIG6_INFO_IP_ADDR_BODY        #language en-US    "%02x"
#string STR_IFCONFIG6_INFO_IP_ADDR_BODY4BIT    #language en-US    "%x"
#string STR_IFCONFIG6_INFO_ROUTE_HEAD          #language en-US    "\n%Hroute table  : %N\r\n"
#string STR_IFCONFIG6_INFO_PREFIX_LEN          #language en-US    "/%d"
#string STR_IFCONFIG6_LINE_HELP                #language en-US    "Displays or modifies the IPv6 configuration"
#string STR_IFCONFIG6_ERR_LACK_INTERFACE       #language en-US    "Lack interface name.\r\n"
                                                                  "Usage: IfConfig6 -s <Name> \r\n"
                                                                  "Example: IfConfig6 -s eth0 auto\r\n"
#string STR_IFCONFIG6_LACK_OPTION              #language en-US    "Flags lack. Please type 'IfConfig6 -?' for help info.\r\n"
#string STR_IFCONFIG6_CONFLICT_OPTIONS         #language en-US    "Flags conflict. Please type 'IfConfig6 -?' for help info.\r\n"
#string STR_IFCONFIG6_ERR_LACK_COMMAND         #language en-US    "Lack interface config option.\r\n"
                                                                  "Hint: Please type 'IfConfig6 -?' for help info.\r\n"
#string STR_IFCONFIG6_ERR_INVALID_INTERFACE    #language en-US    "Invalid interface name.\r\n"
                                                                  "Hint: Use {IfConfig6 -l} to check existing interface names.\r\n"
#string STR_IFCONFIG6_ERR_INVALID_COMMAND      #language en-US    "Invalid command. Bad command %H%s%N is skipped.\r\n"
                                                                  "Hint: Incorrect option or arguments. Please type 'IfConfig6 -?' for help info.\r\n"
#string STR_IFCONFIG6_ERR_LACK_ARGUMENTS       #language en-US    "Lack arguments. Bad command %H%s%N is skipped.\r\n"
                                                                  "Hint: Please type 'IfConfig6 -?' for help info.\r\n"
#string STR_IFCONFIG6_ERR_LACK_OPTION          #language en-US    "Lack options.\r\n"
                                                                  "Hint: Please type 'IfConfig6 -?' for help info.\r\n"
#string STR_IFCONFIG6_ERR_MAN_HOST             #language en-US    "Manual address configuration failed. Please retry.\r\n"

#string STR_IFCONFIG6_ERR_MAN_GW               #language en-US    "Gateway address configuration failed. Please check the argument.\r\n"

#string STR_IFCONFIG6_ERR_INVALID_IP_CONFIG    #language en-US    "The IP address is not configurable when the policy is Ip6ConfigPolicyAutomatic.\r\n"

#string STR_IFCONFIG6_ERR_INVALID_GW_CONFIG    #language en-US    "The gateway address is not configurable when the policy is Ip6ConfigPolicyAutomatic.\r\n"

#string STR_IFCONFIG6_ERR_INVALID_DNS_CONFIG   #language en-US    "The DNS server address is not configurable when the policy is Ip6ConfigPolicyAutomatic.\r\n"

#string STR_IFCONFIG6_ERR_DUPLICATE_COMMAND    #language en-US    "Duplicate commands. Bad command %H%s%N is skipped.\r\n"
                                                                  "Hint: Please type 'IfConfig6 -?' for help info.\r\n"
#string STR_IFCONFIG6_ERR_CONFLICT_COMMAND     #language en-US    "Conflict commands. Bad command %H%s%N is skipped.\r\n"
                                                                  "Hint: Please type 'IfConfig6 -?' for help info.\r\n"
#string STR_IFCONFIG6_ERR_UNKNOWN_COMMAND      #language en-US    "Unknown commands. Bad command %H%s%N is skipped.\r\n"
                                                                  "Hint: Please type 'IfConfig6 -?' for help info.\r\n"
#string STR_IFCONFIG6_ERR_ADDRESS_FAILED       #language en-US    "Failed to configure IPv6 address: "


#string STR_GET_HELP_PING6                     #language en-US ""
".TH ping6 0 "Ping a target machine with UEFI IPv6 network stack."\r\n"
".SH NAME\r\n"
"Ping a target machine with UEFI IPv6 network stack.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"Ping6 [-l size] [-n count] [-s SourceIp] TargetIp\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  -l size     Send buffer size, in bytes(default=16, min=16, max=32768).\r\n"
"  -n count    Send request count, (default=10, min=1, max=10000).\r\n"
"  -s SourceIp Source IPv6 address.\r\n"
"  TargetIp    Target IPv6 address.\r\n"
"NOTES:\r\n"
"  1. When TargetIp is a link local address, SourceIp needs to be specified.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"Examples:\r\n"
"  * To ping the target host by sending 5 request with 1000 bytes from 2002::1\r\n"
"    Shell:\> Ping6 -s 2002::1 2002::2 -l 1000 -n 5\r\n"
" \r\n"
"  * To ping the target host with 1000 bytes\r\n"
"    Shell:\> Ping6 2002::2 -l 1000\r\n"

#string STR_GET_HELP_IFCONFIG6                 #language en-US    ""
".TH ifconfig6 0 "Displays or modifies IPv6 configuration for network interface."\r\n"
".SH NAME\r\n"
"Displays or modifies IPv6 configuration for network interface.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"IfConfig6 -r [Name] | -l [Name] \r\n"
"IfConfig6 -s <Name> [dad <Num>] [auto | [man [id <mac>] [host <IPv6> gw <IPv6>]\r\n"
"             [dns <IPv6>]]]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  Name                     Adapter name, i.e., eth0\r\n"
"  -r [Name]                Reconfigure all or specified interface, and set\r\n"
"                           automatic policy. If specified interface is already\r\n"
"                           set to automatic,then refresh the IPv6 configuration.\r\n"
"  -l [Name]                List the configuration of the specified interface.\r\n"
"  -s <Name> dad <Num>      Set dad transmits count of the specified interface.\r\n"
"  -s <Name> auto           Set automatic policy of the specified interface.\r\n"
"  -s <Name> man id <Mac>   Set alternative interface id of the specified \r\n"
"                           interface. Must under manual policy.\r\n"
"  -s <Name> man host <IPv6> gw <IPv6>\r\n"
"                           Set static host IP and gateway address of the\r\n"
"                           specified interface. Must under manual policy.\r\n"
"  -s <Name> man dns <IPv6> Set DNS server IP addresses of the specified\r\n"
"                           interface.Must under manual policy.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To list the configuration for the interface eth0:\r\n"
"    Shell:\> ifConfig6 -l eth0\r\n"
" \r\n"
"  * To use automatic configuration to request the IPv6 address configuration\r\n"
"    dynamically for the interface eth0:\r\n"
"    Shell:\> ifconfig6 -s eth0 auto\r\n"
" \r\n"
"  * To set the dad transmits count for eth0 under automatic policy:\r\n"
"    Shell:\> ifconfig6 -s eth0 auto dad 10\r\n"
" \r\n"
"  * To set the alternative interface id of eth0 under manual policy:\r\n"
"    Shell:\> ifconfig6 -s eth0 man id ff:dd:aa:88:66:cc\r\n"
" \r\n"
"  * To use the static IP6 addresses configuration for the interface eth0,\r\n"
"    and this configuration survives the network reload:\r\n"
"    Shell:\> ifconfig6 -s eth0 man host 2002::1/64 2002::2/64 gw 2002::3/64\r\n"

// /**
//
// (C) Copyright 2016 Hewlett Packard Enterprise Development LP<BR>
// (C) Copyright 2013-2015 Hewlett-Packard Development Company, L.P.<BR>
// Copyright (c) 2009 - 2017, Intel Corporation. All rights reserved. <BR>
// SPDX-License-Identifier: BSD-2-Clause-Patent
//
// Module Name:
//
// UefiShellLevel3CommandsLib.uni
//
// Abstract:
//
// String definitions for UEFI Shell 2.0 level 3 commands
//
//
// **/

/=#

#langdef   en-US "english"

#string STR_GEN_TOO_MANY          #language en-US "%H%s%N: Too many arguments\r\n"
#string STR_GEN_TOO_FEW           #language en-US "%H%s%N: Too few arguments\r\n"
#string STR_GEN_PARAM_INV         #language en-US "%H%s%N: Invalid argument - '%H%s%N'\r\n"
#string STR_GEN_PROBLEM           #language en-US "%H%s%N: Unknown flag - '%H%s%N'\r\n"
#string STR_GEN_NO_VALUE          #language en-US "%H%s%N: Missing argument for flag - '%H%s%N'\r\n"
#string STR_GEN_ERR_AD            #language en-US "%H%s%N: Access denied.\r\n"
#string STR_GEN_ERR_NOT_FOUND     #language en-US "%H%s%N: '%H%s%N' does not exist.\r\n"
#string STR_GEN_ERR_UK            #language en-US "%H%s%N: Status: %r\r\n"
#string STR_GEN_PARAM_CON         #language en-US "%H%s%N: Parameters conflict\r\n"
#string STR_GEN_PARAM_CONFLICT    #language en-US "%H%s%N: Flags conflict with - '%H%s%N' and '%H%s%N'\r\n"
#string STR_GEN_FILE_OPEN_FAIL    #language en-US "%H%s%N: Cannot open file - '%H%s%N'\r\n"
#string STR_GEN_FILE_AD           #language en-US "%H%s%N: Access file error - '%H%s%N'\r\n"
#string STR_GEN_CRLF              #language en-US "\r\n"
#string STR_GEN_NO_CWD            #language en-US "%H%s%N: Current directory not specified\r\n"
#string STR_GEN_NO_FILES          #language en-US "%H%s%N: No matching files were found\r\n"
#string STR_GEN_DIR_NF            #language en-US "%H%s%N: Directory not found - '%H%s%N'\r\n"
#string STR_GEN_FILE_NF           #language en-US "%H%s%N: File not found - '%H%s%N'\r\n"
#string STR_GEN_IS_DIR            #language en-US "%H%s%N: '%H%s%N' is a directory\r\n"
#string STR_GEN_SFO_HEADER        #language en-US "ShellCommand,"%s"\r\n"
#string STR_NO_SCRIPT             #language en-US "The command '%H%s%N' is not allowed outside of a script\r\n"

#string STR_TYP_ERROR             #language en-US "%H%s%N: Operation was not successful on '%H%s%N'\r\n"

#string STR_TOUCH_ERROR           #language en-US "%H%s%N: Operation was not successful on '%H%s%N'\r\n"

#string STR_VER_OUTPUT_SHELL      #language en-US "UEFI %s Shell v%d.%d\r\n"
#string STR_VER_OUTPUT_SIMPLE     #language en-US "%d.%d\r\n"
#string STR_VER_OUTPUT_UEFI       #language en-US "UEFI v%d.%02d (%s, 0x%08x)\r\n"
#string STR_VER_OUTPUT_SUPPLIER   #language en-US "%s\r\n"

#string STR_ECHO_ON               #language en-US "Echo is on.\r\n"
#string STR_ECHO_OFF              #language en-US "Echo is off.\r\n"

#string STR_PAUSE_PROMPT          #language en-US "Enter 'q' to quit, any other key to continue:\r\n"

#string STR_HELP_NF               #language en-US "No help could be found for command '%B%s%N'.\r\n"
#string STR_HELP_INV              #language en-US "The help data for command '%B%s%N' was incorrect format.\r\n"
#string STR_HELP_SC_HEADER        #language en-US "Character    Description\r\n"
                                                  "---------    ---------------------------------------------- \r\n"
#string STR_HELP_SC_DATA          #language en-US "<newline>    Ends a command line.\r\n"
                                                  "<space>      Ends an argument, if it is not in a quotation.\r\n"
                                                  "#            Starts a comment.\r\n"
                                                  ">            Used for output redirection.\r\n"
                                                  "<            Used for input redirection.\r\n"
                                                  "|            Used for pipe command support.\r\n"
                                                  "%            Used to delimit a variable or an argument.\r\n"
                                                  "\"            Used to delimit a quotation.\r\n"
                                                  "^            Prevents the next character from being\r\n"
                                                  "             interpreted as having special meaning.\r\n"
                                                  "             Can be used inside quoted strings.\r\n"
                                                  "*, ?, [, ]   Wildcards to specify multiple similar file names.\r\n"
#string STR_HELP_COMMAND          #language en-US "%H%-14s%N- %s\r\n"
#string STR_HELP_FOOTER           #language en-US "%N\r\nHelp usage:help [%Hcmd%N|%Hpattern%N|%Hspecial%N] [%H-usage%N] [%H-verbose%N] [%H-section name%N][%H-b%N]\r\n"

#string STR_HELP_PAGE_COMMAND     #language en-US "%N%s\r\n"

#string STR_ALIAS_OUTPUT          #language en-US "%1c %10s:%s\r\n"

#string STR_GET_MTC_OUTPUT        #language en-US "%016Lx\r\n"
#string STR_CLS_OUTPUT_SFO        #language en-US "ConOutAttribInfo,"%d","%d","%d"\r\n"

#string STR_GET_HELP_HELP         #language en-US ""
".TH help 0 "Displays help information from the UEFI Shell."\r\n"
".SH NAME\r\n"
"Displays the UEFI Shell command list or verbose command help.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"HELP [cmd | pattern | special] [-usage] [-verbose] [-section sectionname][-b]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  -usage      - Displays the usage information for the command. The same as\r\n"
"                specifying "-section NAME" and "-section SYNOPSIS" \r\n"
"  -section    - Displays the specified section of the help information.\r\n"
"  -b          - Displays one page on screen and allows user to continue\r\n"
"                to next page\r\n"
"  cmd         - Specifies a command to display help about.\r\n"
"  pattern     - Specifies a pattern which describes the commands to be displayed.\r\n"
"  special     - Displays a list of the special characters used in the shell\r\n"
"                command line.\r\n"
"  sectionname - Specifies a section name. Supported options are:\r\n"
"                  - NAME\r\n"
"                  - SYNOPSIS\r\n"
"                  - OPTIONS\r\n"
"                  - DESCRIPTION\r\n"
"                  - EXAMPLES\r\n"
"                  - RETURNVALUES\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. The HELP command displays information about one or more shell commands.\r\n"
"  2. If no other options are specified, each command will be displayed along\r\n"
"     with a brief description of its function.\r\n"
"  3. If -verbose is specified, then display all help information for the\r\n"
"     specified commands.\r\n"
"  4. If -section is specified, only the help section specified will be\r\n"
"     displayed.\r\n"
"  5. If -usage is specified, then the command, a brief description\r\n"
"     and the usage will be displayed.\r\n"
"  6. The help text is gathered from UCS-2 text files found in the directory\r\n"
"     where the shell or shell command executable was located. The files have\r\n"
"     the name commandname.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To display the list of commands in the UEFI Shell and break after one\r\n"
"    screen:\r\n"
"    Shell> help -b\r\n"
" \r\n"
"  * To display help information of a Shell command - ls:\r\n"
"    Shell> help ls\r\n"
"    Shell> -? ls\r\n"
"    Shell> ls -?\r\n"
" \r\n"
"  * To display the list of commands that start with character 'p':\r\n"
"    Shell> help p*\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  0  The help was displayed\r\n"
"  1  No command help was displayed\r\n"

#string STR_GET_HELP_ALIAS        #language en-US ""
".TH alias 0 "Handles aliases in the Shell."\r\n"
".SH NAME\r\n"
"Displays, creates, or deletes UEFI Shell aliases.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"ALIAS [-d|-v] [alias-name] [command-name]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  -d           - Deletes an alias. Command-name must not be specified.\r\n"
"  -v           - Makes the alias volatile.\r\n"
"  alias-name   - Specifies an alias name.\r\n"
"  command-name - Specifies an original command's name or path.\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. This command displays, creates, or deletes aliases in the UEFI Shell\r\n"
"     environment.\r\n"
"  2. An alias provides a new name for an existing UEFI Shell\r\n"
"     command or UEFI application. Once the alias is created, it can be used\r\n"
"     to run the command or launch the UEFI application.\r\n"
"  3. There are some aliases that are predefined in the UEFI Shell environment.\r\n"
"     These aliases provide the MS-DOS and UNIX equivalent names for the file\r\n"
"     manipulation commands.\r\n"
"  4. Aliases will be retained even after exiting the shell unless the -v option\r\n"
"     is specified. If -v is specified then the alias will not be valid after\r\n"
"     leaving the shell.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To display all aliases in the UEFI Shell environment:\r\n"
"    Shell> alias\r\n"
" \r\n"
"  * To create an alias in the UEFI Shell environment:\r\n"
"    Shell> alias shutdown "reset -s" \r\n"
" \r\n"
"  * To delete an alias in the UEFI Shell environment:\r\n"
"    Shell> alias -d shutdown\r\n"
" \r\n"
"  * To add a volatile alias in the current UEFI environment, which has a star *\r\n"
"    at the line head. This volatile alias will disappear at next boot.\r\n"
"    Shell> alias -v fs0 floppy\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  SHELL_SUCCESS             The action was completed as requested.\r\n"
"  SHELL_INVALID_PARAMETER   One of the passed-in parameters was incorrectly\r\n"
"                            formatted or its value was out of bounds.\r\n"
"  SHELL_OUT_OF_RESOURCES    A request to set a variable in a non-volatile\r\n"
"                            fashion could not be completed. The resulting\r\n"
"                            non-volatile request has been converted into\r\n"
"                            a volatile request.\r\n"
"  SHELL_SECURITY_VIOLATION  This function was not performed due to a security\r\n"
"                            violation.\r\n"

#string STR_GET_HELP_CLS          #language en-US ""
".TH cls 0 "clear screen"\r\n"
".SH NAME\r\n"
"Clears the console output and optionally changes the background and foreground color.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"CLS [background] [foreground] | [-sfo]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  background - Sets a new background color:\r\n"
"               0 - Black\r\n"
"               1 - Blue\r\n"
"               2 - Green\r\n"
"               3 - Cyan\r\n"
"               4 - Red\r\n"
"               5 - Magenta\r\n"
"               6 - Yellow\r\n"
"               7 - Light gray\r\n"
"  foreground - Sets a new foreground color:\r\n"
"               0  - Black\r\n"
"               1  - Blue\r\n"
"               2  - Green\r\n"
"               3  - Cyan\r\n"
"               4  - Red\r\n"
"               5  - Magenta\r\n"
"               6  - Yellow\r\n"
"               7  - Light gray\r\n"
"               8  - Dark gray\r\n"
"               9  - Light blue\r\n"
"               10 - Light green\r\n"
"               11 - Light cyan\r\n"
"               12 - Light red\r\n"
"               13 - Light magenta\r\n"
"               14 - Yellow\r\n"
"               15 - White\r\n"
"  -sfo       - Displays current console color settings in Standard Format\r\n"
"               Output.\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. This command clears the standard output device with an optional\r\n"
"     background and foreground color attribute.\r\n"
"  2. If background color is not specified, or if background and foreground\r\n"
"     colors are not specified, then the colors do not change.\r\n"
"  3. When -sfo flag is used, console output is not cleared and instead it\r\n"
"     displays current console foreground and background attribute settings.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To clear standard output without changing the background or foreground\r\n"
"    color:\r\n"
"    fs0:\> cls\r\n"
" \r\n"
"  * To clear standard output and change the background color to cyan:\r\n"
"    fs0:\> cls 3\r\n"
" \r\n"
"  * To clear standard output and change the background to black and foreground\r\n"
"    to white:\r\n"
"    fs0:\> cls 0 15\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  SHELL_SUCCESS             The action was completed as requested.\r\n"
"  SHELL_INVALID_PARAMETER   One of the passed-in parameters was incorrectly\r\n"
"                            formatted or its value was out of bounds.\r\n"
"  SHELL_SECURITY_VIOLATION  This function was not performed due to a security\r\n"
"                            violation.\r\n"
"  SHELL_NOT_FOUND           The requested file was not found.\r\n"

#string STR_GET_HELP_ECHO         #language en-US ""
".TH echo 0 "display text or control text output"\r\n"
".SH NAME\r\n"
"Controls script file command echoing or displays a message.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"ECHO [-on|-off]\r\n"
"ECHO [message]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  -on     - Enables display when reading commands from script files.\r\n"
"  -off    - Disables display when reading commands from script files.\r\n"
"  message - Specifies a message to display.\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. The first form of this command controls whether script commands are\r\n"
"     displayed as they are read from the script file. If no argument is given,\r\n"
"     the current "on" or "off" status is displayed.\r\n"
"  2. The second form prints the given message to the display.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To display a message string of 'Hello World':\r\n"
"    fs0:\> echo Hello World\r\n"
" \r\n"
"  * To turn command echoing on:\r\n"
"    fs0:\> echo -on\r\n"
" \r\n"
"  * To execute HelloWorld.nsh, and display when reading lines from the script\r\n"
"    file:\r\n"
"    fs0:\> HelloWorld.nsh\r\n"
"    +HelloWorld.nsh> echo Hello World\r\n"
" \r\n"
"  * To turn command echoing off:\r\n"
"    fs0:\> echo -off\r\n"
" \r\n"
"  * To display the current echo setting:\r\n"
"    fs0:\> echo\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  SHELL_SUCCESS             The action was completed as requested.\r\n"
"  SHELL_SECURITY_VIOLATION  This function was not performed due to a security\r\n"
"                            violation.\r\n"

#string STR_GET_HELP_GETMTC       #language en-US ""
".TH getmtc 0 "gets the MTC count"\r\n"
".SH NAME\r\n"
"Gets the MTC from BootServices and displays it.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"GETMTC\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. This command displays the current monotonic counter value. The lower 32\r\n"
"     bits increment every time this command is executed. Every time the system\r\n"
"     is reset, the upper 32 bits will be incremented, and the lower 32 bits\r\n"
"     will be reset to 0.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To display the current monotonic counter value:\r\n"
"    fs0:\> getmtc\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  SHELL_SUCCESS              The action was completed as requested.\r\n"
"  SHELL_DEVICE_ERROR         The underlying device was not working correctly.\r\n"
"  SHELL_SECURITY_VIOLATION   This function was not performed due to a security\r\n"
"                             violation.\r\n"

#string STR_GET_HELP_PAUSE        #language en-US ""
".TH pause 0 "pauses scripts"\r\n"
".SH NAME\r\n"
"Pauses a script and waits for an operator to press a key.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"PAUSE [-q]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  -q - Does not display a test output prompt.\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. The PAUSE command prints a message to the display, then suspends script\r\n"
"     file execution, and waits for keyboard input. Pressing any key resumes\r\n"
"     execution, except for q or Q. If either q or Q is pressed, script\r\n"
"     processing terminates; otherwise, execution continues with the next line\r\n"
"     after the pause command.\r\n"
"  2. The PAUSE command is available only in scripts. Switch -q can hide the\r\n"
"     message and it's optional.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * Following script is a sample of 'pause' command:\r\n"
"    fs0:\> type pause.nsh\r\n"
"    #\r\n"
"    # Example script for 'pause' command\r\n"
"    #\r\n"
"    echo pause.nsh begin..\r\n"
"    date\r\n"
"    time\r\n"
"    pause\r\n"
"    echo pause.nsh done.\r\n"
" \r\n"
"  * To execute the script with echo on:\r\n"
"    fs0:\> pause.nsh\r\n"
"    +pause.nsh> echo pause.nsh begin..\r\n"
"    pause.nsh begin..\r\n"
"    +pause.nsh> date\r\n"
"    06/19/2001\r\n"
"    +pause.nsh> time\r\n"
"    00:51:45\r\n"
"    +pause.nsh> pause\r\n"
"    Enter 'q' to quit, or any other key to continue:\r\n"
"    +pause.nsh> echo pause.nsh done.\r\n"
"    pause.nsh done.\r\n"
" \r\n"
"  * To execute the script with echo off:\r\n"
"    fs0:\> echo -off\r\n"
"    fs0:\> pause.nsh\r\n"
"    pause.nsh begin..\r\n"
"    06/19/2001\r\n"
"    00:52:50\r\n"
"    Enter 'q' to quit, or any other key to continue: q\r\n"
"    fs0:\>\r\n"

#string STR_GET_HELP_TOUCH        #language en-US ""
".TH touch 0 "Touch a file to update a directory"\r\n"
".SH NAME\r\n"
"Updates the filename timestamp with the current system date and time.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"TOUCH [-r] file [file ...]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  -r   - Sets the update as recurse into subdirectories.\r\n"
"  file - Specifies the name or pattern of the file or directory. There can be multiple \r\n"
"         files on the command-line.\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. This command updates to the current time and date the time and date on\r\n"
"     the file that is specified by the file parameter.\r\n"
"  2. If multiple files are specified on the command line, it will continue\r\n"
"     processing. It will touch the files one by one and errors will be\r\n"
"     ignored.\r\n"
"  3. TOUCH cannot change the time and date of read-only files and directories.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To update the timestamp of a specific file:\r\n"
"    fs0:\> touch for.nsh\r\n"
" \r\n"
"  * To touch a directory recursively:\r\n"
"    fs0:\> touch -r efi1.1\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  SHELL_SUCCESS             The action was completed as requested.\r\n"
"  SHELL_NOT_FOUND           The target file or set of files were not found.\r\n"
"  SHELL_SECURITY_VIOLATION  This function was not performed due to a security\r\n"
"                            violation.\r\n"
"  SHELL_WRITE_PROTECTED     The media was write-protected or the file had a\r\n"
"                            read-only attribute associated with it.\r\n"

#string STR_GET_HELP_TYPE         #language en-US ""
".TH type 0 "print a file to StdOut"\r\n"
".SH NAME\r\n"
"Sends the contents of a file to the standard output device.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"TYPE [-a|-u] file [file...]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  -a   - Displays the file as if it is encoded as 8-bit ASCII\r\n"
"  -u   - Displays the file as if it were encoded as UCS-2 Unicode.\r\n"
"  file - Specifies the name of the file to display.\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. This command sends the contents of a file to the standard output device.\r\n"
"     If no options are used, then the command attempts to automatically detect\r\n"
"     the file type. If it fails, then UCS-2 is presumed.\r\n"
"  2. If the -a option is specified, the file is sent to the standard output\r\n"
"     device as a stream of ASCII characters.\r\n"
"  3. If the -u option is specified, the file is sent to the standard output\r\n"
"     device as a stream of Unicode (UCS-2) characters.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To display a file in UCS-2 format:\r\n"
"    fs0:\> type -u pause.nsh\r\n"
" \r\n"
"  * To display a file in ASCII format:\r\n"
"    fs0:\> type -a pause.nsh\r\n"
" \r\n"
"  * To display multiple files:\r\n"
"    fs0:\> type test.*\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  SHELL_SUCCESS               The action was completed as requested.\r\n"
"  SHELL_INVALID_PARAMETER     One of the passed in parameters was incorrectly\r\n"
"                              formatted or its value was out of bounds.\r\n"
"  SHELL_SECURITY_VIOLATION    This function was not performed due to a security\r\n"
"                              violation.\r\n"
"  SHELL_NOT_FOUND             The target file or set of files were not found.\r\n"

#string STR_GET_HELP_VER          #language en-US ""
".TH ver 0 "prints out version info"\r\n"
".SH NAME\r\n"
"Displays UEFI Firmware version information.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"VER [-s|-terse]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  -s     -  Displays only the UEFI Shell version.\r\n"
"  -terse -  Displays only the first part of the data.\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. This command displays the version information for this UEFI Firmware, or\r\n"
"     the version information for the UEFI Shell itself. The information is\r\n"
"     retrieved through the UEFI System Table or the Shell image.\r\n"
" \r\n"
"  2. Standard format for ver output as shown below with a sample:\r\n"
"       UEFI <support-level> Shell v<uefi-shell-version>\r\n"
"       shell-supplier-specific-data\r\n"
"       UEFI v<uefi-firmware-version> (<firmware vendor name>, 0x<firmware vendor\r\n"
"       version as 32-bit hex value> <optional additional vendor version>)\r\n"
"       #\r\n"
"       # Sample \r\n"
"       #\r\n"
"       UEFI Basic Shell v2.0\r\n"
"       Copyright 2008 by Intel(R) Corporation.\r\n"
"       UEFI v2.31 (Intel(R) Corporation., 0x00010100)\r\n"
" \r\n"
"  3. UEFI version tag information:\r\n"
"       <support-level>\r\n"
"         0 = Minimal\r\n"
"         1 = Scripting\r\n"
"         2 = Basic\r\n"
"         3 = Interactive\r\n"
"       <uefi-shell-version>\r\n"
"         Comes from the Shell specification upon which the Shell\r\n"
"         implementation is based.\r\n"
"      <shell-supplier-specific-data>\r\n"
"         Build, copyright, etc.\r\n"
"      <uefi-firmware-version>\r\n"
"         Comes from the UEFI specification upon which the firmware\r\n"
"         implementation is based\r\n"
"      <firmware vendor name>\r\n"
"         Indicates Vendor Name\r\n"
"      <firmware vendor version>\r\n"
"         Indicates Vendor's firmware version\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To display UEFI Firmware version information:\r\n"
"    fs0:\> ver\r\n"
" \r\n"
"  * To display UEFI Shell version information only:\r\n"
"    Shell> ver -s\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  SHELL_SUCCESS   The action was completed as requested.\r\n"


## @file
#  Platform Hook Library instance for UART device for Universal Payload.
#
#  Copyright (c) 2021, Intel Corporation. All rights reserved.<BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = PlatformHookLib
  FILE_GUID                      = 807E05AB-9411-429F-97F0-FE425BF6B094
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = PlatformHookLib
  CONSTRUCTOR                    = PlatformHookSerialPortConstructor

[Sources]
  PlatformHookLib.c

[LibraryClasses]
  PcdLib
  BaseLib
  HobLib
  DxeHobListLib

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  UefiPayloadPkg/UefiPayloadPkg.dec

[Guids]
  gUniversalPayloadSerialPortInfoGuid

[Pcd]
  gEfiMdeModulePkgTokenSpaceGuid.PcdSerialUseMmio         ## PRODUCES
  gEfiMdeModulePkgTokenSpaceGuid.PcdSerialRegisterBase    ## PRODUCES
  gEfiMdeModulePkgTokenSpaceGuid.PcdSerialBaudRate        ## PRODUCES
  gEfiMdeModulePkgTokenSpaceGuid.PcdSerialRegisterStride  ## PRODUCES

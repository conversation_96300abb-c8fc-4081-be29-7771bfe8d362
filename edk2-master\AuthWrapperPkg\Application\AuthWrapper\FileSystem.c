/** @file
  File System Access Utilities

  This module provides file system access functionality for reading
  key files from the C drive and loading boot.efi.

  Copyright (c) 2024, Auth Wrapper Project. All rights reserved.
  SPDX-License-Identifier: BSD-2-Clause-Patent

**/

#include "AuthWrapper.h"

/**
  Get the file system protocol for C drive.

  @param[out] FileSystem        Pointer to file system protocol.

  @retval EFI_SUCCESS           File system protocol obtained successfully.
  @retval other                 Error occurred during operation.

**/
EFI_STATUS
GetCDriveFileSystem (
  OUT EFI_SIMPLE_FILE_SYSTEM_PROTOCOL **FileSystem
  )
{
  EFI_STATUS                       Status;
  UINTN                            HandleCount;
  EFI_HANDLE                       *HandleBuffer = NULL;
  EFI_SIMPLE_FILE_SYSTEM_PROTOCOL  *Fs;
  EFI_FILE_PROTOCOL                *Root;
  EFI_BLOCK_IO_PROTOCOL            *BlockIo;

  if (FileSystem == NULL) {
    return EFI_INVALID_PARAMETER;
  }

  *FileSystem = NULL;

  //
  // Locate all file system handles
  //
  Status = gBS->LocateHandleBuffer (
                  ByProtocol,
                  &gEfiSimpleFileSystemProtocolGuid,
                  NULL,
                  &HandleCount,
                  &HandleBuffer
                  );
  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_ERROR, "Failed to locate file system handles: %r\n", Status));
    return Status;
  }

  //
  // Try to find the C drive (first writable file system)
  //
  for (UINTN Index = 0; Index < HandleCount; Index++) {
    Status = gBS->HandleProtocol (
                    HandleBuffer[Index],
                    &gEfiSimpleFileSystemProtocolGuid,
                    (VOID **) &Fs
                    );
    if (EFI_ERROR (Status)) {
      continue;
    }

    //
    // Try to open the root directory
    //
    Status = Fs->OpenVolume (Fs, &Root);
    if (EFI_ERROR (Status)) {
      continue;
    }

    //
    // Check if this is a writable file system by checking Block I/O
    //
    Status = gBS->HandleProtocol (
                    HandleBuffer[Index],
                    &gEfiBlockIoProtocolGuid,
                    (VOID **) &BlockIo
                    );
    if (!EFI_ERROR (Status) && !BlockIo->Media->ReadOnly) {
      //
      // Found a writable file system, assume this is C drive
      //
      Root->Close (Root);
      *FileSystem = Fs;
      FreePool (HandleBuffer);
      DEBUG ((DEBUG_INFO, "Found C drive file system\n"));
      return EFI_SUCCESS;
    }

    Root->Close (Root);
  }

  FreePool (HandleBuffer);
  DEBUG ((DEBUG_ERROR, "C drive file system not found\n"));
  return EFI_NOT_FOUND;
}

/**
  Check if key file exists on C drive.

  @param[in] KeyFilename          Key filename to check.

  @retval TRUE                    Key file exists.
  @retval FALSE                   Key file does not exist.

**/
BOOLEAN
CheckKeyFileExists (
  IN CHAR16 *KeyFilename
  )
{
  EFI_STATUS                       Status;
  EFI_SIMPLE_FILE_SYSTEM_PROTOCOL  *FileSystem;
  EFI_FILE_PROTOCOL                *Root;
  EFI_FILE_PROTOCOL                *KeyFile;
  CHAR16                           FullPath[256];

  if (KeyFilename == NULL) {
    return FALSE;
  }

  //
  // Get C drive file system
  //
  Status = GetCDriveFileSystem (&FileSystem);
  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_ERROR, "Failed to get C drive file system: %r\n", Status));
    return FALSE;
  }

  //
  // Open root directory
  //
  Status = FileSystem->OpenVolume (FileSystem, &Root);
  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_ERROR, "Failed to open root directory: %r\n", Status));
    return FALSE;
  }

  //
  // Construct full path: KeyFilename.vtkey
  //
  UnicodeSPrint (FullPath, sizeof (FullPath), L"%s.vtkey", KeyFilename);

  //
  // Try to open the key file
  //
  Status = Root->Open (
                   Root,
                   &KeyFile,
                   FullPath,
                   EFI_FILE_MODE_READ,
                   0
                   );

  Root->Close (Root);

  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_INFO, "Key file does not exist: %s\n", FullPath));
    return FALSE;
  }

  //
  // File exists, close it
  //
  KeyFile->Close (KeyFile);
  DEBUG ((DEBUG_INFO, "Key file exists: %s\n", FullPath));
  return TRUE;
}

/**
  Read key file content from C drive.

  @param[in]  KeyFilename         Key filename to read.
  @param[out] FileContent         Content of the key file.
  @param[in]  ContentSize         Size of content buffer.

  @retval EFI_SUCCESS             Key file read successfully.
  @retval other                   Error occurred during reading.

**/
EFI_STATUS
ReadKeyFileContent (
  IN  CHAR16  *KeyFilename,
  OUT CHAR16  *FileContent,
  IN  UINTN   ContentSize
  )
{
  EFI_STATUS                       Status;
  EFI_SIMPLE_FILE_SYSTEM_PROTOCOL  *FileSystem;
  EFI_FILE_PROTOCOL                *Root;
  EFI_FILE_PROTOCOL                *KeyFile;
  CHAR16                           FullPath[256];
  EFI_FILE_INFO                    *FileInfo;
  UINTN                            FileInfoSize;
  UINTN                            ReadSize;
  CHAR8                            *Buffer;

  if (KeyFilename == NULL || FileContent == NULL || ContentSize == 0) {
    return EFI_INVALID_PARAMETER;
  }

  //
  // Get C drive file system
  //
  Status = GetCDriveFileSystem (&FileSystem);
  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_ERROR, "Failed to get C drive file system: %r\n", Status));
    return Status;
  }

  //
  // Open root directory
  //
  Status = FileSystem->OpenVolume (FileSystem, &Root);
  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_ERROR, "Failed to open root directory: %r\n", Status));
    return Status;
  }

  //
  // Construct full path: KeyFilename.vtkey
  //
  UnicodeSPrint (FullPath, sizeof (FullPath), L"%s.vtkey", KeyFilename);

  //
  // Open the key file
  //
  Status = Root->Open (
                   Root,
                   &KeyFile,
                   FullPath,
                   EFI_FILE_MODE_READ,
                   0
                   );
  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_ERROR, "Failed to open key file %s: %r\n", FullPath, Status));
    Root->Close (Root);
    return Status;
  }

  //
  // Get file information
  //
  FileInfoSize = sizeof (EFI_FILE_INFO) + 256;
  FileInfo = AllocatePool (FileInfoSize);
  if (FileInfo == NULL) {
    KeyFile->Close (KeyFile);
    Root->Close (Root);
    return EFI_OUT_OF_RESOURCES;
  }

  Status = KeyFile->GetInfo (KeyFile, &gEfiFileInfoGuid, &FileInfoSize, FileInfo);
  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_ERROR, "Failed to get file info: %r\n", Status));
    FreePool (FileInfo);
    KeyFile->Close (KeyFile);
    Root->Close (Root);
    return Status;
  }

  //
  // Check file size (should be reasonable for verification code)
  //
  if (FileInfo->FileSize == 0 || FileInfo->FileSize > 1024) {
    DEBUG ((DEBUG_ERROR, "Invalid key file size: %ld\n", FileInfo->FileSize));
    FreePool (FileInfo);
    KeyFile->Close (KeyFile);
    Root->Close (Root);
    return EFI_INVALID_PARAMETER;
  }

  //
  // Allocate buffer for file content
  //
  Buffer = AllocatePool ((UINTN) FileInfo->FileSize + 1);
  if (Buffer == NULL) {
    FreePool (FileInfo);
    KeyFile->Close (KeyFile);
    Root->Close (Root);
    return EFI_OUT_OF_RESOURCES;
  }

  //
  // Read file content
  //
  ReadSize = (UINTN) FileInfo->FileSize;
  Status = KeyFile->Read (KeyFile, &ReadSize, Buffer);
  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_ERROR, "Failed to read key file: %r\n", Status));
    FreePool (Buffer);
    FreePool (FileInfo);
    KeyFile->Close (KeyFile);
    Root->Close (Root);
    return Status;
  }

  //
  // Null terminate the buffer
  //
  Buffer[ReadSize] = '\0';

  //
  // Convert ASCII to Unicode
  //
  AsciiStrToUnicodeStrS (Buffer, FileContent, ContentSize / sizeof (CHAR16));

  //
  // Remove any trailing whitespace/newlines
  //
  UINTN Len = StrLen (FileContent);
  while (Len > 0 && (FileContent[Len - 1] == L'\r' || 
                     FileContent[Len - 1] == L'\n' || 
                     FileContent[Len - 1] == L' ' || 
                     FileContent[Len - 1] == L'\t')) {
    FileContent[--Len] = L'\0';
  }

  //
  // Clean up
  //
  FreePool (Buffer);
  FreePool (FileInfo);
  KeyFile->Close (KeyFile);
  Root->Close (Root);

  DEBUG ((DEBUG_INFO, "Successfully read key file content: %s\n", FileContent));
  return EFI_SUCCESS;
}

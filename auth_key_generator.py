#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Auth Wrapper 密钥生成工具

此工具用于生成与Auth Wrapper验证系统匹配的密钥文件。
基于USB硬件指纹生成密钥文件名和验证码。

Copyright (c) 2024, Auth Wrapper Project. All rights reserved.
SPDX-License-Identifier: BSD-2-Clause-Patent
"""

import os
import sys
import hashlib
import platform
import subprocess
from typing import List, Tuple, Optional

class AuthKeyGenerator:
    """Auth Wrapper密钥生成器"""
    
    # Base32字符集（与UEFI代码保持一致）
    BASE32_CHARSET = "0123456789ABCDEFGHJKLMNPQRSTUVWXYZ"
    
    # 盐值（与UEFI代码保持一致）
    SALT_STRING = "VTKEY_VERIFICATION_SALT_2024"
    
    def __init__(self):
        self.charset = self.BASE32_CHARSET
        self.salt = self.SALT_STRING.encode('utf-8')
    
    def get_usb_devices_windows(self) -> List[Tuple[str, str, str]]:
        """
        在Windows上获取USB设备信息
        返回: [(VendorID, ProductID, SerialNumber), ...]
        """
        devices = []
        
        try:
            # 使用wmic命令获取USB设备信息
            cmd = 'wmic path Win32_USBControllerDevice get Dependent /format:list'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    if 'USB\\VID_' in line:
                        # 解析设备ID: USB\VID_xxxx&PID_yyyy\SerialNumber
                        try:
                            device_id = line.split('=')[1].strip().strip('"')
                            if 'VID_' in device_id and 'PID_' in device_id:
                                parts = device_id.split('\\')
                                vid_pid_part = parts[1]  # VID_xxxx&PID_yyyy
                                serial_part = parts[2] if len(parts) > 2 else "UNKNOWN"
                                
                                vid = vid_pid_part.split('&')[0].replace('VID_', '')
                                pid = vid_pid_part.split('&')[1].replace('PID_', '')
                                
                                devices.append((vid, pid, serial_part))
                        except:
                            continue
            
            # 如果wmic失败，尝试使用PowerShell
            if not devices:
                cmd = 'powershell "Get-WmiObject -Class Win32_USBControllerDevice | ForEach-Object { $_.Dependent } | Where-Object { $_.DeviceID -like \'USB*\' } | Select-Object DeviceID"'
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines:
                        if 'USB\\VID_' in line:
                            try:
                                device_id = line.strip()
                                if 'VID_' in device_id and 'PID_' in device_id:
                                    parts = device_id.split('\\')
                                    vid_pid_part = parts[1]
                                    serial_part = parts[2] if len(parts) > 2 else "UNKNOWN"
                                    
                                    vid = vid_pid_part.split('&')[0].replace('VID_', '')
                                    pid = vid_pid_part.split('&')[1].replace('PID_', '')
                                    
                                    devices.append((vid, pid, serial_part))
                            except:
                                continue
        
        except Exception as e:
            print(f"获取USB设备信息时出错: {e}")
        
        return devices
    
    def get_usb_devices_linux(self) -> List[Tuple[str, str, str]]:
        """
        在Linux上获取USB设备信息
        返回: [(VendorID, ProductID, SerialNumber), ...]
        """
        devices = []
        
        try:
            # 使用lsusb命令
            result = subprocess.run(['lsusb', '-v'], capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                current_device = {}
                
                for line in lines:
                    line = line.strip()
                    if line.startswith('idVendor'):
                        vid = line.split()[1].replace('0x', '')
                        current_device['vid'] = vid
                    elif line.startswith('idProduct'):
                        pid = line.split()[1].replace('0x', '')
                        current_device['pid'] = pid
                    elif line.startswith('iSerial'):
                        serial = line.split(None, 2)[-1] if len(line.split()) > 2 else "UNKNOWN"
                        current_device['serial'] = serial
                        
                        # 如果有完整的设备信息，添加到列表
                        if 'vid' in current_device and 'pid' in current_device:
                            devices.append((
                                current_device['vid'].upper(),
                                current_device['pid'].upper(),
                                current_device.get('serial', 'UNKNOWN')
                            ))
                        current_device = {}
        
        except Exception as e:
            print(f"获取USB设备信息时出错: {e}")
        
        return devices
    
    def get_usb_devices(self) -> List[Tuple[str, str, str]]:
        """
        获取USB设备信息（跨平台）
        返回: [(VendorID, ProductID, SerialNumber), ...]
        """
        if platform.system() == "Windows":
            return self.get_usb_devices_windows()
        elif platform.system() == "Linux":
            return self.get_usb_devices_linux()
        else:
            print(f"不支持的操作系统: {platform.system()}")
            return []
    
    def generate_hardware_fingerprint(self, usb_devices: List[Tuple[str, str, str]]) -> bytes:
        """
        从USB设备信息生成硬件指纹
        """
        if not usb_devices:
            print("警告: 没有找到USB设备，使用系统信息作为备用")
            # 使用系统信息作为备用
            system_info = f"{platform.processor()}|{platform.system()}|{platform.machine()}|{platform.node()}"
            return hashlib.sha256(system_info.encode('utf-8')).digest()
        
        # 按VendorID:ProductID排序以确保一致性
        sorted_devices = sorted(usb_devices, key=lambda x: (x[0], x[1]))
        
        # 生成设备字符串
        device_strings = []
        for vid, pid, serial in sorted_devices:
            device_string = f"{vid}:{pid}:{serial}"
            device_strings.append(device_string)
        
        # 合并所有设备信息
        combined_string = ''.join(device_strings)
        
        print(f"硬件指纹输入: {combined_string}")
        
        # 生成SHA256哈希
        return hashlib.sha256(combined_string.encode('utf-8')).digest()
    
    def convert_to_base32(self, data: bytes, length: int) -> str:
        """
        将二进制数据转换为Base32字符串
        """
        result = []
        buffer = 0
        bits_in_buffer = 0
        
        for byte in data[:length]:
            buffer = (buffer << 8) | byte
            bits_in_buffer += 8
            
            while bits_in_buffer >= 5:
                index = (buffer >> (bits_in_buffer - 5)) & 0x1F
                result.append(self.charset[index])
                bits_in_buffer -= 5
        
        # 处理剩余的位
        if bits_in_buffer > 0:
            index = (buffer << (5 - bits_in_buffer)) & 0x1F
            result.append(self.charset[index])
        
        return ''.join(result)
    
    def generate_key_filename(self, hardware_fingerprint: bytes) -> str:
        """
        从硬件指纹生成密钥文件名（16字符）
        """
        # 对硬件指纹进行SHA256哈希
        filename_hash = hashlib.sha256(hardware_fingerprint).digest()
        
        # 转换前8字节为Base32（约16字符）
        filename = self.convert_to_base32(filename_hash, 8)
        
        # 确保正好16字符
        return filename[:16].ljust(16, '0')
    
    def generate_verification_code(self, hardware_fingerprint: bytes) -> str:
        """
        从硬件指纹生成验证码（32字符）
        """
        # 创建加盐的指纹
        salted_fingerprint = hardware_fingerprint + self.salt
        
        # 生成SHA256哈希
        verification_hash = hashlib.sha256(salted_fingerprint).digest()
        
        # 转换前16字节为Base32（约32字符）
        verification_code = self.convert_to_base32(verification_hash, 16)
        
        # 确保正好32字符
        return verification_code[:32].ljust(32, '0')
    
    def create_key_file(self, output_dir: str = "C:\\") -> Tuple[str, str]:
        """
        创建密钥文件
        返回: (文件名, 验证码)
        """
        print("=== Auth Wrapper 密钥生成工具 ===")
        print("正在扫描USB设备...")
        
        # 获取USB设备信息
        usb_devices = self.get_usb_devices()
        
        if usb_devices:
            print(f"发现 {len(usb_devices)} 个USB设备:")
            for i, (vid, pid, serial) in enumerate(usb_devices, 1):
                print(f"  {i}. VID:{vid}, PID:{pid}, SN:{serial}")
        else:
            print("未发现USB设备，将使用系统信息作为备用")
        
        # 生成硬件指纹
        print("\n正在生成硬件指纹...")
        hardware_fingerprint = self.generate_hardware_fingerprint(usb_devices)
        
        # 生成密钥文件名
        print("正在生成密钥文件名...")
        key_filename = self.generate_key_filename(hardware_fingerprint)
        
        # 生成验证码
        print("正在生成验证码...")
        verification_code = self.generate_verification_code(hardware_fingerprint)
        
        # 创建密钥文件
        full_filename = f"{key_filename}.vtkey"
        full_path = os.path.join(output_dir, full_filename)
        
        try:
            with open(full_path, 'w', encoding='utf-8') as f:
                f.write(verification_code)
            
            print(f"\n✓ 密钥文件创建成功!")
            print(f"  文件路径: {full_path}")
            print(f"  文件名: {full_filename}")
            print(f"  验证码: {verification_code}")
            
            return key_filename, verification_code
            
        except Exception as e:
            print(f"\n✗ 创建密钥文件失败: {e}")
            return "", ""
    
    def verify_key_file(self, key_filename: str, expected_code: str, file_dir: str = "C:\\") -> bool:
        """
        验证密钥文件是否正确
        """
        full_path = os.path.join(file_dir, f"{key_filename}.vtkey")
        
        try:
            with open(full_path, 'r', encoding='utf-8') as f:
                file_content = f.read().strip()
            
            if file_content == expected_code:
                print(f"✓ 密钥文件验证成功: {full_path}")
                return True
            else:
                print(f"✗ 密钥文件验证失败: {full_path}")
                print(f"  期望: {expected_code}")
                print(f"  实际: {file_content}")
                return False
                
        except Exception as e:
            print(f"✗ 读取密钥文件失败: {e}")
            return False

def main():
    """主函数"""
    generator = AuthKeyGenerator()
    
    if len(sys.argv) > 1:
        output_dir = sys.argv[1]
    else:
        output_dir = "C:\\"
    
    # 确保输出目录存在
    if not os.path.exists(output_dir):
        try:
            os.makedirs(output_dir)
        except Exception as e:
            print(f"无法创建输出目录 {output_dir}: {e}")
            return
    
    # 生成密钥文件
    key_filename, verification_code = generator.create_key_file(output_dir)
    
    if key_filename and verification_code:
        # 验证生成的文件
        print("\n正在验证生成的密钥文件...")
        generator.verify_key_file(key_filename, verification_code, output_dir)
        
        print(f"\n密钥文件已准备就绪，可以与Auth Wrapper配合使用。")
        print(f"请确保将 {key_filename}.vtkey 文件放置在C盘根目录下。")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()

@echo off
REM Auth Wrapper 部署脚本
REM 此脚本用于部署Auth Wrapper验证系统

echo ========================================
echo Auth Wrapper 部署脚本
echo ========================================

REM 检查必要文件
if not exist "AuthWrapper.efi" (
    echo 错误: 找不到AuthWrapper.efi文件
    echo 请先运行build_auth_wrapper.bat构建项目
    pause
    exit /b 1
)

if not exist "1BOOT.EFI" (
    echo 错误: 找不到1BOOT.EFI文件
    echo 请确保原始boot.efi已重命名为1BOOT.EFI
    pause
    exit /b 1
)

echo 找到必要文件:
echo   ✓ AuthWrapper.efi
echo   ✓ 1BOOT.EFI

REM 备份现有文件
echo.
echo 正在备份现有文件...

if exist "BOOT.EFI" (
    if not exist "BOOT.EFI.backup" (
        copy "BOOT.EFI" "BOOT.EFI.backup" >nul
        echo   ✓ 已备份 BOOT.EFI -> BOOT.EFI.backup
    ) else (
        echo   - BOOT.EFI.backup 已存在，跳过备份
    )
)

REM 部署Auth Wrapper
echo.
echo 正在部署Auth Wrapper...

copy "AuthWrapper.efi" "BOOT.EFI" >nul
if errorlevel 1 (
    echo   ✗ 部署失败: 无法复制AuthWrapper.efi到BOOT.EFI
    pause
    exit /b 1
) else (
    echo   ✓ 已部署 AuthWrapper.efi -> BOOT.EFI
)

REM 检查密钥生成工具
echo.
echo 正在检查密钥生成工具...

if not exist "auth_key_generator.py" (
    echo   ✗ 找不到auth_key_generator.py
    echo   请确保密钥生成工具存在
    pause
    exit /b 1
) else (
    echo   ✓ 找到密钥生成工具
)

REM 询问是否生成密钥文件
echo.
set /p generate_key="是否现在生成密钥文件? (y/n): "

if /i "%generate_key%"=="y" (
    echo.
    echo 正在运行密钥生成工具...
    python auth_key_generator.py
    
    if errorlevel 1 (
        echo   ✗ 密钥生成失败
        echo   请手动运行: python auth_key_generator.py
    ) else (
        echo   ✓ 密钥生成完成
    )
) else (
    echo.
    echo 跳过密钥生成。
    echo 请稍后手动运行: python auth_key_generator.py
)

REM 显示部署完成信息
echo.
echo ========================================
echo 部署完成！
echo ========================================
echo.
echo 当前文件状态:
echo   BOOT.EFI        - Auth Wrapper验证程序
echo   1BOOT.EFI       - 原始启动程序
echo   BOOT.EFI.backup - 原始文件备份
echo.
echo 使用说明:
echo 1. 确保C盘根目录有正确的.vtkey密钥文件
echo 2. 重启系统测试验证功能
echo 3. 如果验证失败，系统将拒绝启动
echo 4. 如需恢复，将BOOT.EFI.backup重命名为BOOT.EFI
echo.
echo 故障排除:
echo - 如果启动失败，检查密钥文件是否正确
echo - 如果USB设备变化，需要重新生成密钥文件
echo - 查看调试信息了解具体错误原因
echo.

pause

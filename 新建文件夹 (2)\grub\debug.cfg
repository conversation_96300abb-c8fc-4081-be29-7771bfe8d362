#
# VTLANG_CTRL_TEMP_SET must be the first
# And the inside item order can not be changed
#
submenu "$VTLANG_CTRL_TEMP_SET" --class=debug_temp_set --class=F5tool {
    menuentry "[ $VTOY_WIN11_BYPASS_CHECK ]  $VTLANG_WIN11_BYPASS_CHECK" --class=debug_temp_win11_bypass --class=debug_temp_set --class=F5tool \
        VTOY_RUN_SET0 {
        if [ "$VTOY_WIN11_BYPASS_CHECK" = "0" ]; then
            set VTOY_WIN11_BYPASS_CHECK=1
        else
            set VTOY_WIN11_BYPASS_CHECK=0
        fi
    }
    
    menuentry "[ $VTOY_WIN11_BYPASS_NRO ]  $VTLANG_WIN11_BYPASS_NRO" --class=debug_temp_win11_nro --class=debug_temp_set --class=F5tool \
        VTOY_RUN_SET1 {
        if [ "$VTOY_WIN11_BYPASS_NRO" = "0" ]; then
            set VTOY_WIN11_BYPASS_NRO=1
        else
            set VTOY_WIN11_BYPASS_NRO=0
        fi
    }    
    
    menuentry "[ $VTOY_SECONDARY_BOOT_MENU ]  $VTLANG_SECONDARY_BOOT_MENU" --class=debug_temp_second_menu --class=debug_temp_set --class=F5tool \
        VTOY_RUN_SET3 {
        if [ "$VTOY_SECONDARY_BOOT_MENU" = "0" ]; then
            set VTOY_SECONDARY_BOOT_MENU=1
        else
            set VTOY_SECONDARY_BOOT_MENU=0
        fi
    }

    menuentry "$VTLANG_RETURN_PREVIOUS" --class=vtoyret VTOY_RET {
        echo "Return ..."
    }
}


submenu "$VTLANG_MENU_LANG" --class=debug_menu_lang --class=F5tool {
    source $prefix/menulang.cfg
}

source $prefix/power.cfg
source $prefix/hwinfo.cfg
source $prefix/keyboard.cfg

submenu "$VTLANG_RESOLUTION_CFG" --class=debug_resolution --class=F5tool {
    menuentry "$VTLANG_RETURN_PREVIOUS" --class=vtoyret VTOY_RET {
        echo "Return ..."
    }
    
    vt_update_cur_video_mode VT_CUR_MODE
    set vdid=0
    while [ $vdid -lt $VTOY_VIDEO_MODE_NUM ]; do
        vt_get_video_mode $vdid vtCurMode
        
        unset vtActive
        if [ "$vtCurMode" = "$VT_CUR_MODE" ]; then
            set vtActive="[*]"
        fi
        
        menuentry "$vtCurMode $vtActive" --class=debug_videomode --class=debug_resolution --class=F5tool VTOY_RUN_RET {
            terminal_output console
            set gfxmode=$1
            terminal_output gfxterm            
            if [ "$vtoy_res_fit" = "1" ]; then
                vt_set_theme switch
            fi
        }

        vt_incr vdid 1
    done
}

submenu "$VTLANG_SCREEN_MODE" --class=debug_screen_mode --class=F5tool {
    menuentry "$VTLANG_SCREEN_TEXT_MODE" --class=debug_text_mode --class=debug_screen_mode --class=F5tool {
        vt_push_menu_lang en_US
        terminal_output console
    }
    menuentry "$VTLANG_SCREEN_GUI_MODE" --class=debug_gui_mode --class=debug_screen_mode --class=F5tool {
        terminal_output gfxterm
        vt_pop_menu_lang
    }
    menuentry "$VTLANG_RETURN_PREVIOUS" --class=vtoyret VTOY_RET {
        echo "Return ..."
    }
}

if [ -n "$VTOY_THEME_COUNT" -a $VTOY_THEME_COUNT -gt 1 ]; then
    submenu "$VTLANG_THEME_SELECT" --class=debug_theme_select --class=F5tool {
        vt_select_theme_cfg
    }
fi

if [ "$grub_platform" != "pc" ]; then
    submenu "$VTLANG_UEFI_UTIL" --class=debug_util --class=F5tool {
        menuentry "$VTLANG_UTIL_SHOW_EFI_DRV" --class=debug_util_efidrv --class=debug_util --class=F5tool {
            vt_push_pager
            chainloader ${vtoy_path}/vtoyutil_${VTOY_EFI_ARCH}.efi env_param=${env_param} ${vtdebug_flag} feature=show_efi_drivers
            boot
            vt_pop_pager
            echo -en "\n$VTLANG_ENTER_EXIT ..."
            read vtInputKey
        }
        
        menuentry "$VTLANG_UTIL_FIX_BLINIT_FAIL" --class=debug_util_blinit --class=debug_util --class=F5tool {
            chainloader ${vtoy_path}/vtoyutil_${VTOY_EFI_ARCH}.efi env_param=${env_param} ${vtdebug_flag} feature=fix_windows_mmap
            boot
            echo -en "\n$VTLANG_ENTER_EXIT ..."
            read vtInputKey
        }

        menuentry "$VTLANG_RETURN_PREVIOUS" --class=vtoyret VTOY_RET {
            echo "Return ..."
        }
    }
fi


submenu "$VTLANG_JSON_CHK_JSON" --class=debug_json --class=F5tool {
    menuentry "$VTLANG_JSON_CHK_CONTROL" --class=debug_control --class=debug_json --class=F5tool {
        set pager=1
        vt_check_plugin_json $vt_plugin_path control $vtoy_iso_part
        
        echo -en "\n$VTLANG_ENTER_EXIT ..."
        read vtInputKey
        unset pager
    }
    
    menuentry "$VTLANG_JSON_CHK_THEME" --class=debug_theme --class=debug_json --class=F5tool {
        set pager=1
        vt_check_plugin_json $vt_plugin_path theme $vtoy_iso_part
        
        echo -en "\n$VTLANG_ENTER_EXIT ..."
        read vtInputKey
        unset pager
    }
    
    menuentry "$VTLANG_JSON_CHK_AUTOINS" --class=debug_autoinstall --class=debug_json --class=F5tool {
        set pager=1
        vt_check_plugin_json $vt_plugin_path auto_install $vtoy_iso_part
        
        echo -en "\n$VTLANG_ENTER_EXIT ..."
        read vtInputKey
        unset pager
    }
    
    menuentry "$VTLANG_JSON_CHK_PERSIST" --class=debug_persistence --class=debug_json --class=F5tool {
        set pager=1
        vt_check_plugin_json $vt_plugin_path persistence $vtoy_iso_part
        
        echo -e "\n############### dump persistence ###############"
        vt_dump_persistence
        
        echo -en "\n$VTLANG_ENTER_EXIT ..."
        read vtInputKey
        unset pager
    }
    
    menuentry "$VTLANG_JSON_CHK_MENU_ALIAS" --class=debug_menualias --class=debug_json --class=F5tool {
        set pager=1
        vt_check_plugin_json $vt_plugin_path menu_alias $vtoy_iso_part
        
        echo -en "\n$VTLANG_ENTER_EXIT ..."
        read vtInputKey
        unset pager
    }
    
    menuentry "$VTLANG_JSON_CHK_MENU_TIP" --class=debug_menutip --class=debug_json --class=F5tool {
        set pager=1
        vt_check_plugin_json $vt_plugin_path menu_tip $vtoy_iso_part
        
        echo -en "\n$VTLANG_ENTER_EXIT ..."
        read vtInputKey
        unset pager
    }
    
    menuentry "$VTLANG_JSON_CHK_MENU_CLASS" --class=debug_menuclass --class=debug_json --class=F5tool {
        set pager=1
        vt_check_plugin_json $vt_plugin_path menu_class $vtoy_iso_part
        
        echo -en "\n$VTLANG_ENTER_EXIT ..."
        read vtInputKey
        unset pager
    }  

    menuentry "$VTLANG_JSON_CHK_INJECTION" --class=debug_injection --class=debug_json --class=F5tool {
        set pager=1
        vt_check_plugin_json $vt_plugin_path injection $vtoy_iso_part
        
        echo -en "\n$VTLANG_ENTER_EXIT ..."
        read vtInputKey
        unset pager
    } 

    menuentry "$VTLANG_JSON_CHK_AUTO_MEMDISK" --class=debug_automemdisk --class=debug_json --class=F5tool {
        set pager=1
        vt_check_plugin_json $vt_plugin_path auto_memdisk $vtoy_iso_part
        
        echo -en "\n$VTLANG_ENTER_EXIT ..."
        read vtInputKey
        unset pager
    }  

    menuentry "$VTLANG_JSON_CHK_IMG_LIST" --class=debug_imagelist --class=debug_json --class=F5tool {
        set pager=1
        vt_check_plugin_json $vt_plugin_path image_list $vtoy_iso_part
        
        echo -en "\n$VTLANG_ENTER_EXIT ..."
        read vtInputKey
        unset pager
    }
    
    menuentry "$VTLANG_JSON_CHK_IMG_BLIST" --class=debug_imageblacklist --class=debug_json --class=F5tool {
        set pager=1
        vt_check_plugin_json $vt_plugin_path image_blacklist $vtoy_iso_part
        
        echo -en "\n$VTLANG_ENTER_EXIT ..."
        read vtInputKey
        unset pager
    }
    
    menuentry "$VTLANG_JSON_CHK_CONF_REPLACE" --class=debug_bootconf_replace --class=debug_json --class=F5tool {
        set pager=1
        vt_check_plugin_json $vt_plugin_path conf_replace $vtoy_iso_part
        
        echo -en "\n$VTLANG_ENTER_EXIT ..."
        read vtInputKey
        unset pager
    }
    
    menuentry "$VTLANG_JSON_CHK_DUD" --class=debug_dud --class=debug_json --class=F5tool {
        set pager=1
        vt_check_plugin_json $vt_plugin_path dud $vtoy_iso_part
        
        echo -en "\n$VTLANG_ENTER_EXIT ..."
        read vtInputKey
        unset pager
    }
    
    menuentry "$VTLANG_JSON_CHK_PASSWORD" --class=debug_pwd --class=debug_json --class=F5tool {
        set pager=1
        vt_check_plugin_json $vt_plugin_path password $vtoy_iso_part
        
        echo -en "\n$VTLANG_ENTER_EXIT ..."
        read vtInputKey
        unset pager
    }
    
    menuentry "$VTLANG_RETURN_PREVIOUS" --class=vtoyret VTOY_RET {
        echo "Return ..."
    }
}


menuentry "$VTLANG_RETURN_PREVIOUS" --class=vtoyret VTOY_RET {
    echo "Return ..."
}

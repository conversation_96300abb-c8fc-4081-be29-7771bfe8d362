## @file
# Download iasl executable tool from a nuget.org package
# - package contains different binaries based on host
# Add the folder with the tool to the path
#
# This is only downloaded for scope armvirt thus
# should have no impact on the asl compiler used by any
# other platform build
#
# Copyright (c) Microsoft Corporation.
# SPDX-License-Identifier: BSD-2-Clause-Patent
##
{
  "id": "iasl-armvirt-1",
  "scope": "armvirt",
  "type": "nuget",
  "name": "iasl",
  "source": "https://api.nuget.org/v3/index.json",
  "version": "20190215.0.0",
  "flags": ["set_path", "host_specific"],
}

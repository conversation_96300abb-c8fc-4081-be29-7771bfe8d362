#/** @file
#
#  Copyright (c) 2014, Linaro Limited. All rights reserved.
#  Copyright (c) 2020, ARM Limited. All rights reserved.
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#**/

[Defines]
  DEC_SPECIFICATION              = 0x00010005
  PACKAGE_NAME                   = ArmVirtPkg
  PACKAGE_GUID                   = A0B31216-508E-4025-BEAB-56D836C66F0A
  PACKAGE_VERSION                = 0.1

################################################################################
#
# Include Section - list of Include Paths that are provided by this package.
#                   Comments are used for Keywords and Module Types.
#
# Supported Module Types:
#  BASE SEC PEI_CORE PEIM DXE_CORE DXE_DRIVER DXE_RUNTIME_DRIVER DXE_SMM_DRIVER DXE_SAL_DRIVER UEFI_DRIVER UEFI_APPLICATION
#
################################################################################
[Includes.common]
  Include                        # Root include for the package

[LibraryClasses]
  ArmVirtMemInfoLib|Include/Library/ArmVirtMemInfoLib.h

[Guids.common]
  gArmVirtTokenSpaceGuid = { 0x0B6F5CA7, 0x4F53, 0x445A, { 0xB7, 0x6E, 0x2E, 0x36, 0x5B, 0x80, 0x63, 0x66 } }
  gEarlyPL011BaseAddressGuid       = { 0xB199DEA9, 0xFD5C, 0x4A84, { 0x80, 0x82, 0x2F, 0x41, 0x70, 0x78, 0x03, 0x05 } }
  gEarly16550UartBaseAddressGuid   = { 0xea67ca3e, 0x1f54, 0x436b, { 0x97, 0x88, 0xd4, 0xeb, 0x29, 0xc3, 0x42, 0x67 } }
  gArmVirtSystemMemorySizeGuid     = { 0x504eccb9, 0x1bf0, 0x4420, { 0x86, 0x5d, 0xdc, 0x66, 0x06, 0xd4, 0x13, 0xbf } }

[PcdsFeatureFlag]
  #
  # Feature Flag PCD that defines whether TPM2 support is enabled
  #
  gArmVirtTokenSpaceGuid.PcdTpm2SupportEnabled|FALSE|BOOLEAN|0x00000004

[PcdsFixedAtBuild, PcdsPatchableInModule]
  ##
  # This is the physical address of Rsdp which is the core struct of Acpi.
  # Cloud Hypervisor has no other way to pass Rsdp address to the guest except use a PCD.
  #
  gArmVirtTokenSpaceGuid.PcdCloudHvAcpiRsdpBaseAddress|0x0|UINT64|0x00000005

// /** @file
// ACPI CPU Data initialization module
//
// This module initializes the ACPI_CPU_DATA structure and registers the address
// of this structure in the PcdCpuS3DataAddress PCD.  This is a generic/simple
// version of this module.  It does not provide a machine check handler or CPU
// register initialization tables for ACPI S3 resume.  It also only supports the
// number of CPUs reported by the MP Services Protocol, so this module does not
// support hot plug CPUs.  This module can be copied into a CPU specific package
// and customized if these additional features are required.
//
// Copyright (c) 2015, Intel Corporation. All rights reserved.<BR>
//
// Copyright (c) 2015, Red Hat, Inc.
//
// SPDX-License-Identifier: BSD-2-Clause-Patent
//
// **/

#string STR_MODULE_ABSTRACT
#language en-US
"ACPI CPU Data initialization module"

#string STR_MODULE_DESCRIPTION
#language en-US
"This module initializes the ACPI_CPU_DATA structure and registers the address "
"of this structure in the PcdCpuS3DataAddress PCD.  This is a generic/simple "
"version of this module.  It does not provide a machine check handler or CPU "
"register initialization tables for ACPI S3 resume.  It also only supports the "
"number of CPUs reported by the MP Services Protocol, so this module does not "
"support hot plug CPUs.  This module can be copied into a CPU specific package "
"and customized if these additional features are required."



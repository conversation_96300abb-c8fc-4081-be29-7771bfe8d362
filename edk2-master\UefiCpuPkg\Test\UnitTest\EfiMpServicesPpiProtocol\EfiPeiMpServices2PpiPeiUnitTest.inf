## @file
# PEIM that unit tests the EfiPeiMpServices2Ppi
#
# Copyright (c) 2022, Intel Corporation. All rights reserved.<BR>
# SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION     = 0x00010005
  BASE_NAME       = EfiPeiMpServices2PpiPeiUnitTest
  FILE_GUID       = ********-4D1E-445E-BD6F-F6821B852B5D
  MODULE_TYPE     = PEIM
  VERSION_STRING  = 1.0
  ENTRY_POINT     = PeiEntryPoint

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources]
  EfiMpServicesUnitTestCommom.c
  EfiMpServicesUnitTestCommom.h
  EfiPeiMpServices2PpiUnitTest.c

[Packages]
  MdePkg/MdePkg.dec
  UefiCpuPkg/UefiCpuPkg.dec

[LibraryClasses]
  BaseLib
  DebugLib
  BaseMemoryLib
  MemoryAllocationLib
  PeimEntryPoint
  PeiServicesLib
  UnitTestPersistenceLib
  UnitTestLib

[Ppis]
  gEfiPeiMpServices2PpiGuid           ## CONSUMES

[Depex]
  gEfiPeiMpServices2PpiGuid

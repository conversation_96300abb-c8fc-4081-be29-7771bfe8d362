## @file
# RISC-V CPU Exception Handler Library
#
# Copyright (c) 2022-2023, Ventana Micro Systems Inc. All rights reserved.<BR>
#
# SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x0001001B
  BASE_NAME                      = BaseRiscV64CpuExceptionHandlerLib
  MODULE_UNI_FILE                = BaseRiscV64CpuExceptionHandlerLib.uni
  FILE_GUID                      = 6AB0D5FD-E615-45A3-9374-E284FB061FC9
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = CpuExceptionHandlerLib

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = RISCV64
#

[Sources]
  SupervisorTrapHandler.S
  CpuExceptionHandlerLib.c
  CpuExceptionHandlerLib.h

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  UefiCpuPkg/UefiCpuPkg.dec

[LibraryClasses]
  BaseLib
  SerialPortLib
  PrintLib
  SynchronizationLib
  PeCoffGetEntryPointLib
  MemoryAllocationLib
  DebugLib

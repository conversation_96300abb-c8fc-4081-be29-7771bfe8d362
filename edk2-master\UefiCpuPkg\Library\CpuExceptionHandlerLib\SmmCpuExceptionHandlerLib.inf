## @file
#  CPU Exception Handler library instance for SMM modules.
#
#  Copyright (c) 2013 - 2022, Intel Corporation. All rights reserved.<BR>
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = SmmCpuExceptionHandlerLib
  MODULE_UNI_FILE                = SmmCpuExceptionHandlerLib.uni
  FILE_GUID                      = 8D2C439B-3981-42ff-9CE5-1B50ECA502D6
  MODULE_TYPE                    = DXE_SMM_DRIVER
  VERSION_STRING                 = 1.1
  LIBRARY_CLASS                  = CpuExceptionHandlerLib|DXE_SMM_DRIVER MM_STANDALONE MM_CORE_STANDALONE

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources.Ia32]
  Ia32/ArchExceptionHandler.c
  Ia32/ArchInterruptDefs.h
  Ia32/ExceptionHandlerAsm.nasm
  Ia32/ExceptionTssEntryAsm.nasm

[Sources.X64]
  X64/ArchExceptionHandler.c
  X64/ArchInterruptDefs.h
  X64/ExceptionHandlerAsm.nasm

[Sources.common]
  CpuExceptionCommon.h
  CpuExceptionCommon.c
  PeiDxeSmmCpuException.c
  SmmException.c

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  UefiCpuPkg/UefiCpuPkg.dec

[LibraryClasses]
  BaseLib
  CcExitLib
  DebugLib
  LocalApicLib
  PeCoffGetEntryPointLib
  PrintLib
  SerialPortLib
  SynchronizationLib

[Pcd]
  gEfiMdeModulePkgTokenSpaceGuid.PcdCpuStackGuard
  gUefiCpuPkgTokenSpaceGuid.PcdCpuStackSwitchExceptionList
  gUefiCpuPkgTokenSpaceGuid.PcdCpuKnownGoodStackSize

[FeaturePcd]
  gUefiCpuPkgTokenSpaceGuid.PcdCpuSmmStackGuard                    ## CONSUMES

[BuildOptions]
  XCODE:*_*_X64_NASM_FLAGS = -D NO_ABSOLUTE_RELOCS_IN_TEXT

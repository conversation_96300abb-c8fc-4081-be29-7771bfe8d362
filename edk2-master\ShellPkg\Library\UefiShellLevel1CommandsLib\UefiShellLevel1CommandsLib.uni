// /**
//
// (C) Copyright 2014-2015 Hewlett-Packard Development Company, L.P.<BR>
// Copyright (c) 2009 - 2011, Intel Corporation. All rights reserved.<BR>
// SPDX-License-Identifier: BSD-2-Clause-Patent
//
// Module Name:
//
// UefiShellLevel2CommandsLib.uni
//
// Abstract:
//
// String definitions for UEFI Shell 2.0 level 1 commands
//
//
// **/

/=#

#langdef   en-US "english"

#string STR_NO_SCRIPT             #language en-US "The command '%H%s%N' is incorrect outside of a script\r\n"
#string STR_GEN_PROBLEM           #language en-US "%H%s%N: Unknown flag - '%H%s%N'\r\n"
#string STR_GEN_PROBLEM_VAL       #language en-US "%H%s%N: Bad value - '%H%s%N' for flag - '%H%s%N'\r\n"
#string STR_GEN_PROBLEM_SCRIPT    #language en-US "The argument '%B%s%N' is incorrect. Line: %d\r\n"
#string STR_GEN_INV_VAR           #language en-US "The script's Indexvar '%B%s%N' is incorrect\r\n"
#string STR_GEN_TOO_FEW           #language en-US "%H%s%N: Too few arguments\r\n"
#string STR_GEN_TOO_MANY          #language en-US "%H%s%N: Too many arguments\r\n"
#string STR_GEN_PARAM_INV         #language en-US "%H%s%N: Invalid argument - '%H%s%N'\r\n"

#string STR_TEXT_AFTER_THEN       #language en-US "%H%s%N: Then cannot be followed by anything\r\n"
#string STR_SYNTAX_AFTER_BAD      #language en-US "%H%s%N: Syntax after '%H%s%N' is incorrect\r\n"
#string STR_SYNTAX_IN             #language en-US "Syntax after analyzing %s\r\n"
#string STR_SYNTAX_NO_MATCHING    #language en-US "No matching '%H%s%N' for '%H%s%N' statement found. Line: %d\r\n"
#string STR_INVALID_BINOP         #language en-US "Binary operator not found first in '%H%s%N'\r\n"
#string STR_SYNTAX_STARTING       #language en-US "Syntax after %s\r\n"

#string STR_STALL_FAILED          #language en-US "%H%s%N: BootService Stall() failed\r\n"

#string STR_GET_HELP_EXIT         #language en-US ""
".TH exit 0 "exits the script or shell"\r\n"
".SH NAME\r\n"
"Exits the UEFI Shell or the current script.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"EXIT [/b] [exit-code]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  /b        - Indicates that only the current UEFI shell script should be\r\n"
"              terminated. Ignored if not used within a script.\r\n"
"  exit-code - If exiting a UEFI shell script, the value that will be placed\r\n"
"              into the environment variable lasterror. If exiting an instance\r\n"
"              of the UEFI shell, the value that will be returned to the\r\n"
"              caller. If not specified, then 0 will be returned.\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. This command exits the UEFI Shell or, if /b is specified, the current\r\n"
"     script.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To exit shell successfully:\r\n"
"    Shell> exit\r\n"
" \r\n"
"  * To exit the current UEFI shell script:\r\n"
"    Shell> exit /b \r\n"
" \r\n"
"  * To exit a UEFI shell script with exit-code value returned to the caller:\r\n"
"    Shell> exit 0\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  0          Exited normally\r\n"
"  exit-code  The return value specified as an option.\r\n"

#string STR_GET_HELP_FOR          #language en-US ""
".TH for 0 "starts a for loop"\r\n"
".SH NAME\r\n"
"Starts a loop based on 'for' syntax.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"FOR %indexvar IN set\r\n"
"    command [arguments]\r\n"
"    [command [arguments]]\r\n"
"    ...\r\n"
"ENDFOR\r\n"
" \r\n"
"FOR %indexvar RUN (start end [step])\r\n"
"    command [arguments]\r\n"
"    [command [arguments]]\r\n"
"    ...\r\n"
"ENDFOR\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  %indexvar           - Variable name used to index a set\r\n"
"  set                 - Set to be searched\r\n"
"  command [arguments] - Command to be executed with optional arguments\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. The FOR command executes one or more commands for each item in a set of\r\n"
"     items. The set may be text strings or filenames or a mixture of both,\r\n"
"     separated by spaces (if not in a quotation).\r\n"
"  2. If the length of an element in the set is between 0 and 256, and if the\r\n"
"     string contains wildcards, the string will be treated as a file name\r\n"
"     containing wildcards, and be expanded before command is executed.\r\n"
"  3. If after expansion no such files are found, the literal string itself is\r\n"
"     kept. %indexvar is any alphabet character from 'a' to 'z' or 'A' to 'Z',\r\n"
"     and they are case sensitive. It should not be a digit (0-9) because\r\n"
"     %digit will be interpreted as a positional argument on the command line\r\n"
"     that launches the script. The namespace for index variables is separate\r\n"
"     from that for environment variables, so if %indexvar has the same name as\r\n"
"     an existing environment variable, the environment variable will remain\r\n"
"     unchanged by the FOR loop.\r\n"
"  4. Each command is executed once for each item in the set, with any\r\n"
"     occurrence of %indexvar in the command replacing with the current item.\r\n"
"     In the second format of FOR ... ENDFOR statement, %indexvar will be\r\n"
"     assigned a value from start to end with an interval of step. Start and\r\n"
"     end can be any integer whose length is less than 7 digits excluding sign,\r\n"
"     and it can also applied to step with one exception of zero. Step is\r\n"
"     optional, if step is not specified it will be automatically determined by\r\n"
"     following rule:\r\n"
"       if start <= end then step = 1, otherwise step = -1.\r\n"
"     start, end and step are divided by space.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * Sample FOR loop - listing all .txt files:\r\n"
"    echo -off\r\n"
"    for %a in *.txt\r\n"
"      echo %a exists\r\n"
"    endfor\r\n"
" \r\n"
"    # \r\n"
"    # If in current directory, there are 2 files named file1.txt and file2.txt\r\n"
"    # then the output of the sample script will be as shown below.\r\n"
"    # \r\n"
"    Sample1> echo -off\r\n"
"    file1.txt exists\r\n"
"    file2.txt exists\r\n"
" \r\n"
"  * Theoretically it is legal for 2 nested FOR commands to use the same\r\n"
"    alphabet letter as their index variable, for instance, a: \r\n"
"    #\r\n"
"    # Sample FOR loop from 1 to 3 with step 1\r\n"
"    #\r\n"
"    echo -off\r\n"
"    for %a run (1 3)\r\n"
"      echo %a\r\n"
"    endfor\r\n"
" \r\n"
"    #\r\n"
"    # Sample FOR loop from 3 down to 1 with step -1\r\n"
"    #\r\n"
"    echo -off\r\n"
"    for %a run (3 1 -1)\r\n"
"      echo %a\r\n"
"    endfor\r\n"
" \r\n"
"    #\r\n"
"    # Sample FOR loop - 2 nested for using same index variable\r\n"
"    #\r\n"
"    echo -off\r\n"
"    for %a in value1 value2\r\n"
"      for %a in value3 value4\r\n"
"        echo %a\r\n"
"      endfor\r\n"
"    endfor\r\n"
" \r\n"
"    Note: When processing first FOR and before seeing the ENDFOR, the index\r\n"
"          variable %a has the value "value1", so in second FOR, the %a has\r\n"
"          been already defined and it will be replaced with the current value\r\n"
"          of %a. The string after substitution becomes FOR value1 in value3\r\n"
"          value4, which is not a legal FOR command. Thus only when the value\r\n"
"          of %a is also a single alphabet letter, the script will be executed\r\n"
"          without error. If 2 independent FOR commands use the same index\r\n"
"          variable, when the second FOR is encountered, the first FOR has\r\n"
"          already freed the variable so there will be no problem in this case.\r\n"

#string STR_GET_HELP_ENDFOR       #language en-US ""
".TH endfor 0 "ends a for loop"\r\n"
".SH NAME\r\n"
"Ends a 'for' loop.\r\n"
".SH SYNOPSIS\r\n"
"See 'for' for usage.\r\n"
".SH EXAMPLES\r\n"
"See 'for' for examples.\r\n"

#string STR_GET_HELP_GOTO         #language en-US ""
".TH goto 0 "moves to a label"\r\n"
".SH NAME\r\n"
"Moves around the point of execution in a script.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"GOTO label\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  label - Specifies a location in batch file\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. The GOTO command directs script file execution to the line in the script\r\n"
"     file after the given label. The command is not supported from the\r\n"
"     interactive shell.\r\n"
"  2. A label is a line beginning with a colon (:). It can appear either after\r\n"
"     the GOTO command, or before the GOTO command. The search for label is\r\n"
"     done forward in the script file, from the current file position. If the\r\n"
"     end of the file is reached, the search resumes at the top of the file and\r\n"
"     continues until label is found or the starting point is reached. If label\r\n"
"     is not found, the script process terminates and an error message is\r\n"
"     displayed. If a label is encountered but there is no GOTO command\r\n"
"     executed, the label lines are ignored.\r\n"
"  3. Using GOTO command to jump into another for loop is not allowed,\r\n"
"     but jumping into an if statement is legal.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * This is a script:\r\n"
"    goto Done\r\n"
"    ...\r\n"
"    :Done\r\n"
"    cleanup.nsh\r\n"

#string STR_GET_HELP_ENDIF        #language en-US ""
".TH endif 0 "ends an if block"\r\n"
".SH NAME\r\n"
"Ends the block of a script controlled by an 'if' statement.\r\n"
".SH SYNOPSIS\r\n"
"See 'if' for usage.\r\n"
".SH EXAMPLES\r\n"
"See 'if' for examples.\r\n"

#string STR_GET_HELP_IF           #language en-US ""
".TH if 0 "controls the execution of a block of a script"\r\n"
".SH NAME\r\n"
"Executes commands in specified conditions.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"IF [NOT] EXIST filename THEN\r\n"
"  command [arguments]\r\n"
"  [command [arguments]]\r\n"
"  ...\r\n"
"[ELSE\r\n"
"  command [arguments]\r\n"
"  [command [arguments]]\r\n"
"  ...\r\n"
"  ]\r\n"
"ENDIF\r\n"
" \r\n"
"IF [/i] [NOT] string1 == string2 THEN\r\n"
"  command [arguments]\r\n"
"  [command [arguments]]\r\n"
"  ...\r\n"
"[ELSE\r\n"
"  command [arguments]\r\n"
"  [command [arguments]]\r\n"
"  ...\r\n"
"  ]\r\n"
"ENDIF\r\n"
"if [/i][/s] ConditionalExpression THEN\r\n"
"  command [arguments]\r\n"
"  [command [arguments]]\r\n"
"  ...\r\n"
"[ELSE\r\n"
"  command [arguments]\r\n"
"  [command [arguments]]\r\n"
"  ...\r\n"
"  ]\r\n"
"ENDIF\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. The IF command executes one or more commands before the ELSE or ENDIF\r\n"
"     commands, if the specified condition is TRUE; otherwise commands between\r\n"
"     ELSE (if present) and ENDIF are executed.\r\n"
"  2. In the first usage of IF, the EXIST condition is true when the file\r\n"
"     specified by filename exists. The filename argument may include device\r\n"
"     and path information. Also wildcard expansion is supported by this form.\r\n"
"     If more than one file matches the wildcard pattern, the condition\r\n"
"     evaluates to TRUE.\r\n"
"  3. In the second usage, the string1 == string2 condition is TRUE if the two\r\n"
"     strings are identical. Here the comparison can be case sensitive or\r\n"
"     insensitive, it depends on the optional switch /i. If /i is specified,\r\n"
"     it will compare strings in the case insensitive manner; otherwise, it\r\n"
"     compares strings in the case sensitive manner.\r\n"
"  4. In the third usage, general purpose comparison is supported using\r\n"
"     expressions optionally separated by AND or OR. Since < and > are used for\r\n"
"     redirection, the expressions use common two character (FORTRAN)\r\n"
"     abbreviations for the operators (augmented with unsigned equivalents):\r\n"
"     - Expressions : Conditional expressions are evaluated strictly from left\r\n"
"                     to right. Complex conditionals requiring precedence may\r\n"
"                     be implemented as nested IFs.\r\n"
"                     The expressions used in the third usage can have the\r\n"
"                     following syntax:\r\n"
"                     conditional-expression := expression |\r\n"
"                                               expression and expression |\r\n"
"                                               expression or expression\r\n"
"                     expression := expr | not expr\r\n"
"                     expr := item binop item | boolfunc(string)\r\n"
"                     item := mapfunc(string) | string\r\n"
"                     mapfunc := efierror | pierror | oemerror\r\n"
"                     boolfunc := isint | exists | available | profile\r\n"
"                     binop := gt | lt | eq | ne | ge | le | == | ugt | ult |\r\n"
"                              uge | ule\r\n"
"     - Comparisons : By default, comparisons are done numerically if the\r\n"
"                     strings on both sides of the operator are numbers\r\n"
"                     (as defined below) and in case sensitive character sort\r\n"
"                     order otherwise. Spaces separate the operators from\r\n"
"                     operands.\r\n"
"  5. The /s option forces string comparisons and the /i option forces\r\n"
"     case-insensitive string comparisons. If either of these is used, the\r\n"
"     signed or unsigned versions of the operators have the same results.\r\n"
"     The /s and /i apply to the entire line and must appear at the start of\r\n"
"     the line (just after the if itself). The two may appear in either order.\r\n"
"  6. When performing comparisons, the Unicode Byte Ordering Character is\r\n"
"     ignored at the beginning of any argument.\r\n"
"  7. Comparison Operator Definition:\r\n"
"       gt  : Greater than\r\n"
"       ugt : Unsigned Greater than\r\n"
"       lt  : Less than\r\n"
"       ult : Unsigned Less than\r\n"
"       ge  : Greater than or equal\r\n"
"       uge : Unsigned greater than or equal\r\n"
"       le  : Less than or equal\r\n"
"       ule : Unsigned less than or equal\r\n"
"       ne  : Not equal\r\n"
"       eq  : Equals (semantically equivalent to ==)\r\n"
"       ==  : Equals (semantically equivalent to eq)\r\n"
"  8. Error Mapping Functions are used to convert integers into UEFI, PI or OEM\r\n"
"     error codes.\r\n"
"     Functions used to convert integers into UEFI, PI or OEM error codes:\r\n"
"       UefiError : Sets top nibble of parameter to 1000 binary (0x8)\r\n"
"       PiError   : Sets top nibble of parameter to 1010 binary (0xA)\r\n"
"       OemError  : Sets top nibble of parameter to 1100 binary (0xC)\r\n"
"     Each function maps the small positive parameter into its equivalent error\r\n"
"     classification.\r\n"
"     For example:\r\n"
"      if %lasterror% == EfiError(8) then # Check for write protect.\r\n"
"      ...\r\n"
"  9. Boolean Functions may only be used to modify operators in comparisons.\r\n"
"     The following built-in Boolean functions are also available:\r\n"
"       IsInt     : Evaluates to true if the parameter string that follows\r\n"
"                   is a number (as defined below) and false otherwise.\r\n"
"       Exists    : Evaluates to true if the file specified by string exists\r\n"
"                   is in the current working directory or false if not.\r\n"
"       Available : Evaluates to true if the file specified by string is in the\r\n"
"                   current working directory or current path.\r\n"
"       Profile   : Determines whether the parameter string matches one of the\r\n"
"                   profile names in the profiles environment variable.\r\n"
"  10. No spaces are allowed between function names and the open parenthesis,\r\n"
"      between the open parenthesis and the string or between the string and\r\n"
"      the closed parenthesis. Constant strings containing spaces must be\r\n"
"      quoted.\r\n"
"  11. To avoid ambiguity and current or future incompatibility, users are\r\n"
"      strongly encouraged to surround constant strings that contain\r\n"
"      parenthesis with quotes in if statements.\r\n"
"  12. Allowable number formats are decimal numbers and C-style case\r\n"
"      insensitive hexadecimal numbers. Numbers may be preceded by a\r\n"
"      "-" indicating a negative number.\r\n"
"      Examples:\r\n"
"        13\r\n"
"        46\r\n"
"        -0x3FFF\r\n"
"        0x3fff\r\n"
"        0x1234\r\n"
"  13. Unsigned values must be less than 264. Signed integer values are bounded\r\n"
"      by -/+263.\r\n"
"  14. Numbers are internally represented in two's compliment form. The\r\n"
"      representation of the number in the string has no bearing on the way\r\n"
"      that number is treated in an numeric expression - type is assigned by\r\n"
"      the operator. So, for example, -1 lt 2 is true but -1 ult 2 is false.\r\n"
"  15. The IF command is only available in scripts.\r\n"
"  16. The ELSE command is optional in an IF/ELSE statement.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * Sample script for "if" command usages 1 and 2:\r\n"
"    if exist fs0:\myscript.nsh then\r\n"
"    myscript myarg1 myarg2\r\n"
"    endif\r\n"
"    if %myvar% == runboth then\r\n"
"    myscript1\r\n"
"    myscript2\r\n"
"    else\r\n"
"    echo ^%myvar^% != runboth\r\n"
"    endif\r\n"
"    Note: In this example, if the script file myscript.nsh exists in fs0:\,\r\n"
"          this script will be launched with 2 arguments, myarg1 and myarg2.\r\n"
"          After that, environment variable %myvar% is checked to see if its\r\n"
"          value is runboth, if so, script myscript1 and myscript2 will be\r\n"
"          executed one after the other, otherwise a message %myvar% != runboth\r\n"
"          is printed.\r\n"
" \r\n"
"  * Sample script for "if" command usage 3:\r\n"
"    :Redo\r\n"
"    echo Enter 0-6 or q to quit\r\n"
"    # assumes "input y" stores a character of user input into variable y\r\n"
"    InputCh MyVar\r\n"
"    if x%MyVar% eq x then\r\n"
"    echo Empty line. Try again\r\n"
"    goto Redo\r\n"
"    endif\r\n"
"    if IsInt(%MyVar%) and %MyVar% le 6 then\r\n"
"    myscript1 %MyVar%\r\n"
"    goto Redo\r\n"
"    endif\r\n"
"    if /i %MyVar% ne q then\r\n"
"    echo Invalid input\r\n"
"    goto Redo\r\n"
"    endif\r\n"
"    Note: In this example, the script requests user input and uses the if\r\n"
"          command for input validation. It checks for empty line first and\r\n"
"          then range checks the input.\r\n"

#string STR_GET_HELP_SHIFT        #language en-US ""
".TH shift 0 "move parameters 1 down"\r\n"
".SH NAME\r\n"
"Shifts in-script parameter positions.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"SHIFT\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. The SHIFT command shifts the contents of a UEFI Shell script's positional\r\n"
"     parameters so that %1 is discarded, %2 is copied to %1, %3 is copied to\r\n"
"     %2, %4 is copied to %3 and so on. This allows UEFI Shell scripts to\r\n"
"     process script parameters from left to right.\r\n"
"  2. This command does not change the UEFI shell environment variable\r\n"
"     lasterror.\r\n"
"  3. The SHIFT command is available only in UEFI Shell scripts.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * Following script is a sample of 'shift' command:\r\n"
"    fs0:\> type shift.nsh\r\n"
"    #\r\n"
"    # shift.nsh\r\n"
"    # \r\n"
"    echo %1 %2 %3\r\n"
"    shift\r\n"
"    echo %1 %2\r\n"
" \r\n"
"  * To execute the script with echo on:\r\n"
"    fs0:\> shift.nsh welcome UEFI world\r\n"
"    shift.nsh> echo welcome UEFI world\r\n"
"    welcome UEFI world\r\n"
"    shift\r\n"
"    echo UEFI world\r\n"
"    UEFI world\r\n"
" \r\n"
"  * To execute the script with echo off:\r\n"
"    fs0:\> echo -off\r\n"
"    fs0:\> shift.nsh welcome UEFI world\r\n"
"    welcome UEFI world\r\n"
"    UEFI world\r\n"

#string STR_GET_HELP_ELSE         #language en-US ""
".TH else 0 "part of an 'if' conditional statement"\r\n"
".SH NAME\r\n"
"Identifies the code executed when 'if' is FALSE.\r\n"
".SH SYNOPSIS\r\n"
"See 'else' for usage.\r\n"
".SH EXAMPLES\r\n"
"See 'if' for examples.\r\n"

#string STR_GET_HELP_STALL        #language en-US ""
".TH stall 0 "stall the operation"\r\n"
".SH NAME\r\n"
"Stalls the operation for a specified number of microseconds.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"STALL time\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  time - The number of microseconds for the processor to stall.\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. This command would be used to establish a timed STALL of operations\r\n"
"     during a script.\r\n"
"  2. Microseconds is in decimal units.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To stall the processor for 1000000 microseconds:\r\n"
"    Shell> stall 1000000\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  SHELL_SUCCESS            The action was completed as requested.\r\n"
"  SHELL_NOT_FOUND          The requested option was not found.\r\n"
"  SHELL_INVALID_PARAMETER  One of the passed in parameters was incorrectly\r\n"
"                           formatted or its value was out of bounds.\r\n"
"  SHELL_DEVICE_ERROR       There was a hardware error associated with this\r\n"
"                           request.\r\n"


## @file
#  This is the first module for UEFI payload.
#
#  Copyright (c) 2006 - 2021, Intel Corporation. All rights reserved.<BR>
#  Copyright (c) 2017, AMD Incorporated. All rights reserved.<BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 1.30
  BASE_NAME                      = PayloadEntry
  FILE_GUID                      = 2119BBD7-9432-4f47-B5E2-5C4EA31B6BDC
  MODULE_TYPE                    = SEC
  VERSION_STRING                 = 1.0

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 AARCH64
#

[Sources]
  UefiPayloadEntry.c
  LoadDxeCore.c
  MemoryAllocation.c
  AcpiTable.c

[Sources.Ia32]
  X64/VirtualMemory.h
  X64/VirtualMemory.c
  Ia32/DxeLoadFunc.c
  Ia32/IdtVectorAsm.nasm

[Sources.X64]
  X64/VirtualMemory.h
  X64/VirtualMemory.c
  X64/DxeLoadFunc.c

[Sources.AARCH64]
  AArch64/DxeHandoff.c
  AArch64/DxeLoadFuncFit.c

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  UefiCpuPkg/UefiCpuPkg.dec
  UefiPayloadPkg/UefiPayloadPkg.dec

[LibraryClasses]
  BaseMemoryLib
  DebugLib
  BaseLib
  SerialPortLib
  IoLib
  BlParseLib
  HobLib
  PeCoffLib
  PlatformSupportLib
  CpuLib
  StackCheckLib
  UefiCpuBaseArchSupportLib

[Guids]
  gEfiMemoryTypeInformationGuid
  gEfiFirmwareFileSystem2Guid
  gEfiGraphicsInfoHobGuid
  gEfiGraphicsDeviceInfoHobGuid
  gUefiAcpiBoardInfoGuid
  gUniversalPayloadSmbiosTableGuid
  gUniversalPayloadAcpiTableGuid
  gUniversalPayloadSerialPortInfoGuid
  gEfiSmmStoreInfoHobGuid

[FeaturePcd.IA32]
  gEfiMdeModulePkgTokenSpaceGuid.PcdDxeIplSwitchToLongMode      ## CONSUMES

[FeaturePcd.X64]
  gEfiMdeModulePkgTokenSpaceGuid.PcdDxeIplBuildPageTables       ## CONSUMES


[Pcd.IA32,Pcd.X64,Pcd.AARCH64]
  gEfiMdeModulePkgTokenSpaceGuid.PcdUse1GPageTable                      ## SOMETIMES_CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdPteMemoryEncryptionAddressOrMask    ## CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdNullPointerDetectionPropertyMask    ## CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdHeapGuardPropertyMask               ## CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdCpuStackGuard                       ## CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdGhcbBase                            ## CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdGhcbSize                            ## CONSUMES

  gUefiPayloadPkgTokenSpaceGuid.PcdPayloadFdMemBase
  gUefiPayloadPkgTokenSpaceGuid.PcdPayloadFdMemSize
  gUefiPayloadPkgTokenSpaceGuid.PcdBootloaderParameter
  gUefiPayloadPkgTokenSpaceGuid.PcdSystemMemoryUefiRegionSize
  gUefiPayloadPkgTokenSpaceGuid.PcdMemoryTypeEfiACPIMemoryNVS
  gUefiPayloadPkgTokenSpaceGuid.PcdMemoryTypeEfiACPIReclaimMemory
  gUefiPayloadPkgTokenSpaceGuid.PcdMemoryTypeEfiReservedMemoryType
  gUefiPayloadPkgTokenSpaceGuid.PcdMemoryTypeEfiRuntimeServicesData
  gUefiPayloadPkgTokenSpaceGuid.PcdMemoryTypeEfiRuntimeServicesCode

  gEfiMdeModulePkgTokenSpaceGuid.PcdSetNxForStack               ## SOMETIMES_CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdDxeNxMemoryProtectionPolicy ## SOMETIMES_CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdImageProtectionPolicy       ## SOMETIMES_CONSUMES

  gEfiMdeModulePkgTokenSpaceGuid.PcdSerialClockRate             ## PRODUCES

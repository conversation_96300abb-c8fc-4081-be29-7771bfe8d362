## @file
# Sample UnitTest built for execution in PEI.
# This test case generates an exception. For some host-based environments, this
# is a fatal condition that terminates the unit tests and no additional test
# cases are executed. On other environments, this condition may be report a unit
# test failure and continue with additional unit tests.
#
# Copyright (c) 2024, Intel Corporation. All rights reserved.<BR>
# SPDX-License-Identifier: BSD-2-Clause-Patent
##

[Defines]
  INF_VERSION    = 0x00010006
  BASE_NAME      = SampleUnitTestPeiGenerateException
  FILE_GUID      = F66B54D6-0EB0-410E-A5A5-C76A739C5F5D
  MODULE_TYPE    = PEIM
  VERSION_STRING = 1.0
  ENTRY_POINT    = PeiEntryPoint

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources]
  SampleUnitTestGenerateException.c

[Packages]
  MdePkg/MdePkg.dec

[LibraryClasses]
  PeimEntryPoint
  BaseLib
  DebugLib
  UnitTestLib
  PrintLib

[Pcd]
  gEfiMdePkgTokenSpaceGuid.PcdDebugPropertyMask

[Depex]
  gEfiPeiMemoryDiscoveredPpiGuid

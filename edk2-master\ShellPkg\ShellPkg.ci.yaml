## @file
# CI configuration for ShellPkg
#
# Copyright (c) Microsoft Corporation
# Copyright (c) 2020, Intel Corporation. All rights reserved.<BR>
# SPDX-License-Identifier: BSD-2-Clause-Patent
##
{
    "PrEval": {
        "DscPath": "ShellPkg.dsc",
    },
    "LicenseCheck": {
        "IgnoreFiles": []
    },
    "EccCheck": {
        ## Exception sample looks like below:
        ## "ExceptionList": [
        ##     "<ErrorID>", "<KeyWord>"
        ## ]
        "ExceptionList": [
            "8001", "ShellCommandLineParse"
        ],
        ## Both file path and directory path are accepted.
        "IgnoreFiles": [
        ]
    },
    "CompilerPlugin": {
        "DscPath": "ShellPkg.dsc"
    },
    "CharEncodingCheck": {
        "IgnoreFiles": []
    },
    "DependencyCheck": {
        "AcceptableDependencies": [
            "MdePkg/MdePkg.dec",
            "MdeModulePkg/MdeModulePkg.dec",
            "ShellPkg/ShellPkg.dec",
            "NetworkPkg/NetworkPkg.dec"
        ],
        # For host based unit tests
        "AcceptableDependencies-HOST_APPLICATION":[
            "UnitTestFrameworkPkg/UnitTestFrameworkPkg.dec"
        ],
        # For UEFI shell based apps
        "AcceptableDependencies-UEFI_APPLICATION":[],
        "IgnoreInf": []
    },
    "DscCompleteCheck": {
        "DscPath": "ShellPkg.dsc",
        "IgnoreInf": [
            "ShellPkg/Application/ShellCTestApp/ShellCTestApp.inf",
            "ShellPkg/Application/ShellExecTestApp/SA.inf",
            "ShellPkg/Application/ShellSortTestApp/ShellSortTestApp.inf"
            ]
    },
    "GuidCheck": {
        "IgnoreGuidName": [],
        "IgnoreGuidValue": [],
        "IgnoreFoldersAndFiles": [],
        "IgnoreDuplicates": [
            "Shell=gUefiShellFileGuid", # by design
        ]
    },
    "LibraryClassCheck": {
        "IgnoreHeaderFile": []
    },

    ## options defined ci/Plugin/SpellCheck
    "SpellCheck": {
        "AuditOnly": True,           # Fails test but run in AuditOnly mode to collect log
        "IgnoreFiles": [],           # use gitignore syntax to ignore errors in matching files
        "ExtendWords": [],           # words to extend to the dictionary for this package
        "IgnoreStandardPaths": [],   # Standard Plugin defined paths that should be ignore
        "AdditionalIncludePaths": [] # Additional paths to spell check (wildcards supported)
    }
}

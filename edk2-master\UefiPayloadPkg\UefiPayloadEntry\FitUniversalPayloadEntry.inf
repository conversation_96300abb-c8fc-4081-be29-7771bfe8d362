## @file
#  This is the first module for UEFI payload.
#
#  Copyright (c) 2023, Intel Corporation. All rights reserved.<BR>
#  Copyright 2024 Google LLC
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 1.30
  BASE_NAME                      = FitUniversalPayloadEntry
  FILE_GUID                      = CED5A8A9-B6EA-4D5A-8689-577EE88566CF
  MODULE_TYPE                    = SEC
  VERSION_STRING                 = 1.0

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 AARCH64
#

[Sources]
  FitUniversalPayloadEntry.c
  LoadDxeCore.c
  MemoryAllocation.c
  PrintHob.c
  AcpiTable.c

[Sources.Ia32]
  X64/VirtualMemory.h
  X64/VirtualMemory.c
  Ia32/DxeLoadFuncFit.c
  Ia32/IdtVectorAsm.nasm

[Sources.X64]
  X64/VirtualMemory.h
  X64/VirtualMemory.c
  X64/DxeLoadFuncFit.c

[Sources.RISCV64]
  RiscV64/DxeLoadFunc.c
  RiscV64/DxeLoadFuncFit.c

[Sources.AARCH64]
  AArch64/DxeHandoff.c
  AArch64/DxeLoadFuncFit.c

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  UefiCpuPkg/UefiCpuPkg.dec
  UefiPayloadPkg/UefiPayloadPkg.dec

[LibraryClasses]
  BaseMemoryLib
  DebugLib
  BaseLib
  SerialPortLib
  IoLib
  HobLib
  PeCoffLib
  CpuLib
  FdtLib
  HobPrintLib
  CustomFdtNodeParserLib
  PcdLib
  StackCheckLib

[Guids]
  gEfiMemoryTypeInformationGuid
  gEfiFirmwareFileSystem2Guid
  gEfiGraphicsInfoHobGuid
  gEfiGraphicsDeviceInfoHobGuid
  gUefiAcpiBoardInfoGuid
  gEfiSmbiosTableGuid
  gUefiSerialPortInfoGuid
  gUniversalPayloadExtraDataGuid
  gUniversalPayloadBaseGuid
  gPcdDataBaseHobGuid
  gUniversalPayloadSmbiosTableGuid
  gEfiHobMemoryAllocBspStoreGuid
  gUniversalPayloadAcpiTableGuid
  gUniversalPayloadPciRootBridgeInfoGuid
  gUniversalPayloadSmbios3TableGuid
  gUniversalPayloadDeviceTreeGuid

[FeaturePcd.IA32]
  gEfiMdeModulePkgTokenSpaceGuid.PcdDxeIplSwitchToLongMode      ## CONSUMES

[FeaturePcd.X64]
  gEfiMdeModulePkgTokenSpaceGuid.PcdDxeIplBuildPageTables       ## CONSUMES

[Pcd.IA32,Pcd.X64,Pcd.RISCV64,Pcd.AARCH64]
  gUefiPayloadPkgTokenSpaceGuid.PcdPcdDriverFile
  gEfiMdeModulePkgTokenSpaceGuid.PcdUse1GPageTable                      ## SOMETIMES_CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdPteMemoryEncryptionAddressOrMask    ## CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdNullPointerDetectionPropertyMask    ## CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdHeapGuardPropertyMask               ## CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdCpuStackGuard                       ## CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdGhcbBase                            ## CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdGhcbSize                            ## CONSUMES
  gUefiPayloadPkgTokenSpaceGuid.PcdPayloadFdMemBase
  gUefiPayloadPkgTokenSpaceGuid.PcdPayloadFdMemSize
  gUefiPayloadPkgTokenSpaceGuid.PcdSystemMemoryUefiRegionSize
  gEfiMdeModulePkgTokenSpaceGuid.PcdSetNxForStack               ## SOMETIMES_CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdDxeNxMemoryProtectionPolicy ## SOMETIMES_CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdImageProtectionPolicy       ## SOMETIMES_CONSUMES
  gUefiPayloadPkgTokenSpaceGuid.PcdHandOffFdtEnable
  gUefiPayloadPkgTokenSpaceGuid.PcdMemoryTypeEfiACPIMemoryNVS
  gUefiPayloadPkgTokenSpaceGuid.PcdMemoryTypeEfiACPIReclaimMemory
  gUefiPayloadPkgTokenSpaceGuid.PcdMemoryTypeEfiReservedMemoryType
  gUefiPayloadPkgTokenSpaceGuid.PcdMemoryTypeEfiRuntimeServicesData
  gUefiPayloadPkgTokenSpaceGuid.PcdMemoryTypeEfiRuntimeServicesCode
  gUefiPayloadPkgTokenSpaceGuid.PcdFDTPageSize

[BuildOptions]
  MSFT:*_*_*_CC_FLAGS = /wd4244 /wd4305
  GCC:*_*_*_CC_FLAGS  = -Wno-error=pointer-to-int-cast -Wno-error=int-to-pointer-cast

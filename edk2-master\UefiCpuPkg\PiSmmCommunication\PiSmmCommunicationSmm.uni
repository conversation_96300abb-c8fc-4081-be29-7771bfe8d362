// /** @file
// PI SMM Communication SMM driver that saves SMM communication context
// for use by SMM Communication PEIM in the S3 boot mode.
//
// Copyright (c) 2010 - 2017, Intel Corporation. All rights reserved.<BR>
//
// SPDX-License-Identifier: BSD-2-Clause-Patent
//
// **/

#string STR_MODULE_ABSTRACT             #language en-US "PI SMM Communication SMM driver that saves SMM communication context"

#string STR_MODULE_DESCRIPTION          #language en-US "PI SMM Communication SMM driver that saves SMM communication context for use by SMM Communication PEIM in the S3 boot mode."

/** @file
  USB Hardware Enumeration and Fingerprint Generation

  This module provides functionality to enumerate USB devices and generate
  hardware fingerprints for authentication purposes.

  Copyright (c) 2024, Auth Wrapper Project. All rights reserved.
  SPDX-License-Identifier: BSD-2-Clause-Patent

**/

#include "AuthWrapper.h"

/**
  Enumerate USB devices and extract hardware information.

  @param[out] DeviceList    Pointer to array of USB device information.
  @param[out] DeviceCount   Number of USB devices found.

  @retval EFI_SUCCESS       USB devices enumerated successfully.
  @retval other             Error occurred during enumeration.

**/
EFI_STATUS
EnumerateUSBDevices (
  OUT USB_DEVICE_INFO **DeviceList,
  OUT UINTN           *DeviceCount
  )
{
  EFI_STATUS                Status;
  UINTN                     HandleCount;
  EFI_HANDLE                *HandleBuffer = NULL;
  USB_DEVICE_INFO           *Devices = NULL;
  UINTN                     DeviceIndex;
  EFI_USB_IO_PROTOCOL       *UsbIo;
  EFI_USB_DEVICE_DESCRIPTOR DeviceDescriptor;

  if (DeviceList == NULL || DeviceCount == NULL) {
    return EFI_INVALID_PARAMETER;
  }

  *DeviceList = NULL;
  *DeviceCount = 0;

  //
  // Locate all USB I/O protocol handles
  //
  Status = gBS->LocateHandleBuffer (
                  ByProtocol,
                  &gEfiUsbIoProtocolGuid,
                  NULL,
                  &HandleCount,
                  &HandleBuffer
                  );
  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_ERROR, "Failed to locate USB I/O handles: %r\n", Status));
    return Status;
  }

  if (HandleCount == 0) {
    DEBUG ((DEBUG_WARN, "No USB devices found\n"));
    return EFI_NOT_FOUND;
  }

  //
  // Allocate memory for device information array
  //
  Devices = AllocateZeroPool (HandleCount * sizeof (USB_DEVICE_INFO));
  if (Devices == NULL) {
    FreePool (HandleBuffer);
    return EFI_OUT_OF_RESOURCES;
  }

  //
  // Enumerate each USB device
  //
  DeviceIndex = 0;
  for (UINTN Index = 0; Index < HandleCount; Index++) {
    Status = gBS->HandleProtocol (
                    HandleBuffer[Index],
                    &gEfiUsbIoProtocolGuid,
                    (VOID **) &UsbIo
                    );
    if (EFI_ERROR (Status)) {
      continue;
    }

    //
    // Get device descriptor
    //
    Status = UsbIo->UsbGetDeviceDescriptor (UsbIo, &DeviceDescriptor);
    if (EFI_ERROR (Status)) {
      continue;
    }

    //
    // Store device information
    //
    Devices[DeviceIndex].VendorId = DeviceDescriptor.IdVendor;
    Devices[DeviceIndex].ProductId = DeviceDescriptor.IdProduct;

    //
    // Try to get serial number string
    //
    if (DeviceDescriptor.StrSerialNumber != 0) {
      CHAR16 *SerialString = NULL;
      UINT16 *LangIdTable = NULL;
      UINT16 TableSize = 0;

      //
      // Get supported language IDs
      //
      Status = UsbIo->UsbGetSupportedLanguages (UsbIo, &LangIdTable, &TableSize);
      if (!EFI_ERROR (Status) && TableSize > 0) {
        //
        // Get serial number string using first supported language
        //
        Status = UsbIo->UsbGetStringDescriptor (
                          UsbIo,
                          LangIdTable[0],
                          DeviceDescriptor.StrSerialNumber,
                          &SerialString
                          );
        if (!EFI_ERROR (Status) && SerialString != NULL) {
          StrnCpyS (
            Devices[DeviceIndex].SerialNumber,
            sizeof (Devices[DeviceIndex].SerialNumber) / sizeof (CHAR16),
            SerialString,
            63
            );
          FreePool (SerialString);
        }
      }

      if (LangIdTable != NULL) {
        FreePool (LangIdTable);
      }
    }

    //
    // If no serial number, use VendorId:ProductId as identifier
    //
    if (Devices[DeviceIndex].SerialNumber[0] == 0) {
      UnicodeSPrint (
        Devices[DeviceIndex].SerialNumber,
        sizeof (Devices[DeviceIndex].SerialNumber),
        L"%04X:%04X",
        DeviceDescriptor.IdVendor,
        DeviceDescriptor.IdProduct
        );
    }

    DEBUG ((DEBUG_INFO, "USB Device %d: VID=%04X, PID=%04X, SN=%s\n",
            DeviceIndex,
            Devices[DeviceIndex].VendorId,
            Devices[DeviceIndex].ProductId,
            Devices[DeviceIndex].SerialNumber));

    DeviceIndex++;
  }

  FreePool (HandleBuffer);

  *DeviceList = Devices;
  *DeviceCount = DeviceIndex;

  DEBUG ((DEBUG_INFO, "Successfully enumerated %d USB devices\n", DeviceIndex));
  return EFI_SUCCESS;
}

/**
  Generate hardware fingerprint from USB device information.

  @param[in]  DeviceList        Array of USB device information.
  @param[in]  DeviceCount       Number of USB devices.
  @param[out] HardwareFingerprint Generated hardware fingerprint.

  @retval EFI_SUCCESS           Hardware fingerprint generated successfully.
  @retval other                 Error occurred during generation.

**/
EFI_STATUS
GenerateHardwareFingerprint (
  IN  USB_DEVICE_INFO *DeviceList,
  IN  UINTN           DeviceCount,
  OUT UINT8           *HardwareFingerprint
  )
{
  VOID    *HashContext = NULL;
  UINTN   ContextSize;
  CHAR8   DeviceString[256];
  UINTN   DeviceStringLen;

  if (DeviceList == NULL || HardwareFingerprint == NULL) {
    return EFI_INVALID_PARAMETER;
  }

  //
  // Initialize SHA256 context
  //
  ContextSize = Sha256GetContextSize ();
  HashContext = AllocatePool (ContextSize);
  if (HashContext == NULL) {
    return EFI_OUT_OF_RESOURCES;
  }

  if (!Sha256Init (HashContext)) {
    FreePool (HashContext);
    return EFI_DEVICE_ERROR;
  }

  //
  // Process each USB device in sorted order (by VendorId:ProductId)
  // This ensures consistent fingerprint generation regardless of enumeration order
  //
  for (UINTN i = 0; i < DeviceCount - 1; i++) {
    for (UINTN j = i + 1; j < DeviceCount; j++) {
      if ((DeviceList[i].VendorId > DeviceList[j].VendorId) ||
          (DeviceList[i].VendorId == DeviceList[j].VendorId && 
           DeviceList[i].ProductId > DeviceList[j].ProductId)) {
        //
        // Swap devices
        //
        USB_DEVICE_INFO Temp;
        CopyMem (&Temp, &DeviceList[i], sizeof (USB_DEVICE_INFO));
        CopyMem (&DeviceList[i], &DeviceList[j], sizeof (USB_DEVICE_INFO));
        CopyMem (&DeviceList[j], &Temp, sizeof (USB_DEVICE_INFO));
      }
    }
  }

  //
  // Hash each device information
  //
  for (UINTN Index = 0; Index < DeviceCount; Index++) {
    //
    // Convert device info to string format: "VID:PID:SerialNumber"
    //
    DeviceStringLen = AsciiSPrint (
                        DeviceString,
                        sizeof (DeviceString),
                        "%04X:%04X:%s",
                        DeviceList[Index].VendorId,
                        DeviceList[Index].ProductId,
                        DeviceList[Index].SerialNumber
                        );

    //
    // Update hash with device string
    //
    if (!Sha256Update (HashContext, DeviceString, DeviceStringLen)) {
      FreePool (HashContext);
      return EFI_DEVICE_ERROR;
    }

    DEBUG ((DEBUG_INFO, "Hashing device: %a\n", DeviceString));
  }

  //
  // Finalize hash
  //
  if (!Sha256Final (HashContext, HardwareFingerprint)) {
    FreePool (HashContext);
    return EFI_DEVICE_ERROR;
  }

  //
  // Clean up
  //
  ZeroMem (HashContext, ContextSize);
  FreePool (HashContext);

  DEBUG ((DEBUG_INFO, "Hardware fingerprint generated successfully\n"));
  return EFI_SUCCESS;
}

/** @file
  Auth Wrapper Application Main File

  This application provides authentication wrapper for boot.efi.
  It verifies hardware fingerprint and key file before loading boot.efi.

  Copyright (c) 2024, Auth Wrapper Project. All rights reserved.
  SPDX-License-Identifier: BSD-2-Clause-Patent

**/

#include "AuthWrapper.h"

/**
  Main entry point for Auth Wrapper Application.

  @param[in] ImageHandle    The firmware allocated handle for the EFI image.
  @param[in] SystemTable    A pointer to the EFI System Table.

  @retval EFI_SUCCESS       The entry point executed successfully.
  @retval other             Some error occurred when executing this entry point.

**/
EFI_STATUS
EFIAPI
AuthWrapperMain (
  IN EFI_HANDLE        ImageHandle,
  IN EFI_SYSTEM_TABLE  *SystemTable
  )
{
  EFI_STATUS Status;

  DEBUG ((DEBUG_INFO, "Auth Wrapper: Starting authentication process...\n"));
  Print (L"Auth Wrapper: 开始验证过程...\n");

  //
  // Perform authentication verification
  //
  Status = PerformAuthentication ();
  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_ERROR, "Auth Wrapper: Authentication failed: %r\n", Status));
    Print (L"Auth Wrapper: 验证失败，拒绝启动！\n");
    Print (L"错误代码: %r\n", Status);
    
    // Wait for user input before exit
    Print (L"按任意键退出...\n");
    gST->ConIn->Reset (gST->ConIn, FALSE);
    EFI_INPUT_KEY Key;
    gST->ConIn->ReadKeyStroke (gST->ConIn, &Key);
    
    return Status;
  }

  DEBUG ((DEBUG_INFO, "Auth Wrapper: Authentication successful, loading boot.efi...\n"));
  Print (L"Auth Wrapper: 验证成功，正在加载boot.efi...\n");

  //
  // Load and execute the original boot.efi
  //
  Status = LoadAndExecuteBootEfi ();
  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_ERROR, "Auth Wrapper: Failed to load boot.efi: %r\n", Status));
    Print (L"Auth Wrapper: 加载boot.efi失败: %r\n", Status);
    
    // Wait for user input before exit
    Print (L"按任意键退出...\n");
    gST->ConIn->Reset (gST->ConIn, FALSE);
    EFI_INPUT_KEY Key;
    gST->ConIn->ReadKeyStroke (gST->ConIn, &Key);
    
    return Status;
  }

  // Should not reach here if boot.efi executed successfully
  DEBUG ((DEBUG_INFO, "Auth Wrapper: boot.efi execution completed\n"));
  return EFI_SUCCESS;
}

/**
  Perform complete authentication verification.

  @retval EFI_SUCCESS             Authentication successful.
  @retval EFI_ACCESS_DENIED       Authentication failed.
  @retval other                   Error occurred during verification.

**/
EFI_STATUS
PerformAuthentication (
  VOID
  )
{
  EFI_STATUS        Status;
  USB_DEVICE_INFO   *UsbDevices = NULL;
  UINTN             UsbDeviceCount = 0;
  UINT8             HardwareFingerprint[HARDWARE_FINGERPRINT_SIZE];
  CHAR16            KeyFilename[KEY_FILENAME_LENGTH + 1];
  CHAR16            ExpectedVerificationCode[VERIFICATION_CODE_LENGTH + 1];
  CHAR16            FileContent[VERIFICATION_CODE_LENGTH + 1];

  DEBUG ((DEBUG_INFO, "Auth Wrapper: Step 1 - Enumerating USB devices...\n"));
  Print (L"步骤1: 扫描USB设备...\n");

  //
  // Step 1: Enumerate USB devices
  //
  Status = EnumerateUSBDevices (&UsbDevices, &UsbDeviceCount);
  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_ERROR, "Auth Wrapper: USB enumeration failed: %r\n", Status));
    Print (L"USB设备扫描失败: %r\n", Status);
    return Status;
  }

  DEBUG ((DEBUG_INFO, "Auth Wrapper: Found %d USB devices\n", UsbDeviceCount));
  Print (L"发现 %d 个USB设备\n", UsbDeviceCount);

  //
  // Step 2: Generate hardware fingerprint
  //
  DEBUG ((DEBUG_INFO, "Auth Wrapper: Step 2 - Generating hardware fingerprint...\n"));
  Print (L"步骤2: 生成硬件指纹...\n");

  Status = GenerateHardwareFingerprint (UsbDevices, UsbDeviceCount, HardwareFingerprint);
  if (UsbDevices != NULL) {
    FreePool (UsbDevices);
  }
  
  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_ERROR, "Auth Wrapper: Hardware fingerprint generation failed: %r\n", Status));
    Print (L"硬件指纹生成失败: %r\n", Status);
    return Status;
  }

  //
  // Step 3: Generate key filename
  //
  DEBUG ((DEBUG_INFO, "Auth Wrapper: Step 3 - Generating key filename...\n"));
  Print (L"步骤3: 生成密钥文件名...\n");

  Status = GenerateKeyFilename (HardwareFingerprint, KeyFilename);
  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_ERROR, "Auth Wrapper: Key filename generation failed: %r\n", Status));
    Print (L"密钥文件名生成失败: %r\n", Status);
    return Status;
  }

  DEBUG ((DEBUG_INFO, "Auth Wrapper: Key filename: %s\n", KeyFilename));
  Print (L"密钥文件名: %s.vtkey\n", KeyFilename);

  //
  // Step 4: Check if key file exists
  //
  DEBUG ((DEBUG_INFO, "Auth Wrapper: Step 4 - Checking key file existence...\n"));
  Print (L"步骤4: 检查密钥文件是否存在...\n");

  if (!CheckKeyFileExists (KeyFilename)) {
    DEBUG ((DEBUG_ERROR, "Auth Wrapper: Key file does not exist: %s.vtkey\n", KeyFilename));
    Print (L"密钥文件不存在: %s.vtkey\n", KeyFilename);
    return EFI_ACCESS_DENIED;
  }

  Print (L"密钥文件存在，继续验证...\n");

  //
  // Step 5: Read key file content
  //
  DEBUG ((DEBUG_INFO, "Auth Wrapper: Step 5 - Reading key file content...\n"));
  Print (L"步骤5: 读取密钥文件内容...\n");

  Status = ReadKeyFileContent (KeyFilename, FileContent, sizeof (FileContent));
  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_ERROR, "Auth Wrapper: Failed to read key file: %r\n", Status));
    Print (L"读取密钥文件失败: %r\n", Status);
    return Status;
  }

  //
  // Step 6: Generate expected verification code
  //
  DEBUG ((DEBUG_INFO, "Auth Wrapper: Step 6 - Generating verification code...\n"));
  Print (L"步骤6: 生成验证码...\n");

  Status = GenerateVerificationCode (HardwareFingerprint, ExpectedVerificationCode);
  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_ERROR, "Auth Wrapper: Verification code generation failed: %r\n", Status));
    Print (L"验证码生成失败: %r\n", Status);
    return Status;
  }

  //
  // Step 7: Compare verification codes
  //
  DEBUG ((DEBUG_INFO, "Auth Wrapper: Step 7 - Comparing verification codes...\n"));
  Print (L"步骤7: 比较验证码...\n");

  if (StrCmp (FileContent, ExpectedVerificationCode) != 0) {
    DEBUG ((DEBUG_ERROR, "Auth Wrapper: Verification code mismatch\n"));
    DEBUG ((DEBUG_ERROR, "Expected: %s\n", ExpectedVerificationCode));
    DEBUG ((DEBUG_ERROR, "Found:    %s\n", FileContent));
    Print (L"验证码不匹配，验证失败！\n");
    return EFI_SECURITY_VIOLATION;
  }

  DEBUG ((DEBUG_INFO, "Auth Wrapper: Authentication successful!\n"));
  Print (L"验证成功！\n");

  return EFI_SUCCESS;
}

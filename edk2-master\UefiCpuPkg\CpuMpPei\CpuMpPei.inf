## @file
#  CPU driver installs CPU PI Multi-processor PPI.
#
#  Copyright (c) 2015 - 2023, Intel Corporation. All rights reserved.<BR>
#  Copyright (c) 2025, Loongson Technology Corporation Limited. All rights reserved.<BR>
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = CpuMpPei
  MODULE_UNI_FILE                = CpuMpPei.uni
  FILE_GUID                      = EDADEB9D-DDBA-48BD-9D22-C1C169C8C5C6
  MODULE_TYPE                    = PEIM
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = CpuMpPeimInit

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 LOONGARCH64
#

[Sources]
  CpuMpPei.h
  CpuMp2Pei.h
  CpuMp.c
  CpuMp2.c

[Sources.Ia32, Sources.X64]
  CpuBist.c
  CpuPaging.c
  CpuMpPei.c

[Sources.LoongArch64]
  LoongArch64/CpuMpPei.c

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  UefiCpuPkg/UefiCpuPkg.dec

[LibraryClasses]
  BaseLib
  DebugLib
  HobLib
  PeimEntryPoint
  PeiServicesLib
  ReportStatusCodeLib
  MpInitLib
  BaseMemoryLib
  CpuLib
  MemoryAllocationLib

[LibraryClasses.Ia32, LibraryClasses.X64]
  CpuExceptionHandlerLib
  CpuPageTableLib
  LocalApicLib

[Guids]
  gEdkiiMigratedFvInfoGuid                                             ## SOMETIMES_CONSUMES     ## HOB
  gMpInformation2HobGuid                        ## PRODUCES

[Ppis]
  gEfiPeiMpServicesPpiGuid                      ## PRODUCES
  gEfiSecPlatformInformationPpiGuid             ## SOMETIMES_CONSUMES
  ## SOMETIMES_CONSUMES
  ## PRODUCES
  ## UNDEFINED # HOB
  gEfiSecPlatformInformation2PpiGuid
  gEfiVectorHandoffInfoPpiGuid                  ## SOMETIMES_CONSUMES
  gEfiPeiMemoryDiscoveredPpiGuid                ## CONSUMES
  gEfiPeiMpServices2PpiGuid                     ## PRODUCES

[Pcd]
  gEfiMdeModulePkgTokenSpaceGuid.PcdPteMemoryEncryptionAddressOrMask    ## CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdCpuStackGuard                       ## CONSUMES
  gUefiCpuPkgTokenSpaceGuid.PcdCpuStackSwitchExceptionList              ## SOMETIMES_CONSUMES
  gUefiCpuPkgTokenSpaceGuid.PcdCpuKnownGoodStackSize                    ## SOMETIMES_CONSUMES
  gUefiCpuPkgTokenSpaceGuid.PcdCpuApStackSize                           ## SOMETIMES_CONSUMES

[Depex]
  TRUE

[UserExtensions.TianoCore."ExtraFiles"]
  CpuMpPeiExtra.uni


## @file
# Sample UnitTest built for execution on a Host machine.
# This test case generates a double free that is caught by a sanitizer.
#
# Copyright (c) 2024, Intel Corporation. All rights reserved.<BR>
# SPDX-License-Identifier: BSD-2-Clause-Patent
##

[Defines]
  INF_VERSION    = 0x00010005
  BASE_NAME      = SampleUnitTestDoubleFree
  FILE_GUID      = B40BBE1C-90AB-4DE9-9B49-E734666FF9A7
  MODULE_TYPE    = HOST_APPLICATION
  VERSION_STRING = 1.0

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources]
  SampleUnitTestDoubleFree.c

[Packages]
  MdePkg/MdePkg.dec

[LibraryClasses]
  BaseLib
  DebugLib
  UnitTestLib
  MemoryAllocationLib

## @file
#  PciHostBridgeLib utility functions for ArmVirt.
#
#  Copyright (c) 2021, Arm Limited. All rights reserved.
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#
##

[Defines]
  INF_VERSION                    = 0x0001001B
  BASE_NAME                      = ArmVirtPciHostBridgeUtilityLib
  FILE_GUID                      = 22A8844E-2AE7-4BF1-91FA-6EFDE3FE540C
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = PciHostBridgeUtilityLib

#
# The following information is for reference only and not required by the build
# tools.
#
#  VALID_ARCHITECTURES           = AARCH64 ARM
#

[Sources]
  ArmVirtPciHostBridgeUtilityLib.c

[Packages]
  MdeModulePkg/MdeModulePkg.dec
  MdePkg/MdePkg.dec
  OvmfPkg/OvmfPkg.dec

[LibraryClasses]
  BaseMemoryLib
  DebugLib
  DevicePathLib
  MemoryAllocationLib
  PciLib

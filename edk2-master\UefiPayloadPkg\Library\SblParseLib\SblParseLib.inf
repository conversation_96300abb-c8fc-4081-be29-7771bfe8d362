## @file
#  Slim Bootloader parse library.
#
#  Copyright (c) 2019 - 2021, Intel Corporation. All rights reserved.<BR>
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = SblParseLib
  FILE_GUID                      = DE6FB32C-52CF-4A17-A84C-B323653CB5E0
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = BlParseLib

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources]
  SblParseLib.c

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  UefiPayloadPkg/UefiPayloadPkg.dec

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  DebugLib
  PcdLib
  HobLib

[Guids]
  gUefiSerialPortInfoGuid
  gLoaderMemoryMapInfoGuid
  gEfiGraphicsInfoHobGuid
  gEfiGraphicsDeviceInfoHobGuid
  gUniversalPayloadPciRootBridgeInfoGuid

[Pcd]
  gUefiPayloadPkgTokenSpaceGuid.PcdBootloaderParameter

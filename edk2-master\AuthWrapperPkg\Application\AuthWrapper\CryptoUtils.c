/** @file
  Cryptographic Utilities

  This module provides cryptographic functions including Base32 encoding
  and key generation utilities for authentication purposes.

  Copyright (c) 2024, Auth Wrapper Project. All rights reserved.
  SPDX-License-Identifier: BSD-2-Clause-Patent

**/

#include "AuthWrapper.h"

/**
  Convert binary data to Base32 string.

  @param[in]  Data              Binary data to convert.
  @param[in]  DataSize          Size of binary data.
  @param[out] Base32String      Output Base32 string.
  @param[in]  StringSize        Size of output string buffer.

  @retval EFI_SUCCESS           Conversion successful.
  @retval other                 Error occurred during conversion.

**/
EFI_STATUS
ConvertToBase32 (
  IN  UINT8   *Data,
  IN  UINTN   DataSize,
  OUT CHAR16  *Base32String,
  IN  UINTN   StringSize
  )
{
  CONST CHAR8 *Charset = BASE32_CHARSET;
  UINTN       OutputLen;
  UINTN       InputIndex;
  UINTN       OutputIndex;
  UINT32      Buffer;
  UINTN       BitsInBuffer;

  if (Data == NULL || Base32String == NULL || StringSize == 0) {
    return EFI_INVALID_PARAMETER;
  }

  //
  // Calculate required output length (5 bits per character)
  //
  OutputLen = (DataSize * 8 + 4) / 5;
  if (OutputLen >= StringSize) {
    return EFI_BUFFER_TOO_SMALL;
  }

  //
  // Convert binary data to Base32
  //
  InputIndex = 0;
  OutputIndex = 0;
  Buffer = 0;
  BitsInBuffer = 0;

  while (InputIndex < DataSize || BitsInBuffer >= 5) {
    //
    // Fill buffer with more bits if needed
    //
    while (BitsInBuffer < 5 && InputIndex < DataSize) {
      Buffer = (Buffer << 8) | Data[InputIndex];
      BitsInBuffer += 8;
      InputIndex++;
    }

    //
    // Extract 5 bits and convert to Base32 character
    //
    if (BitsInBuffer >= 5) {
      UINT32 Index = (Buffer >> (BitsInBuffer - 5)) & 0x1F;
      Base32String[OutputIndex] = (CHAR16) Charset[Index];
      OutputIndex++;
      BitsInBuffer -= 5;
    } else {
      //
      // Handle remaining bits (less than 5)
      //
      if (BitsInBuffer > 0) {
        UINT32 Index = (Buffer << (5 - BitsInBuffer)) & 0x1F;
        Base32String[OutputIndex] = (CHAR16) Charset[Index];
        OutputIndex++;
      }
      break;
    }
  }

  //
  // Null terminate the string
  //
  Base32String[OutputIndex] = L'\0';

  DEBUG ((DEBUG_INFO, "Base32 conversion: %d bytes -> %d characters\n", DataSize, OutputIndex));
  return EFI_SUCCESS;
}

/**
  Generate key filename from hardware fingerprint.

  @param[in]  HardwareFingerprint Hardware fingerprint data.
  @param[out] KeyFilename         Generated key filename.

  @retval EFI_SUCCESS             Key filename generated successfully.
  @retval other                   Error occurred during generation.

**/
EFI_STATUS
GenerateKeyFilename (
  IN  UINT8   *HardwareFingerprint,
  OUT CHAR16  *KeyFilename
  )
{
  EFI_STATUS Status;
  VOID       *HashContext = NULL;
  UINTN      ContextSize;
  UINT8      FilenameHash[SHA256_DIGEST_SIZE];

  if (HardwareFingerprint == NULL || KeyFilename == NULL) {
    return EFI_INVALID_PARAMETER;
  }

  //
  // Initialize SHA256 context for filename generation
  //
  ContextSize = Sha256GetContextSize ();
  HashContext = AllocatePool (ContextSize);
  if (HashContext == NULL) {
    return EFI_OUT_OF_RESOURCES;
  }

  if (!Sha256Init (HashContext)) {
    FreePool (HashContext);
    return EFI_DEVICE_ERROR;
  }

  //
  // Hash the hardware fingerprint
  //
  if (!Sha256Update (HashContext, HardwareFingerprint, HARDWARE_FINGERPRINT_SIZE)) {
    FreePool (HashContext);
    return EFI_DEVICE_ERROR;
  }

  //
  // Finalize hash
  //
  if (!Sha256Final (HashContext, FilenameHash)) {
    FreePool (HashContext);
    return EFI_DEVICE_ERROR;
  }

  //
  // Clean up hash context
  //
  ZeroMem (HashContext, ContextSize);
  FreePool (HashContext);

  //
  // Convert first 8 bytes of hash to Base32 (16 characters)
  //
  Status = ConvertToBase32 (
             FilenameHash,
             8,  // Use first 8 bytes for 16-character filename
             KeyFilename,
             KEY_FILENAME_LENGTH + 1
             );

  if (EFI_ERROR (Status)) {
    return Status;
  }

  //
  // Ensure exactly 16 characters
  //
  KeyFilename[KEY_FILENAME_LENGTH] = L'\0';

  DEBUG ((DEBUG_INFO, "Generated key filename: %s\n", KeyFilename));
  return EFI_SUCCESS;
}

/**
  Generate verification code from hardware fingerprint.

  @param[in]  HardwareFingerprint Hardware fingerprint data.
  @param[out] VerificationCode    Generated verification code.

  @retval EFI_SUCCESS             Verification code generated successfully.
  @retval other                   Error occurred during generation.

**/
EFI_STATUS
GenerateVerificationCode (
  IN  UINT8   *HardwareFingerprint,
  OUT CHAR16  *VerificationCode
  )
{
  EFI_STATUS Status;
  VOID       *HashContext = NULL;
  UINTN      ContextSize;
  UINT8      SaltedFingerprint[HARDWARE_FINGERPRINT_SIZE + 32];
  UINT8      VerificationHash[SHA256_DIGEST_SIZE];
  CONST CHAR8 *Salt = SALT_STRING;

  if (HardwareFingerprint == NULL || VerificationCode == NULL) {
    return EFI_INVALID_PARAMETER;
  }

  //
  // Prepare salted fingerprint
  //
  CopyMem (SaltedFingerprint, HardwareFingerprint, HARDWARE_FINGERPRINT_SIZE);
  CopyMem (SaltedFingerprint + HARDWARE_FINGERPRINT_SIZE, Salt, AsciiStrLen (Salt));

  //
  // Initialize SHA256 context for verification code generation
  //
  ContextSize = Sha256GetContextSize ();
  HashContext = AllocatePool (ContextSize);
  if (HashContext == NULL) {
    return EFI_OUT_OF_RESOURCES;
  }

  if (!Sha256Init (HashContext)) {
    FreePool (HashContext);
    return EFI_DEVICE_ERROR;
  }

  //
  // Hash the salted fingerprint
  //
  if (!Sha256Update (HashContext, SaltedFingerprint, HARDWARE_FINGERPRINT_SIZE + AsciiStrLen (Salt))) {
    FreePool (HashContext);
    return EFI_DEVICE_ERROR;
  }

  //
  // Finalize hash
  //
  if (!Sha256Final (HashContext, VerificationHash)) {
    FreePool (HashContext);
    return EFI_DEVICE_ERROR;
  }

  //
  // Clean up hash context and sensitive data
  //
  ZeroMem (HashContext, ContextSize);
  FreePool (HashContext);
  ZeroMem (SaltedFingerprint, sizeof (SaltedFingerprint));

  //
  // Convert first 16 bytes of hash to Base32 (32 characters)
  //
  Status = ConvertToBase32 (
             VerificationHash,
             16,  // Use first 16 bytes for 32-character verification code
             VerificationCode,
             VERIFICATION_CODE_LENGTH + 1
             );

  if (EFI_ERROR (Status)) {
    return Status;
  }

  //
  // Ensure exactly 32 characters
  //
  VerificationCode[VERIFICATION_CODE_LENGTH] = L'\0';

  DEBUG ((DEBUG_INFO, "Generated verification code: %s\n", VerificationCode));
  return EFI_SUCCESS;
}

## @file
# CPU SMM driver.
#
# This SMM driver performs SMM initialization, deploy SMM Entry Vector,
# provides CPU specific services in SMM.
#
# Copyright (c) 2009 - 2024, Intel Corporation. All rights reserved.<BR>
# Copyright (c) 2017, AMD Incorporated. All rights reserved.<BR>
# Copyright (C) 2023 - 2024 Advanced Micro Devices, Inc. All rights reserved.<BR>
#
# SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = PiSmmCpuDxeSmm
  MODULE_UNI_FILE                = PiSmmCpuDxeSmm.uni
  FILE_GUID                      = A3FF0EF5-0C28-42f5-B544-8C7DE1E80014
  MODULE_TYPE                    = DXE_SMM_DRIVER
  VERSION_STRING                 = 1.0
  PI_SPECIFICATION_VERSION       = 0x0001000A
  ENTRY_POINT                    = PiCpuSmmEntry

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources]
  PiSmmCpuDxeSmm.c
  PiSmmCpuCommon.c
  PiSmmCpuCommon.h
  MpService.c
  SyncTimer.c
  CpuS3.c
  CpuService.c
  CpuService.h
  SmmProfile.c
  SmmProfile.h
  SmmProfileInternal.h
  SmramSaveState.c
  SmmCpuMemoryManagement.c
  SmmMp.h
  SmmMp.c
  SmmMpPerf.h
  SmmMpPerf.c
  NonMmramMapDxeSmm.c

[Sources.Ia32]
  Ia32/PageTbl.c
  Ia32/SmmFuncsArch.c
  Ia32/SmmProfileArch.c
  Ia32/SmmProfileArch.h
  Ia32/SmiEntry.nasm
  Ia32/SmiException.nasm
  Ia32/Cet.nasm
  Ia32/SmmFuncsArchDxeSmm.c

[Sources.X64]
  X64/PageTbl.c
  X64/SmmFuncsArch.c
  X64/SmmProfileArch.c
  X64/SmmProfileArch.h
  X64/SmiEntry.nasm
  X64/SmiException.nasm
  X64/Cet.nasm
  X64/SmmFuncsArchDxeSmm.c

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  UefiCpuPkg/UefiCpuPkg.dec

[LibraryClasses]
  UefiDriverEntryPoint
  UefiRuntimeServicesTableLib
  PcdLib
  DebugLib
  BaseLib
  SynchronizationLib
  BaseMemoryLib
  MtrrLib
  IoLib
  TimerLib
  MmServicesTableLib
  MemoryAllocationLib
  DebugAgentLib
  HobLib
  PciLib
  LocalApicLib
  SmmCpuPlatformHookLib
  CpuExceptionHandlerLib
  UefiLib
  DxeServicesTableLib
  CpuLib
  ReportStatusCodeLib
  SmmCpuFeaturesLib
  PeCoffGetEntryPointLib
  PerformanceLib
  CpuPageTableLib
  MmSaveStateLib
  SmmCpuSyncLib

[Protocols]
  gEfiSmmConfigurationProtocolGuid         ## PRODUCES
  gEfiSmmCpuProtocolGuid                   ## PRODUCES
  gEfiSmmReadyToLockProtocolGuid           ## NOTIFY
  gEfiSmmCpuServiceProtocolGuid            ## PRODUCES
  gEdkiiSmmMemoryAttributeProtocolGuid     ## PRODUCES
  gEfiMmMpProtocolGuid                     ## PRODUCES
  gEdkiiSmmCpuRendezvousProtocolGuid       ## PRODUCES
  gEfiMpServiceProtocolGuid                ## CONSUMES
  gEfiSmmVariableProtocolGuid              ## CONSUMES

[Guids]
  gEfiAcpiVariableGuid                     ## SOMETIMES_CONSUMES ## HOB # it is used for S3 boot.
  gEdkiiPiSmmMemoryAttributesTableGuid     ## CONSUMES ## SystemTable
  gEfiMemoryAttributesTableGuid            ## CONSUMES ## SystemTable
  gSmmBaseHobGuid                          ## CONSUMES
  gMpInformation2HobGuid                   ## CONSUMES # Assume the HOB must has been created
  gEfiSmmSmramMemoryGuid

[FeaturePcd]
  gUefiCpuPkgTokenSpaceGuid.PcdCpuSmmDebug                         ## CONSUMES
  gUefiCpuPkgTokenSpaceGuid.PcdCpuSmmBlockStartupThisAp            ## CONSUMES
  gUefiCpuPkgTokenSpaceGuid.PcdCpuSmmEnableBspElection             ## CONSUMES
  gUefiCpuPkgTokenSpaceGuid.PcdCpuHotPlugSupport                   ## CONSUMES
  gUefiCpuPkgTokenSpaceGuid.PcdCpuSmmStackGuard                    ## CONSUMES
  gUefiCpuPkgTokenSpaceGuid.PcdCpuSmmProfileEnable                 ## CONSUMES
  gUefiCpuPkgTokenSpaceGuid.PcdCpuSmmProfileRingBuffer             ## CONSUMES
  gUefiCpuPkgTokenSpaceGuid.PcdCpuSmmFeatureControlMsrLock         ## CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdDxeIplSwitchToLongMode         ## CONSUMES
  gUefiCpuPkgTokenSpaceGuid.PcdSmmApPerfLogEnable                  ## CONSUMES

[Pcd]
  gUefiCpuPkgTokenSpaceGuid.PcdCpuSmmApSyncTimeout2                ## CONSUMES
  gUefiCpuPkgTokenSpaceGuid.PcdCpuMaxLogicalProcessorNumber        ## SOMETIMES_CONSUMES
  gUefiCpuPkgTokenSpaceGuid.PcdCpuSmmProfileSize                   ## SOMETIMES_CONSUMES
  gUefiCpuPkgTokenSpaceGuid.PcdCpuSmmStackSize                     ## CONSUMES
  gUefiCpuPkgTokenSpaceGuid.PcdCpuSmmApSyncTimeout                 ## CONSUMES
  gUefiCpuPkgTokenSpaceGuid.PcdCpuHotPlugDataAddress               ## SOMETIMES_PRODUCES
  gUefiCpuPkgTokenSpaceGuid.PcdCpuSmmCodeAccessCheckEnable         ## CONSUMES
  gUefiCpuPkgTokenSpaceGuid.PcdCpuSmmSyncMode                      ## CONSUMES
  gUefiCpuPkgTokenSpaceGuid.PcdCpuSmmShadowStackSize               ## SOMETIMES_CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdAcpiS3Enable                   ## CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdPteMemoryEncryptionAddressOrMask    ## CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdNullPointerDetectionPropertyMask    ## CONSUMES
  gEfiMdeModulePkgTokenSpaceGuid.PcdHeapGuardPropertyMask               ## CONSUMES
  gEfiMdePkgTokenSpaceGuid.PcdControlFlowEnforcementPropertyMask        ## CONSUMES

[FixedPcd]
  gUefiCpuPkgTokenSpaceGuid.PcdCpuSmmMpTokenCountPerChunk               ## CONSUMES

[Depex]
  TRUE

[Pcd.X64]
  gUefiCpuPkgTokenSpaceGuid.PcdCpuSmmRestrictedMemoryAccess        ## CONSUMES

[UserExtensions.TianoCore."ExtraFiles"]
  PiSmmCpuDxeSmmExtra.uni

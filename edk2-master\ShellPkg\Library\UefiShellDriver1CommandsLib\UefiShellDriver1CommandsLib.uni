// /**
//
// (C) Copyright 2016-2017 Hewlett Packard Enterprise Development LP<BR>
// (C) Copyright 2012-2015 Hewlett-Packard Development Company, L.P.<BR>
// Copyright (c) 2010 - 2018, Intel Corporation. All rights reserved.<BR>
// SPDX-License-Identifier: BSD-2-Clause-Patent
//
// Module Name:
//
// UefiShellDriver1CommandsLib.uni
//
// Abstract:
//
// String definitions for UEFI Shell 2.0 driver1 profile commands
//
//
// **/

/=#

#langdef   en-US "english"

#string STR_GEN_PROBLEM           #language en-US "%H%s%N: Unknown flag - '%H%s%N'\r\n"
#string STR_GEN_PROBLEM_VAL       #language en-US "%H%s%N: Bad value - '%H%s%N' for flag - '%H%s%N'\r\n"
#string STR_GEN_PARAM_INV         #language en-US "%H%s%N: Invalid argument - '%H%s%N'\r\n"
#string STR_GEN_TOO_FEW           #language en-US "%H%s%N: Too few arguments\r\n"
#string STR_GEN_TOO_MANY          #language en-US "%H%s%N: Too many arguments\r\n"
#string STR_GEN_INV_HANDLE        #language en-US "%H%s%N: Handle - '%H%s%N' not found\r\n"
#string STR_GEN_PARAM_CONFLICT    #language en-US "%H%s%N: Flags conflict with - '%H%s%N' and '%H%s%N'\r\n"
#string STR_GEN_NO_VALUE          #language en-US "%H%s%N: Missing argument for flag - '%H%s%N'\r\n"
#string STR_GEN_HANDLE_NOT        #language en-US "%H%s%N: Handle [%H%02x%N] is not a valid %s\r\n"
#string STR_GEN_HANDLE_REQ        #language en-US "%H%s%N: Handle required with the specified options\r\n"
#string STR_GEN_PROTOCOL_NF       #language en-US "%H%s%N: The protocol '%H%s%N' is required and not found (%g)\r\n"
#string STR_GEN_FIND_FAIL         #language en-US "%H%s%N: File not found - '%H%s%N'\r\n"
#string STR_GEN_FILE_EXIST        #language en-US "%H%s%N: File already exists - '%H%s%N'\r\n"
#string STR_GEN_FILE_OPEN_FAIL    #language en-US "%H%s%N: Cannot open file - '%H%s%N'\r\n"
#string STR_FILE_WRITE_FAIL       #language en-US "%H%s%N: Write file error - '%H%s%N'\r\n"
#string STR_FILE_READ_FAIL        #language en-US "%H%s%N: Read file error - '%H%s%N'\r\n"
#string STR_GEN_OUT_MEM           #language en-US "%H%s%N: Memory allocation was not successful\r\n"
#string STR_GEN_UEFI_FUNC_ERROR   #language en-US "%H%s%N: UEFI function '%H%s%N' returned an incorrect value for: %s (%x)\r\n"
#string STR_GEN_UEFI_FUNC_WARN    #language en-US "%H%s%N: UEFI function '%H%s%N' returned: %r\r\n"
#string STR_GEN_SFO_HEADER        #language en-US "ShellCommand,"%s"\r\n"

#string STR_DRVDIAG_HEADER        #language en-US "%EAvailable Diagnostics%N.\r\n"
#string STR_DRVDIAG_DRIVER_HEADER #language en-US "Driver [%H%02x%N]: "
#string STR_DRVDIAG_DRIVER_NO_HANDLES #language en-US "No controller handles found.\r\n"

#string STR_HANDLE_RESULT         #language en-US "%H%s%N - Handle [%H%02x%N] Result %r.\r\n"
#string STR_3P_RESULT             #language en-US "%H%s%N - (%H%02x%N,%H%02x%N,%H%02x%N) Result %r.\r\n"
#string STR_CONNECT_NONE          #language en-US "%HConnect%N No drivers could be connected.\r\n"

#string STR_DRVCFG_NONE_FOUND     #language en-US "%HDrvCfg%N No configurable devices were found.\r\n"
#string STR_DRVCFG_COMP           #language en-US "%HDrvCfg%N - operation complete.\r\n"
#string STR_DRVCFG_DONE_HII       #language en-US "Handle[%H%02x%N] successfully updated from file.\r\n"
#string STR_DRVCFG_LINE_HII       #language en-US "Handle[%H%02x%N]                        HII Config Access\r\n"
#string STR_DRVCFG_ALL_LANG       #language en-US "Driver[%H%02x%N]  Ctrl[--]  Lang[%H%a%N]    Driver Configuration"
#string STR_DRVCFG_CTRL_LANG      #language en-US "Driver[%H%02x%N]  Ctrl[%H%02x%N]  Lang[%H%a%N]    Driver Configuration"
#string STR_DRVCFG_CHILD_LANG     #language en-US "Driver[%H%02x%N]  Ctrl[%H%02x%N]  Child[%H%02x%N]  Lang[%H%a%N]    Driver Configuration"
#string STR_DRVCFG_RESTART_S      #language en-US "Restart %s\r\n"
#string STR_DRVCFG_STOP           #language en-US "Stop Controller\n"
#string STR_DRVCFG_ENTER_S        #language en-US "\nPress [ENTER] to %s"
#string STR_DRVCFG_NONE           #language en-US "None\n"
#string STR_DRVCFG_CTLR_S         #language en-US "Controller %s\n"
#string STR_DRVCFG_FORCE_D        #language en-US "Force Default Configuration to DefaultType %08x\n"
#string STR_DRVCFG_VALIDATE       #language en-US "Validate Configuration Options\n"
#string STR_DRVCFG_SET            #language en-US "Set Configuration Options\n"
#string STR_DRVCFG_NOT_SUPPORT    #language en-US "Handle [%H%02x%N] does not support configuration.\n"
#string STR_DRVCFG_OPTIONS_SET    #language en-US " - Options set.  Action Required is "
#string STR_DRVCFG_NOT_SET        #language en-US " - Options not set. Status = %r\n"
#string STR_DRVCFG_DEF_FORCED     #language en-US " - Defaults forced.  Action Required is "
#string STR_DRVCFG_FORCE_FAILED   #language en-US " - Force of defaults failed.  Status = %r\n"
#string STR_DRVCFG_OPTIONS_VALID  #language en-US " - Options valid\n"
#string STR_DRVCFG_OPTIONS_INV    #language en-US " - Options not valid. Status = %r\n"
#string STR_DRVCFG_IN_FILE_NF     #language en-US "DevicePath '%B%s%N' from file not found in HII DB. Skipped.\r\n"


#string STR_DEVICES_HEADER_LINES  #language en-US "%N"
"     T   D\r\n"
"     Y C I\r\n"
"     P F A\r\n"
"CTRL E G G #P #D #C  Device Name\r\n"
"==== = = = == == === =========================================================\r\n"
#string STR_DEVICES_ITEM_LINE     #language en-US "%H%4x%N %1c %1c %1c %2d %2d %3d %s\r\n"
#string STR_DEVICES_ITEM_LINE_SFO #language en-US "DevicesInfo,"%x","%c","%c","%c","%d","%d","%d","%s"\r\n"

#string STR_DRIVERS_HEADER_LINES  #language en-US "%N"
"%H            T   D%N\r\n"
"%HD           Y C I%N\r\n"
"%HR           P F A%N\r\n"
"%HV  VERSION  E G G #D #C DRIVER NAME                         IMAGE NAME%N\r\n"
"== ======== = = = == == =================================== ==========\r\n"
#string STR_DRIVERS_ITEM_LINE     #language en-US "%H%2x%N %08x %1c %1c %1c %2s %2s %-35s %s\r\n"
#string STR_DRIVERS_ITEM_LINE_SFO #language en-US "DriversInfo,"%x","%x","%c","%c","%c","%d","%d","%s","%s"\r\n"

#string STR_DH_OUTPUT_DECODE      #language en-US "%s: %g\r\n"
#string STR_DH_NO_NAME_FOUND      #language en-US "Protocol Name '%s' could not be identified.\r\n"
#string STR_DH_NO_GUID_FOUND      #language en-US "Protocol GUID '%g' could not be identified.\r\n"
#string STR_DH_SFO_OUTPUT         #language en-US "%s, %s, %H%02x%N, %s, &s\r\n"
#string STR_DH_OUTPUT             #language en-US "%H%02x%N: %s\r\n"
#string STR_DH_OUTPUT_ALL_HEADER  #language en-US "Handle dump\r\n"
#string STR_DH_OUTPUT_GUID_HEADER #language en-US "Handle dump by protocol '%g'\r\n"
#string STR_DH_OUTPUT_NAME_HEADER #language en-US "Handle dump by protocol '%s'\r\n"
#string STR_DH_OUTPUT_SINGLE_D    #language en-US "%H%02x%N: %s\r\n"
#string STR_DH_OUTPUT_SINGLE      #language en-US "%H%02x%N: %p\r\n%s"
#string STR_DH_OUTPUT_SFO         #language en-US "%s, %s, %s, %H%02x%N, %s, %s\r\n"
#string STR_DH_OUTPUT_DRIVER1     #language en-US "   Controller Name    : %H%s%N\r\n"
#string STR_DH_OUTPUT_DRIVER2     #language en-US "   Device Path        : %H%s%N\r\n"
                                                  "   Controller Type    : %H%s%N\r\n"
                                                  "   Configuration      : %H%s%N\r\n"
                                                  "   Diagnostics        : %H%s%N\r\n"
#string STR_DH_OUTPUT_DRIVER3     #language en-US "   Managed by         : %H%s%N\r\n"
#string STR_DH_OUTPUT_DRIVER4A    #language en-US "     Drv[%H%02x%N]          : Image(%H%s%N)r\n"
#string STR_DH_OUTPUT_DRIVER4B    #language en-US "     Drv[%H%02x%N]          : %H%s%N\r\n"
#string STR_DH_OUTPUT_DRIVER5     #language en-US "   Parent Controllers : %H%s%N\r\n"
#string STR_DH_OUTPUT_DRIVER5B    #language en-US "     Parent[%H%02x%N]       : %H%s%N\r\n"
#string STR_DH_OUTPUT_DRIVER6     #language en-US "   Child Controllers  : %H%s%N\r\n"
#string STR_DH_OUTPUT_DRIVER6B    #language en-US "     Child[%H%02x%N]        : %H%s%N\r\n"
#string STR_DH_OUTPUT_DRIVER6C    #language en-US "       Child[%H%02x%N]      : %H%s%N\r\n"
#string STR_DH_OUTPUT_DRIVER7     #language en-US "   Driver Name [%H%02x%N]   : %H%s%N\r\n"
#string STR_DH_OUTPUT_DRIVER7B    #language en-US "   Driver Image Name  : %H%s%N\r\n"
#string STR_DH_OUTPUT_DRIVER8     #language en-US "   Driver Version     : %H%08x%N\r\n"
                                                  "   Driver Type        : %H%s%N\r\n"
                                                  "   Configuration      : %H%s%N\r\n"
                                                  "   Diagnostics        : %H%s%N\r\n"
#string STR_DH_OUTPUT_DRIVER9     #language en-US "   Managing           : %H%s%N\r\n"
#string STR_DH_OUTPUT_DRIVER9B    #language en-US "     Ctrl[%H%02x%N]         : %H%s%N\r\n"

#string STR_DEV_TREE_OUTPUT       #language en-US "Ctrl[%H%02x%N] %s\r\n"

#string STR_UNLOAD_CONF           #language en-US "%HUnload%N - Handle [%H%02x%N].  [y/n]?\r\n"

#string STR_OPENINFO_HEADER_LINE  #language en-US "Handle %H%02x%N (%H%0p%N)\r\n"
#string STR_OPENINFO_LINE         #language en-US "  Drv[%H%02x%N] Ctrl[%H%02x%N] Cnt(%H%02x%N) %H%s Image%N(%s)\r\n"
#string STR_OPENINFO_MIN_LINE     #language en-US "  Drv[%H%02x%N] Ctrl[  ] Cnt(%H%02x%N) %H%s Image%N(%s)\r\n"

#string STR_DRV_DIAG_ITEM_LINE    #language en-US "  Drv[%H%02x%N] Ctrl[%H%02x%N] Child[%H%02x%N]\r\n"

#string STR_GET_HELP_DRVCFG       #language en-US ""
".TH drvcfg 0 "configure a UEFI driver."\r\n"
".SH NAME\r\n"
"Invokes the driver configuration.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"DRVCFG [-l XXX] [-c] [-f <Type>|-v|-s] \r\n"
"       [DriverHandle [DeviceHandle [ChildHandle]]] [-i filename] [-o filename]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  -c           - Configures all child devices.\r\n"
"  -l           - Configures using the ISO 3066 language specified by XXX.\r\n"
"  -f           - Forces defaults.\r\n"
"  -v           - Validates options.\r\n"
"  -s           - Sets options.\r\n"
"  -i           - Receives configuration updates from an input file.\r\n"
"  -o           - Exports the settings of the specified driver instance to a\r\n"
"                 file.\r\n"
"  Type         - Specifies the type of default configuration options to force on the\r\n"
"                 controller.\r\n"
"                   0         - Standard Defaults.\r\n"
"                   1         - Manufacturing Defaults.\r\n"
"                   2         - Safe Defaults.\r\n"
"                   4000-FFFF - Custom Defaults.\r\n"
"  DriverHandle - Specifies the the handle of the driver to configure.\r\n"
"  DeviceHandle - Specifies the handle of a device that the DriverHandle is managing.\r\n"
"  ChildHandle  - Specifies the handle of a device that is a child of the DeviceHandle. \r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. Default Type:\r\n"
"     0 - Safe Defaults. Places a controller in a safe configuration with\r\n"
"         the greatest probability of functioning correctly in a platform.\r\n"
"     1 - Manufacturing Defaults. Optional type that places the controller in\r\n"
"         a configuration suitable for a manufacturing and test environment.\r\n"
"     2 - Custom Defaults. Optional type that places the controller in a\r\n"
"         custom configuration.\r\n"
"     3 - Performance Defaults. Optional type that places the controller in a\r\n"
"         configuration that maximizes the controller's performance in a \r\n"
"         platform. \r\n"
"     Other Value - Depends on the driver's implementation.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To display the list of devices that are available for configuration:\r\n"
"    Shell> drvcfg\r\n"
" \r\n"
"  * To display the list of devices and child devices that are available for\r\n"
"    configuration:\r\n"
"    Shell> drvcfg -c\r\n"
" \r\n"
"  * To force defaults on all devices:\r\n"
"    Shell> drvcfg -f 0\r\n"
" \r\n"
"  * To force defaults on all devices that are managed by driver 0x17:\r\n"
"    Shell> drvcfg -f 0 17\r\n"
" \r\n"
"  * To force defaults on device 0x28 that is managed by driver 0x17:\r\n"
"    Shell> drvcfg -f 0 17 28\r\n"
" \r\n"
"  * To force defaults on all child devices of device 0x28 that is managed by\r\n"
"    driver 0x17:\r\n"
"    Shell> drvcfg -f 0 17 28 -c\r\n"
" \r\n"
"  * To force defaults on child device 0x30 of device 0x28 that is managed by\r\n"
"    driver 0x17:\r\n"
"    Shell> drvcfg -f 0 17 28 30\r\n"
" \r\n"
"  * To validate options on all devices:\r\n"
"    Shell> drvcfg -v\r\n"
" \r\n"
"  * To validate options on all devices that are managed by driver 0x17:\r\n"
"    Shell> drvcfg -v 17\r\n"
" \r\n"
"  * To validate options on device 0x28 that is managed by driver 0x17:\r\n"
"    Shell> drvcfg -v 17 28\r\n"
" \r\n"
"  * To validate options on all child devices of device 0x28 that is managed by\r\n"
"    driver 0x17:\r\n"
"    Shell> drvcfg -v 17 28 -c\r\n"
" \r\n"
"  * To validate options on child device 0x30 of device 0x28 that is managed by\r\n"
"    driver 0x17:\r\n"
"    Shell> drvcfg -v 17 28 30\r\n"
" \r\n"
"  * To set options on device 0x28 that is managed by driver 0x17: \r\n"
"    Shell> drvcfg -s 17 28\r\n"
" \r\n"
"  * To set options on child device 0x30 of device 0x28 that is managed by\r\n"
"    driver 0x17:\r\n"
"    Shell> drvcfg -s 17 28 30\r\n"
" \r\n"
"  * To set options on device 0x28 that is managed by driver 0x17 in English:\r\n"
"    Shell> drvcfg -s 17 28 -l eng\r\n"
" \r\n"
"  * To set options on device 0x28 that is managed by driver 0x17 in Spanish:\r\n"
"    Shell> drvcfg -s 17 28 -l spa\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  SHELL_SUCCESS              The action was completed as requested.\r\n"
"  SHELL_SECURITY_VIOLATION   This function was not performed due to a security\r\n"
"                             violation.\r\n"
"  SHELL_UNSUPPORTED          The action as requested was unsupported.\r\n"
"  SHELL_INVALID_PARAMETER    One of the passed in parameters was incorrectly\r\n"
"                             formatted or its value was out of bounds.\r\n"

#string STR_GET_HELP_DRIVERS      #language en-US ""
".TH drivers 0 "display a list of drivers"\r\n"
".SH NAME\r\n"
"Displays the UEFI driver list.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"DRIVERS [-l XXX] [-sfo] \r\n"
".SH OPTIONS\r\n"
" \r\n"
"  -l   - Displays drivers using the specified language (e.g. ISO 639-2) \r\n"
"  -sfo - Displays information as described in Standard-Format Output.\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. This command displays a list of information for drivers that follow the\r\n"
"     UEFI Driver Model in the UEFI environment. The list includes:\r\n"
"       DRV         - The handle number of the UEFI driver.\r\n"
"       VERSION     - The version number of the UEFI driver.\r\n"
"       TYPE        - The driver type:\r\n"
"                       [B] - Bus Driver\r\n"
"                       [D] - Device Driver\r\n"
"       CFG         - Driver supports the Driver Configuration Protocol.\r\n"
"       DIAG        - Driver supports the Driver Diagnostics Protocol.\r\n"
"       #D          - The number of devices that this driver is managing.\r\n"
"       #C          - The number of child devices that this driver has produced.\r\n"
"       DRIVER NAME - The name of the driver from the Component Name Protocol.\r\n"
"       IMAGE PATH  - The file path from which the driver was loaded.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To display the list:\r\n"
"    Shell> drivers\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  SHELL_SUCCESS              The action was completed as requested.\r\n"
"  SHELL_SECURITY_VIOLATION   This function was not performed due to a security\r\n"
"                             violation.\r\n"
"  SHELL_INVALID_PARAMETER    One of the passed in parameters was incorrectly\r\n"
"                             formatted or its value was out of bounds.\r\n"

#string STR_GET_HELP_DISCONNECT   #language en-US ""
".TH disconnect 0 "disconnect a driver"\r\n"
".SH NAME\r\n"
"Disconnects one or more drivers from the specified devices. \r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"DISCONNECT DeviceHandle [DriverHandle [ChildHandle]] \r\n"
"DISCONNECT -r [-nc] \r\n"
".SH OPTIONS\r\n"
" \r\n"
"NOTES:\r\n"
"  -r           - Disconnects all drivers from all devices, then reconnect\r\n"
"                 consoles.\r\n"
"  -nc          - Do not reconnect the console devices.\r\n"
"  DeviceHandle - Specifies a device handle (a hexadecimal number). If not\r\n"
"                 specified, then disconnect DriverHandle.\r\n"
"  DriverHandle - Specifies a driver handle (a hexadecimal number).\r\n"
"  ChildHandle  - Specifies a child handle of a device (a hexadecimal number).\r\n"
"                 If not specified, then all child handles of DeviceHandle are\r\n"
"                 disconnected.\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. If the 'DriverHandle' parameter is not specified, the default is to\r\n"
"     disconnect 'DeviceHandle'.\r\n"
"  2. If the 'ChildHandle' parameter is not specified, the default is to\r\n"
"     disconnect all child handles of the 'DeviceHandle'.\r\n"
"  3. If the '-r' option is specified, all consoles and drivers will be\r\n"
"     disconnected from all devices in the system, then consoles are\r\n"
"     reconnected. If the '-nc' option is also specified, then console devices\r\n"
"     are not reconnected.\r\n"
"  4. This command does not support output redirection.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To disconnect all drivers from all devices, then reconnect console\r\n"
"    devices:\r\n"
"    Shell> disconnect -r\r\n"
" \r\n"
"  * To disconnect all drivers from all devices, including console devices:\r\n"
"    Shell> disconnect -r -nc\r\n"
" \r\n"
"  * To disconnect all drivers from device 0x28:\r\n"
"    fs0:\> disconnect 28\r\n"
" \r\n"
"  * To disconnect driver 0x17 from device 0x28:\r\n"
"    fs0:\> disconnect 28 17\r\n"
" \r\n"
"  * To disconnect driver 0x17 from controlling the child 0x32 of device 0x28:\r\n"
"    fs0:\> disconnect 28 17 32\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  SHELL_SUCCESS              The action was completed as requested.\r\n"
"  SHELL_SECURITY_VIOLATION   This function was not performed due to a security\r\n"
"                             violation.\r\n"
"  SHELL_INVALID_PARAMETER    One of the passed in parameters was incorrectly\r\n"
"                             formatted or its value was out of bounds.\r\n"

#string STR_GET_HELP_DH           #language en-US ""
".TH dh 0 "displays list of handles"\r\n"
".SH NAME\r\n"
"Displays the device handles in the UEFI environment. \r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"DH [-l <lang>] [handle | -p <prot_id>] [-d] [-v] \r\n"
".SH OPTIONS\r\n"
" \r\n"
"  -p     - Dumps all handles of a protocol specified by the GUID.\r\n"
"  -d     - Dumps UEFI Driver Model-related information.\r\n"
"  -l     - Dumps information using the language codes (e.g. ISO 639-2).\r\n"
"  -sfo   - Displays information as described in Standard-Format Output.\r\n"
"  -v     - Dumps verbose information about a specific handle.\r\n"
"  handle - Specifies a handle to dump information about (a hexadecimal number).\r\n"
"           If not present, then all information will be dumped.\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. When neither 'handle' nor 'prot_id' is specified, a list of all the\r\n"
"     device handles in the UEFI environment is displayed. \r\n"
"  2. The '-d' option displays UEFI Driver Model related information including\r\n"
"     parent handles, child handles, all drivers installed on the handle, etc.\r\n"
"  3. The '-v' option displays verbose information for the specified handle\r\n"
"     including all the protocols on the handle and their details.\r\n"
"  4. If the '-p' option is specified, all handles containing the specified\r\n"
"     protocol will be displayed. Otherwise, the 'handle' parameter has to be\r\n"
"     specified for display. In this case, the '-d' option will be enabled\r\n"
"     automatically if the '-v' option is not specified.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To display all handles and display one screen at a time:\r\n"
"    Shell> dh -b\r\n"
" \r\n"
"  * To display the detailed information on handle 0x30:\r\n"
"    Shell> dh 30\r\n"
" \r\n"
"  * To display all handles with 'diskio' protocol:\r\n"
"    Shell> dh -p diskio\r\n"
" \r\n"
"  * To display all handles with 'LoadedImage' protocol and break when the screen is\r\n"
"    full:\r\n"
"    Shell> dh -p LoadedImage -b\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  SHELL_SUCCESS              The action was completed as requested.\r\n"
"  SHELL_SECURITY_VIOLATION   This function was not performed due to a security\r\n"
"                             violation.\r\n"
"  SHELL_INVALID_PARAMETER    One of the passed in parameters was incorrectly\r\n"
"                             formatted or its value was out of bounds.\r\n"

#string STR_GET_HELP_DEVTREE      #language en-US ""
".TH devtree 0 "display device tree"\r\n"
".SH NAME\r\n"
"Displays the UEFI Driver Model compliant device tree.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"DEVTREE [-b] [-d] [-l XXX] [DeviceHandle] \r\n"
".SH OPTIONS\r\n"
" \r\n"
"  -b           - Displays one screen at a time.\r\n"
"  -d           - Displays the device tree using device paths.\r\n"
"  -l           - Displays the device tree using the specified language.\r\n"
"  DeviceHandle - Displays the device tree below a certain handle.\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. This command prints a tree of devices that are being managed by drivers\r\n"
"     that follow the UEFI Driver Model. By default, the devices are printed in\r\n"
"     device names that are retrieved from the Component Name Protocol.\r\n"
"  2. If the option -d is specified, the device paths will be printed instead.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To display the tree of all devices compliant with the UEFI Driver Model:\r\n"
"    Shell> devtree\r\n"
" \r\n"
"  * To display the tree of all devices below device 28 compliant with the UEFI\r\n"
"    Driver Model:\r\n"
"    Shell> devtree 28\r\n"
" \r\n"
"  * To display the tree of all devices compliant with the UEFI Driver Model\r\n"
"    one screen at a time:\r\n"
"    Shell> devtree -b\r\n"
" \r\n"

#string STR_GET_HELP_DEVICES      #language en-US ""
".TH devices 0 "display a list of devices"\r\n"
".SH NAME\r\n"
"Displays the list of devices managed by UEFI drivers. \r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"DEVICES [-b] [-l XXX] [-sfo] \r\n"
".SH OPTIONS\r\n"
" \r\n"
"  -b     - Display one screen at a time\r\n"
"  -l XXX - Display devices using the specified ISO 639-2 language\r\n"
"  -sfo   - Displays information as described in Standard-Format Output.\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. The command prints a list of devices that are being managed by drivers\r\n"
"     that follow the UEFI Driver Model.\r\n"
"  2. Display Format:\r\n"
"       CTRL         - The handle number of the UEFI device\r\n"
"       TYPE         - The device type:\r\n"
"                        [R] - Root Controller\r\n"
"                        [B] - Bus Controller\r\n"
"                        [D] - Device Controller\r\n"
"       CFG          - A managing driver supports the Driver Configuration\r\n"
"                      Protocol.  Yes if 'Y' or 'X'; No if 'N' or '-'.\r\n"
"       DIAG         - A managing driver supports the Driver Diagnostics\r\n"
"                      Protocol.  Yes if 'Y' or 'X'; No if 'N' or '-'.\r\n"
"       #P           - The number of parent controllers for this device\r\n"
"       #D           - The number of drivers managing the device\r\n"
"       #C           - The number of child controllers produced by this device\r\n"
"       DEVICE NAME  - The name of the device from the Component Name Protocol\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To display all devices compliant with the UEFI Driver Model:\r\n"
"    Shell> devices\r\n"
" \r\n"

#string STR_GET_HELP_CONNECT      #language en-US ""
".TH connect 0 "connect a driver"\r\n"
".SH NAME\r\n"
"Binds a driver to a specific device and starts the driver. \r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"CONNECT [[DeviceHandle] [DriverHandle] | [-c] | [-r]] \r\n"
".SH OPTIONS\r\n"
" \r\n"
"  -c           - Connects console devices\r\n"
"  -r           - Connects recursively\r\n"
"  DeviceHandle - Specifies a device handle in hexadecimal format.\r\n"
"  DriverHandle - Specifies a driver handle in hexadecimal format.\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. If no 'DeviceHandle' parameter is specified, all device handles in the\r\n"
"     current system will be the default.\r\n"
"  2. If no 'DriverHandle' parameter is specified, all matched drivers will be\r\n"
"     bound to the specified device.\r\n"
"  3. If 'DriverHandle' parameter is provided, the specified driver will have\r\n"
"     highest priority on connecting the device(s).\r\n"
"  4. If the '-c' option is specified, only console devices described in the\r\n"
"     UEFI Shell environment variables and related devices will be connected.\r\n"
"  5. If the '-r' option is specified, the command will recursively scan all\r\n"
"     handles and check to see if any loaded or embedded driver can match the\r\n"
"     specified device. If so, the driver will be bound to the device.\r\n"
"     Additionally, if more device handles are created during the binding, \r\n"
"     these handles will also be checked to see if a matching driver can bind\r\n"
"     to these devices as well. The process is repeated until no more drivers\r\n"
"     are able to connect to any devices. However, without the option, the\r\n"
"     newly created device handles will not be further bound to any\r\n"
"     drivers.\r\n"
"  6. If only a single handle is specified and the handle has an\r\n"
"     EFI_DRIVER_BINDING_PROTOCOL on it, then the handle is assumed to be a\r\n"
"     driver handle. Otherwise, it is assumed to be a device handle.\r\n"
"  7. If no parameters are specified, then the command will attempt to bind\r\n"
"     all proper drivers to all devices without recursion. Each connection\r\n"
"     status will be displayed.\r\n"
"  8. Output redirection is not supported for 'connect -r' usage.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To connect all drivers to all devices recursively:\r\n"
"    Shell> connect -r\r\n"
" \r\n"
"  * To display all connections:\r\n"
"    Shell> connect\r\n"
" \r\n"
"  * To connect drivers with 0x17 as highest priority to all the devices they\r\n"
"    can manage:\r\n"
"    Shell> connect 17\r\n"
" \r\n"
"  * To connect all possible drivers to device 0x19:\r\n"
"    Shell> connect 19\r\n"
" \r\n"
"  * To connect drivers with 0x17 as highest priority to device 0x19 they can\r\n"
"    manage:\r\n"
"    Shell> connect 19 17\r\n"
" \r\n"
"  * To connect console devices described in the UEFI Shell environment\r\n"
"    variables:\r\n"
"    Shell> connect -c\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  SHELL_SUCCESS              The action was completed as requested.\r\n"
"  SHELL_SECURITY_VIOLATION   This function was not performed due to a security\r\n"
"                             violation.\r\n"
"  SHELL_INVALID_PARAMETER    One of the passed in parameters was incorrectly\r\n"
"                             formatted or its value was out of bounds.\r\n"

#string STR_GET_HELP_OPENINFO     #language en-US ""
".TH openinfo 0 "display info about a handle."\r\n"
".SH NAME\r\n"
"Displays the protocols and agents associated with a handle. \r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"OPENINFO Handle [-b] \r\n"
".SH OPTIONS\r\n"
" \r\n"
"  -b     - Displays one screen at a time.\r\n"
"  Handle - Displays open protocol information for the specified handle.\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. This command is used to display the open protocols on a given handle.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To show open protocols on handle 0x23:\r\n"
"    Shell> openinfo 23\r\n"
" \r\n"

#string STR_GET_HELP_DRVDIAG      #language en-US ""
".TH drvdiag 0 "diagnose a driver"\r\n"
".SH NAME\r\n"
"Invokes the Driver Diagnostics Protocol. \r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"DRVDIAG [-c] [-l XXX] [-s|-e|-m] [DriverHandle [DeviceHandle [ChildHandle]]] \r\n"
".SH OPTIONS\r\n"
" \r\n"
"  -c           - Diagnoses all child devices.\r\n"
"  -l           - Diagnoses using the ISO 639-2 language specified by XXX.\r\n"
"  -s           - Runs diagnostics in standard mode.\r\n"
"  -e           - Runs diagnostics in extended mode.\r\n"
"  -m           - Runs diagnostics in manufacturing mode.\r\n"
"  DriverHandle - Specifies the handle of the driver to diagnose.\r\n"
"  DeviceHandle - Specifies the handle of a device that DriverHandle is managing.\r\n"
"  ChildHandle  - Specifies the handle of a device that is a child of DeviceHandle.\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. This command invokes the Driver Diagnostics Protocol.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To display the list of devices that are available for diagnostics:\r\n"
"    Shell> drvdiag\r\n"
" \r\n"
"  * To display the list of devices and child devices that are available for\r\n"
"    diagnostics:\r\n"
"    Shell> drvdiag -c\r\n"
" \r\n"
"  * To run diagnostics in standard mode on all devices:\r\n"
"    Shell> drvdiag -s\r\n"
" \r\n"
"  * To run diagnostics in standard mode on all devices in English:\r\n"
"    Shell> drvdiag -s -l eng\r\n"
" \r\n"
"  * To run diagnostics in standard mode on all devices in Spanish:\r\n"
"    Shell> drvdiag -s -l spa\r\n"
" \r\n"
"  * To run diagnostics in standard mode on all devices and child devices:\r\n"
"    Shell> drvdiag -s -c\r\n"
" \r\n"
"  * To run diagnostics in extended mode on all devices:\r\n"
"    Shell> drvdiag -e\r\n"
" \r\n"
"  * To run diagnostics in manufacturing mode on all devices:\r\n"
"    Shell> drvdiag -m\r\n"
" \r\n"
"  * To run diagnostics in standard mode on all devices managed by driver 0x17:\r\n"
"    Shell> drvdiag -s 17\r\n"
" \r\n"
"  * To run diagnostics in standard mode on device 0x28 managed by  driver 0x17:\r\n"
"    Shell> drvdiag -s 17 28\r\n"
" \r\n"
"  * To run diagnostics in standard mode on all child devices of device 0x28\r\n"
"    managed by driver 0x17:\r\n"
"    Shell> drvdiag -s 17 28 -c\r\n"
" \r\n"
"  * To run diagnostics in standard mode on child device 0x30 of device 0x28\r\n"
"    managed by driver 0x17:\r\n"
"    Shell> drvdiag -s 17 28 30\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  SHELL_SUCCESS              The action was completed as requested.\r\n"
"  SHELL_SECURITY_VIOLATION   This function was not performed due to a security\r\n"
"                             violation.\r\n"
"  SHELL_INVALID_PARAMETER    One of the passed in parameters was incorrectly\r\n"
"                             formatted or its value was out of bounds.\r\n"

#string STR_GET_HELP_RECONNECT    #language en-US ""
".TH reconnect 0 "reconnect drivers"\r\n"
".SH NAME\r\n"
"Reconnects drivers to the specific device. \r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"RECONNECT DeviceHandle [DriverHandle [ChildHandle]]\r\n"
"RECONNECT -r \r\n"
".SH OPTIONS\r\n"
" \r\n"
"  -r           - Reconnects drivers to all devices.\r\n"
"  DeviceHandle - Specifies a device handle (a hexadecimal number).\r\n"
"  DriverHandle - Specifies a driver handle (a hexadecimal number). If not specified, all\r\n"
"                 drivers on the specified device will be reconnected. \r\n"
"  ChildHandle  - Specifies the child handle of device (a hexadecimal number). If not\r\n"
"                 specified, then all child handles of the specified device are\r\n"
"                reconnected.\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. This command reconnects drivers to the specific device. It will first\r\n"
"     disconnect the specified driver from the specified device and then connect\r\n"
"     the driver to the device recursively.\r\n"
"  2. If the -r option is used, then all drivers will be reconnected to all\r\n"
"     devices. Any drivers that are bound to any devices will be disconnected\r\n"
"     first and then connected recursively.\r\n"
"  3. See the connect and disconnect commands for more details. \r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To reconnect all drivers to all devices:\r\n"
"    Shell> reconnect -r\r\n"
" \r\n"
"  * To reconnect all drivers to device 0x28:\r\n"
"    fs0:\> reconnect 28\r\n"
" \r\n"
"  * To disconnect 0x17 from 0x28 then reconnect drivers with 0x17 as highest\r\n"
"    priority to device 0x28:\r\n"
"    fs0:\> reconnect 28 17\r\n"
" \r\n"
"  * To disconnect 0x17 from 0x28 destroying child 0x32 then reconnect drivers\r\n"
"    with 0x17 as highest priority to device 0x28\r\n"
"    fs0:\> reconnect 28 17 32\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  SHELL_SUCCESS              The action was completed as requested.\r\n"
"  SHELL_SECURITY_VIOLATION   This function was not performed due to a security\r\n"
"                             violation.\r\n"
"  SHELL_INVALID_PARAMETER    One of the passed in parameters was incorrectly\r\n"
"                             formatted or its value was out of bounds.\r\n"

#string STR_GET_HELP_UNLOAD       #language en-US ""
".TH unload 0 "unload a driver"\r\n"
".SH NAME\r\n"
"Unloads a driver image that was already loaded. \r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"UNLOAD [-n] [-v|-verbose] Handle \r\n"
".SH OPTIONS\r\n"
" \r\n"
"  -n           - Skips all prompts during unloading, so that it can be used\r\n"
"                 in a script file.\r\n"
"  -v, -verbose - Dumps verbose status information before the image is unloaded.\r\n"
"  Handle       - Specifies the handle of driver to unload, always taken as hexadecimal number.\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. The '-n' option can be used to skip all prompts during unloading.\r\n"
"  2. If the '-v' option is specified, verbose image information will be\r\n"
"     displayed before the image is unloaded.\r\n"
"  3. Only drivers that support unloading can be successfully unloaded.\r\n"
"  4. Use the 'LOAD' command to load a driver.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To find the handle for the UEFI driver image to unload:\r\n"
"    Shell> dh -b\r\n"
" \r\n"
"  * To unload the UEFI driver image with handle 27:\r\n"
"    Shell> unload 27\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  SHELL_SUCCESS              The action was completed as requested.\r\n"
"  SHELL_SECURITY_VIOLATION   This function was not performed due to a security\r\n"
"                             violation.\r\n"
"  SHELL_INVALID_PARAMETER    One of the passed in parameters was incorrectly\r\n"
"                             formatted or its value was out of bounds.\r\n"

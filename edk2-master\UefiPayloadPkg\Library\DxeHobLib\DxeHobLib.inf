## @file
# HOB Library implementation for Payload Phase.
#
# Copyright (c) 2021, Intel Corporation. All rights reserved.<BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = DxeHobLib
  MODULE_UNI_FILE                = DxeHobLib.uni
  FILE_GUID                      = 1a15b8b3-3e8a-4698-87b9-65aad9993b52
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = HobLib|DXE_DRIVER DXE_RUNTIME_DRIVER SMM_CORE DXE_SMM_DRIVER UEFI_APPLICATION UEFI_DRIVER

#
#  VALID_ARCHITECTURES           = IA32 X64 EBC
#

[Sources]
  HobLib.c


[Packages]
  MdePkg/MdePkg.dec
  UefiPayloadPkg/UefiPayloadPkg.dec


[LibraryClasses]
  BaseMemoryLib
  DebugLib
  DxeHobListLib

##  @file
# Provides interface to shell functionality for shell commands and applications.
#
# (C) Copyright 2016 Hewlett Packard Enterprise Development LP<BR>
# Copyright (c) 2006 - 2018, Intel Corporation. All rights reserved.<BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#
##

[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = UefiShellLib
  FILE_GUID                      = 449D0F00-2148-4a43-9836-F10B3980ECF5
  MODULE_TYPE                    = UEFI_DRIVER
  VERSION_STRING                 = 1.2
  LIBRARY_CLASS                  = ShellLib|UEFI_APPLICATION UEFI_DRIVER DXE_RUNTIME_DRIVER DXE_DRIVER
  CONSTRUCTOR                    = ShellLibConstructor
  DESTRUCTOR                     = ShellLibDestructor

#
#  VALID_ARCHITECTURES           = IA32 X64 EBC
#

[Sources.common]
  UefiShellLib.c
  UefiShellLib.h

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  ShellPkg/ShellPkg.dec

[LibraryClasses]
  UefiBootServicesTableLib
  MemoryAllocationLib
  DevicePathLib
  BaseLib
  BaseMemoryLib
  DebugLib
  FileHandleLib
  PrintLib
  UefiLib
  HiiLib
  SortLib

[Protocols]
  gEfiUnicodeCollation2ProtocolGuid             ## CONSUMES

  # shell 2.0
  gEfiShellProtocolGuid                         ## SOMETIMES_CONSUMES
  gEfiShellParametersProtocolGuid               ## SOMETIMES_CONSUMES

  # 'old' shell
  gEfiShellEnvironment2Guid                     ## SOMETIMES_CONSUMES
  gEfiShellInterfaceGuid                        ## SOMETIMES_CONSUMES

[Guids]
  gEfiFileInfoGuid                              ## SOMETIMES_CONSUMES ## GUID
  gEfiShellEnvironment2ExtGuid                  ## SOMETIMES_CONSUMES ## GUID

[Pcd.common]
  gEfiShellPkgTokenSpaceGuid.PcdShellLibAutoInitialize     ## CONSUMES
  gEfiShellPkgTokenSpaceGuid.PcdShellPrintBufferSize       ## CONSUMES

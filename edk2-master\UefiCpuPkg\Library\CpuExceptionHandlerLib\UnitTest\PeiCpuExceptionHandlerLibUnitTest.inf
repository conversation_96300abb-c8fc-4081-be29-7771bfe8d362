## @file
# Unit tests of the PeiCpuExceptionHandlerLib instance.
#
# Copyright (c) 2022, Intel Corporation. All rights reserved.<BR>
# SPDX-License-Identifier: BSD-2-Clause-Patent
##

[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = CpuExceptionHandlerPeiTest
  FILE_GUID                      = 39A96CF7-F369-4357-9234-4B52F98A007F
  MODULE_TYPE                    = PEIM
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = PeiEntryPoint

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#
[Sources.Ia32]
  Ia32/ArchExceptionHandlerTestAsm.nasm
  Ia32/ArchExceptionHandlerTest.c

[Sources.X64]
  X64/ArchExceptionHandlerTestAsm.nasm
  X64/ArchExceptionHandlerTest.c

[Sources.common]
  CpuExceptionHandlerTest.h
  CpuExceptionHandlerTestCommon.c
  PeiCpuExceptionHandlerUnitTest.c

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  UefiCpuPkg/UefiCpuPkg.dec

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  DebugLib
  UnitTestLib
  MemoryAllocationLib
  CpuExceptionHandlerLib
  PeimEntryPoint
  HobLib
  PeiServicesLib
  CpuPageTableLib
  PeiServicesTablePointerLib

[Pcd]
  gEfiMdeModulePkgTokenSpaceGuid.PcdCpuStackGuard   ## CONSUMES
  gUefiCpuPkgTokenSpaceGuid.PcdCpuApStackSize       ## CONSUMES

[Ppis]
  gEfiPeiMpServices2PpiGuid                         ## CONSUMES

[Depex]
  gEfiPeiMpServices2PpiGuid AND
  gEfiPeiMemoryDiscoveredPpiGuid

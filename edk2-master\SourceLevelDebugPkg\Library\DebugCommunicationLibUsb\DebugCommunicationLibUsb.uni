// /** @file
// Debug Communication Library instance based on usb debug port.
//
// Debug Communication Library instance based on USB debug port.
//
// Copyright (c) 2010 - 2014, Intel Corporation. All rights reserved.<BR>
//
// SPDX-License-Identifier: BSD-2-Clause-Patent
//
// **/


#string STR_MODULE_ABSTRACT             #language en-US "Debug Communication Library instance based on USB debug port"

#string STR_MODULE_DESCRIPTION          #language en-US "Debug Communication Library instance based on USB debug port."


## @file
#  Library functions for PlatformSecLib.
#
#  Null instance of Platform Sec Lib.
#
#  Copyright (c) 2013 - 2015, Intel Corporation. All rights reserved.<BR>
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = PlatformSecLibNull
  MODULE_UNI_FILE                = PlatformSecLibNull.uni
  FILE_GUID                      = 6695974D-968C-420b-80B9-7870CD20118F
  MODULE_TYPE                    = SEC
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = PlatformSecLib

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources]
  PlatformSecLibNull.c

[Packages]
  MdePkg/MdePkg.dec
  UefiCpuPkg/UefiCpuPkg.dec

## @file
# Unit tests of the CpuPageTableLib instance of the CpuPageTableLib class
#
# Copyright (c) 2022, Intel Corporation. All rights reserved.<BR>
# SPDX-License-Identifier: BSD-2-Clause-Patent
##

[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = CpuPageTableLibUnitTestHost
  FILE_GUID                      = D8DC32C2-7272-43A8-B145-1723BED8E119
  MODULE_TYPE                    = HOST_APPLICATION
  VERSION_STRING                 = 1.0

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources]
  CpuPageTableLibUnitTestHost.c
  RandomTest.c
  TestHelper.c
  RandomNumber.c
  RandomTest.h
  CpuPageTableLibUnitTest.h

[Packages]
  MdePkg/MdePkg.dec
  UefiCpuPkg/UefiCpuPkg.dec
  UnitTestFrameworkPkg/UnitTestFrameworkPkg.dec
  CryptoPkg/CryptoPkg.dec

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  DebugLib
  CpuPageTableLib
  UnitTestLib
  MemoryAllocationLib
  BaseCryptLib

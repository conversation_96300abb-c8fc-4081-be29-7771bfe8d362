## @file
#  INI configuration parsing library.
#
#  This library parses the INI configuration file.
#
#  Copyright (c) 2016 - 2018, Intel Corporation. All rights reserved.<BR>
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = IniParsingLib
  MODULE_UNI_FILE                = IniParsingLib.uni
  FILE_GUID                      = 6E4CD200-43E5-43CE-89E9-D715CF9526C4
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = IniParsingLib

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64 EBC
#

[Sources]
  IniParsingLib.c

[Packages]
  MdePkg/MdePkg.dec
  SignedCapsulePkg/SignedCapsulePkg.dec

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  DebugLib
  MemoryAllocationLib

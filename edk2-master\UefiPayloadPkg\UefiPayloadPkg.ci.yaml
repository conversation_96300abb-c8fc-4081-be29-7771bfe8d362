## @file
# Core CI configuration for UefiPayloadPkg
#
# Copyright (c) 2021, Intel Corporation. All rights reserved.<BR>
# SPDX-License-Identifier: BSD-2-Clause-Patent
##
{
    "PrEval": {
        "DscPath": "UefiPayloadPkg.dsc",
    },
    ## options defined .pytool/Plugin/LicenseCheck
    "LicenseCheck": {
        "IgnoreFiles": []
    },
    "EccCheck": {
        ## Exception sample looks like below:
        ## "ExceptionList": [
        ##     "<ErrorID>", "<KeyWord>"
        ## ]
        "ExceptionList": [
        ],
        ## Both file path and directory path are accepted.
        "IgnoreFiles": [
            "Tools/",
            "Include/Coreboot.h",
            "Library/CbParseLib/CbParseLib.c",
            "PayloadLoaderPeim/ElfLib/ElfCommon.h",
            "PayloadLoaderPeim/ElfLib/Elf32.h",
            "PayloadLoaderPeim/ElfLib/Elf64.h"
        ]
    },
    ## options defined .pytool/Plugin/CompilerPlugin
    "CompilerPlugin": {
        "DscPath": "UefiPayloadPkg.dsc"
    },

    ## options defined .pytool/Plugin/HostUnitTestCompilerPlugin
    "HostUnitTestCompilerPlugin": {
        "DscPath": "" # Don't support this test
    },

    ## options defined .pytool/Plugin/CharEncodingCheck
    "CharEncodingCheck": {
        "IgnoreFiles": []
    },

    ## options defined .pytool/Plugin/DependencyCheck
    "DependencyCheck": {
        "AcceptableDependencies": [],
        # For host based unit tests
        "AcceptableDependencies-HOST_APPLICATION":[],
        # For UEFI shell based apps
        "AcceptableDependencies-UEFI_APPLICATION":[],
        "IgnoreInf": [],
        "skip": True
    },

    ## options defined .pytool/Plugin/DscCompleteCheck
    "DscCompleteCheck": {
        "IgnoreInf": [""],
        "DscPath": ""  # Don't support this test
    },

    ## options defined .pytool/Plugin/HostUnitTestDscCompleteCheck
    "HostUnitTestDscCompleteCheck": {
        "IgnoreInf": [""],
        "DscPath": "" # Don't support this test
    },

    ## options defined .pytool/Plugin/GuidCheck
    "GuidCheck": {
        "IgnoreGuidName": [],
        "IgnoreGuidValue": [],
        "IgnoreFoldersAndFiles": [],
        "IgnoreDuplicates": [],
    },

    ## options defined .pytool/Plugin/LibraryClassCheck
    "LibraryClassCheck": {
        "IgnoreHeaderFile": [],
        "skip": True
    },

    ## options defined .pytool/Plugin/SpellCheck
    "SpellCheck": {
        "AuditOnly": True,           # Fails right now with over 270 errors
        "IgnoreFiles": [],           # use gitignore syntax to ignore errors in matching files
        "ExtendWords": [],           # words to extend to the dictionary for this package
        "IgnoreStandardPaths": [],   # Standard Plugin defined paths that should be ignore
        "AdditionalIncludePaths": [] # Additional paths to spell check (wildcards supported)
    },

    "Defines": {
        "BLD_*_UNIVERSAL_PAYLOAD": "TRUE",
        "BLD_*_EMU_VARIABLE_ENABLE": "FALSE",
        "BLD_*_DISABLE_RESET_SYSTEM": "TRUE",
        "BLD_*_SERIAL_DRIVER_ENABLE": "FALSE",
        "BLD_*_BUILD_ARCH": "",
        "BLD_*_SECURE_BOOT_ENABLE": "TRUE",
    }
}

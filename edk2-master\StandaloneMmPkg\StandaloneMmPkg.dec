## @file
# This package is a platform package that provide platform module/library
# required by Standalone MM platform.
#
# Copyright (c) 2016-2021, Arm Ltd. All rights reserved.<BR>
# Copyright (c) 2024, Intel Corporation. All rights reserved.<BR>
#
# SPDX-License-Identifier: BSD-2-Clause-Patent
#
#

[Defines]
  DEC_SPECIFICATION              = 0x0001001A
  PACKAGE_NAME                   = StandaloneMmPkg
  PACKAGE_GUID                   = 2AE82968-7769-4A85-A5BC-A0954CE54A5C
  PACKAGE_VERSION                = 1.0

[Includes]
  Include

[LibraryClasses]

  ##  @libraryclass  Defines a set of helper methods.
  FvLib|Include/Library/FvLib.h

  ##  @libraryclass  Defines a set of interfaces that provides services for
  ##                 MM Memory Operation.
  MemLib|Include/Library/StandaloneMmMemLib.h

[LibraryClasses.X64.PEIM]
  MmPlatformHobProducerLib|Include/Library/MmPlatformHobProducerLib.h

[Guids]
  gStandaloneMmPkgTokenSpaceGuid           = { 0x18fe7632, 0xf5c8, 0x4e63, { 0x8d, 0xe8, 0x17, 0xa5, 0x5c, 0x59, 0x13, 0xbd }}
  gMpInformationHobGuid                    = { 0xba33f15d, 0x4000, 0x45c1, { 0x8e, 0x88, 0xf9, 0x16, 0x92, 0xd4, 0x57, 0xe3 }}
  gMmFvDispatchGuid                        = { 0xb65694cc, 0x09e3, 0x4c3b, { 0xb5, 0xcd, 0x05, 0xf4, 0x4d, 0x3c, 0xdb, 0xff }}

  ## Include/Guid/MmramMemoryReserve.h
  gEfiMmPeiMmramMemoryReserveGuid          = { 0x0703f912, 0xbf8d, 0x4e2a, { 0xbe, 0x07, 0xab, 0x27, 0x25, 0x25, 0xc5, 0x92 }}

  gEfiStandaloneMmNonSecureBufferGuid      = { 0xf00497e3, 0xbfa2, 0x41a1, { 0x9d, 0x29, 0x54, 0xc2, 0xe9, 0x37, 0x21, 0xc5 }}
  gExtraGuidedSectionHandlerInfoGuid       = { 0xa558d266, 0x7bfc, 0x11ef, { 0xb4, 0xf0, 0x5f, 0x7b, 0x86, 0xa7, 0xfb, 0x80 }}

  gEventMmDispatchGuid                     = { 0x7e6efffa, 0x69b4, 0x4c1b, { 0xa4, 0xc7, 0xaf, 0xf9, 0xc9, 0x24, 0x4f, 0xee }}

  # Include/Guid/MmStatusCodeUseSerial.h
  gMmStatusCodeUseSerialHobGuid            = { 0xbb55aa97, 0xc7f2, 0x4f60, { 0xac, 0xc3, 0x16, 0xf6, 0xe8, 0xb5, 0x07, 0x79 }}

[Protocols]
  gEfiMmEntryNotifyProtocolGuid            = { 0x9bfbcc5f, 0x6435, 0x46d3, { 0xb9, 0xa5, 0x70, 0xfb, 0xa4, 0x71, 0xe1, 0x26 }}
  gEfiMmExitNotifyProtocolGuid             = { 0x0e265f48, 0x47c0, 0x471f, { 0x9c, 0x83, 0xc6, 0xd6, 0x57, 0x64, 0x21, 0xa4 }}

[Ppis]
  gMmCoreFvLocationPpiGuid                 = { 0x47a00618, 0x237a, 0x4386, { 0x8f, 0xc5, 0x2a, 0x86, 0xd8, 0xac, 0x41, 0x05 }}

[PcdsFixedAtBuild, PcdsPatchableInModule]
  ## Maximum permitted encapsulation levels of sections in a firmware volume,
  #  in the MM phase. Minimum value is 1. Sections nested more deeply are rejected.
  # @Prompt Maximum permitted FwVol section nesting depth (exclusive) in MM.
  gStandaloneMmPkgTokenSpaceGuid.PcdFwVolMmMaxEncapsulationDepth|0x10|UINT32|0x00000001

  ## Option to make shadow copy of boot firmware volume while loading drivers.
  #  This options is useful when the BFV is not located in the Flash area
  #  but is in the RAM instead and therefore no shadow copy is needed.
  #   TRUE  - Default. Make a shadow copy of the boot firmware volume.
  #   FALSE - Disable shadow copy of the boot firmware volume.
  gStandaloneMmPkgTokenSpaceGuid.PcdShadowBfv|TRUE|BOOLEAN|0x00000003

[PcdsFeatureFlag]
  ## Indicates if restart MM Dispatcher once MM Entry Point is registered.<BR><BR>
  #   TRUE  - Restart MM Dispatcher once MM Entry Point is registered.<BR>
  #   FALSE - Do not restart MM Dispatcher once MM Entry Point is registered.<BR>
  # @Prompt Restart MM Dispatcher once MM Entry Point is registered.
  gStandaloneMmPkgTokenSpaceGuid.PcdRestartMmDispatcherOnceMmEntryRegistered|FALSE|BOOLEAN|0x00000002

[PcdsFeatureFlag.X64]
  ## Indicates if restart MM Dispatcher once MM Entry Point is registered.<BR><BR>
  #   TRUE  - Restart MM Dispatcher once MM Entry Point is registered.<BR>
  #   FALSE - Do not restart MM Dispatcher once MM Entry Point is registered.<BR>
  # @Prompt Restart MM Dispatcher once MM Entry Point is registered.
  gStandaloneMmPkgTokenSpaceGuid.PcdRestartMmDispatcherOnceMmEntryRegistered|TRUE|BOOLEAN|0x00000002

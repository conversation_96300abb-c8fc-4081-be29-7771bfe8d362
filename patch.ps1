Write-Host "Patching BOOT.EFI at offset 0xa9b..."
$bytes = [byte[]](0xB0, 0x01)
Write-Host "Patch bytes: B0 01 (mov al, 1)"
$fs = [System.IO.File]::OpenWrite('BOOT.EFI')
$position = $fs.Seek(0xa9b, [System.IO.SeekOrigin]::Begin)
Write-Host "Seeking to position: $position"
$fs.Write($bytes, 0, 2)
$fs.Close()
Write-Host "BOOT.EFI patched successfully!"

# Verify the patch
$fs = [System.IO.File]::OpenRead('BOOT.EFI')
$fs.Seek(0xa9b, [System.IO.SeekOrigin]::Begin)
$readBytes = New-Object byte[] 2
$fs.Read($readBytes, 0, 2)
$fs.Close()
Write-Host "Verification - bytes at 0xa9b: $([System.BitConverter]::ToString($readBytes))"

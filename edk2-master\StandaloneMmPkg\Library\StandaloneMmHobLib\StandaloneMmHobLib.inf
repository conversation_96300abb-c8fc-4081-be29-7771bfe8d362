## @file
# Instance of HOB Library for Standalone MM modules.
#
# Copyright (c) 2007 - 2014, Intel Corporation. All rights reserved.<BR>
# Copyright (c) 2016 - 2018, ARM Limited. All rights reserved.<BR>
# Copyright (c) 2018, Linaro, Ltd. All rights reserved.<BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#
##

[Defines]
  INF_VERSION                    = 0x0001001B
  BASE_NAME                      = HobLib
  FILE_GUID                      = 8262551B-AB2D-4E76-99FC-5EBB83F4988E
  MODULE_TYPE                    = MM_STANDALONE
  VERSION_STRING                 = 1.0
  PI_SPECIFICATION_VERSION       = 0x00010032
  LIBRARY_CLASS                  = HobLib|MM_STANDALONE
  CONSTRUCTOR                    = HobLibConstructor

#
#  VALID_ARCHITECTURES           = IA32 X64 ARM AARCH64
#

[Sources]
  StandaloneMmHobLib.c

[Packages]
  MdePkg/MdePkg.dec

[LibraryClasses]
  BaseMemoryLib
  DebugLib
  MmServicesTableLib

[Guids]
  gEfiHobListGuid                               ## CONSUMES  ## SystemTable
  gEfiHobMemoryAllocModuleGuid                  ## CONSUMES

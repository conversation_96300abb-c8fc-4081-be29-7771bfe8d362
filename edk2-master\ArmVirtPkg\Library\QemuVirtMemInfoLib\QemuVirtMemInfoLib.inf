#/* @file
#
#  Copyright (c) 2011-2015, ARM Limited. All rights reserved.
#  Copyright (c) 2014-2017, Linaro Limited. All rights reserved.
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#*/

[Defines]
  INF_VERSION                    = 0x0001001A
  BASE_NAME                      = QemuVirtMemInfoLib
  FILE_GUID                      = 9b30ca82-6746-4a82-a3e6-11ea79df3b46
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = ArmVirtMemInfoLib
  CONSTRUCTOR                    = QemuVirtMemInfoLibConstructor

[Sources]
  QemuVirtMemInfoLib.c

[Packages]
  ArmPkg/ArmPkg.dec
  ArmVirtPkg/ArmVirtPkg.dec
  EmbeddedPkg/EmbeddedPkg.dec
  MdeModulePkg/MdeModulePkg.dec
  MdePkg/MdePkg.dec

[LibraryClasses]
  ArmLib
  BaseMemoryLib
  DebugLib
  MemoryAllocationLib

[Guids]
  gArmVirtSystemMemorySizeGuid

[Pcd]
  gArmTokenSpaceGuid.PcdFvBaseAddress
  gArmTokenSpaceGuid.PcdSystemMemoryBase
  gArmTokenSpaceGuid.PcdSystemMemorySize

[FixedPcd]
  gArmTokenSpaceGuid.PcdFvSize

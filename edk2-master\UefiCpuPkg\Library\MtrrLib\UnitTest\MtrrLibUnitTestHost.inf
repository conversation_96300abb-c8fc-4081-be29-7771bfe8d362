## @file
# Unit tests of the MtrrLib instance of the MtrrLib class
#
# Copyright (c) 2020, Intel Corporation. All rights reserved.<BR>
# SPDX-License-Identifier: BSD-2-Clause-Patent
##

[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = MtrrLibUnitTestHost
  FILE_GUID                      = A1542D84-B64D-4847-885E-0509084376AB
  MODULE_TYPE                    = HOST_APPLICATION
  VERSION_STRING                 = 1.0

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources]
  MtrrLibUnitTest.c
  MtrrLibUnitTest.h
  Support.c
  RandomNumber.c

[Packages]
  MdePkg/MdePkg.dec
  UefiCpuPkg/UefiCpuPkg.dec
  UnitTestFrameworkPkg/UnitTestFrameworkPkg.dec

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  DebugLib
  MtrrLib
  UnitTestLib

[Pcd]
  gUefiCpuPkgTokenSpaceGuid.PcdCpuNumberOfReservedVariableMtrrs   ## SOMETIMES_CONSUMES

[BuildOptions]
  MSFT:*_*_*_CC_FLAGS = -D _CRT_SECURE_NO_WARNINGS

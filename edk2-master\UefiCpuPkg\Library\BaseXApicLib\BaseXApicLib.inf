## @file
#  The Local Apic library supports xAPIC mode only.
#
#  Note: Local APIC library assumes local APIC is enabled. It does not handle cases
#  where local APIC is disabled.
#
#  Copyright (c) 2010 - 2018, Intel Corporation. All rights reserved.<BR>
#  Copyright (c) 2020, AMD Inc. All rights reserved.<BR>
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = BaseXApicLib
  MODULE_UNI_FILE                = BaseXApicLib.uni
  FILE_GUID                      = D87CA0A8-1AC2-439b-90F8-EF4A2AC88DAF
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.1
  LIBRARY_CLASS                  = LocalApicLib

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources]
  BaseXApicLib.c

[Packages]
  MdePkg/MdePkg.dec
  UefiCpuPkg/UefiCpuPkg.dec

[LibraryClasses]
  BaseLib
  DebugLib
  TimerLib
  IoLib
  PcdLib
  CpuLib

[Pcd]
  gUefiCpuPkgTokenSpaceGuid.PcdCpuInitIpiDelayInMicroSeconds  ## SOMETIMES_CONSUMES
  gUefiCpuPkgTokenSpaceGuid.PcdCpuLocalApicBaseAddress        ## SOMETIMES_CONSUMES

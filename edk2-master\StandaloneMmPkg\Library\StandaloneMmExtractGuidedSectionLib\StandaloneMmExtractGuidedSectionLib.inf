## @file
# ExtractGuidedSectionLib fot StandaloneMm Driver.
#
#  Copyright (c) 2008 - 2009, Apple Inc. All rights reserved.<BR>
#  Copyright (c) 2024, Arm Ltd. All rights reserved.<BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
##

[Defines]
  INF_VERSION                    = 0x0001001B
  BASE_NAME                      = StandaloneMmExtractGuidedSectionLib
  FILE_GUID                      = 3b2564c2-7bf7-11ef-a7c5-6b16a362823f
  MODULE_TYPE                    = MM_STANDALONE
  VERSION_STRING                 = 1.0
  PI_SPECIFICATION_VERSION       = 0x00010032
  LIBRARY_CLASS                  = ExtractGuidedSectionLib|MM_STANDALONE MM_CORE_STANDALONE
  CONSTRUCTOR                    = StandaloneMmExtractGuidedSectionLibConstructor

[Sources.common]
  StandaloneMmExtractGuidedSectionLib.c

[Packages]
  MdePkg/MdePkg.dec
  StandaloneMmPkg/StandaloneMmPkg.dec

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  DebugLib
  MemoryAllocationLib

[Guids]
  gExtraGuidedSectionHandlerInfoGuid

[FixedPcd.common]
  gEfiMdePkgTokenSpaceGuid.PcdMaximumGuidedExtractHandler

## @file
# SystemFirmware FMP update driver.
#
# Produce FMP instance to update system firmware.
#
#  Copyright (c) 2016, Intel Corporation. All rights reserved.<BR>
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = SystemFirmwareUpdateDxe
  MODULE_UNI_FILE                = SystemFirmwareUpdateDxe.uni
  FILE_GUID                      = 0A2FBD15-1C25-407E-8915-60C5652BC2AA
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  ENTRY_POINT                    = SystemFirmwareUpdateMainDxe

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = X64
#

[Sources]
  SystemFirmwareDxe.h
  SystemFirmwareCommonDxe.c
  SystemFirmwareUpdateDxe.c
  ParseConfigProfile.c

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  SignedCapsulePkg/SignedCapsulePkg.dec

[LibraryClasses]
  BaseLib
  UefiLib
  BaseMemoryLib
  DebugLib
  PcdLib
  MemoryAllocationLib
  UefiBootServicesTableLib
  HobLib
  UefiRuntimeServicesTableLib
  UefiDriverEntryPoint
  EdkiiSystemCapsuleLib
  PlatformFlashAccessLib
  IniParsingLib
  PrintLib

[Pcd]
  gEfiMdeModulePkgTokenSpaceGuid.PcdSystemFmpCapsuleImageTypeIdGuid           ## CONSUMES
  gEfiSignedCapsulePkgTokenSpaceGuid.PcdEdkiiSystemFirmwareFileGuid           ## CONSUMES
  gEfiSignedCapsulePkgTokenSpaceGuid.PcdEdkiiSystemFirmwareImageDescriptor    ## CONSUMES

[Protocols]
  gEfiFirmwareManagementProtocolGuid     ## PRODUCES

[Depex]
  gEfiVariableArchProtocolGuid

[UserExtensions.TianoCore."ExtraFiles"]
  SystemFirmwareUpdateDxeExtra.uni


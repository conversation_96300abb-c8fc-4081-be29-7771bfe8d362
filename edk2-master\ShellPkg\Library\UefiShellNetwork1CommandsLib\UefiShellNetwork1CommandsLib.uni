// /**
//
// (C) Copyright 2013-2015 Hewlett-Packard Development Company, L.P.<BR>
// Copyright (c) 2010 - 2018, Intel Corporation. All rights reserved. <BR>
// (C) Copyright 2017 Hewlett Packard Enterprise Development LP<BR>
// SPDX-License-Identifier: BSD-2-Clause-Patent
//
// Module Name:
//
// UefiShellNetwork1CommandsLib.uni
//
// Abstract:
//
// String definitions for UEFI Shell 2.0 network 1 commands
//
//
// **/

/=#

#langdef   en-US "english"

#string STR_GEN_TOO_MANY          #language en-US "%H%s%N: Too many arguments.\r\n"
#string STR_GEN_TOO_FEW           #language en-US "%H%s%N: Too few arguments.\r\n"
#string STR_GEN_PARAM_INV         #language en-US "%H%s%N: Invalid argument - '%H%s%N'\r\n"
#string STR_GEN_PROBLEM           #language en-US "%H%s%N: Unknown flag - '%H%s%N'\r\n"
#string STR_GEN_PROBLEM_OP2       #language en-US "%H%s%N: Invalid argument - '%H%s%N'. Expected '%B%s%N' or '%B%s%N'.\r\n"
#string STR_GEN_PROBLEM_VAL       #language en-US "%H%s%N: Bad value - '%H%s%N' for flag - '%H%s%N'\r\n"
#string STR_GEN_NO_VALUE          #language en-US "%H%s%N: Missing argument for flag - '%H%s%N'\r\n"
#string STR_GEN_ERR_AD            #language en-US "%H%s%N: Access denied.\r\n"
#string STR_GEN_ERR_UK            #language en-US "%H%s%N: Status: %r\r\n"
#string STR_GEN_PARAM_CON         #language en-US "%H%s%N: Parameters conflict.\r\n"
#string STR_GEN_FILE_OPEN_FAIL    #language en-US "%H%s%N: Cannot open file - '%H%s%N'\r\n"
#string STR_GEN_FILE_AD           #language en-US "%H%s%N: Access file error - '%H%s%N'\r\n"
#string STR_GEN_CRLF              #language en-US "\r\n"
#string STR_GEN_NO_FILES          #language en-US "%H%s%N: No matching files were found.\r\n"
#string STR_GEN_DIR_NF            #language en-US "%H%s%N: Directory not found - '%H%s%N'\r\n"
#string STR_GEN_FILE_NF           #language en-US "%H%s%N: File not found - '%H%s%N'\r\n"
#string STR_GEN_IS_DIR            #language en-US "%H%s%N: '%H%s%N' is a directory\r\n"
#string STR_GEN_PROTOCOL_NF       #language en-US "%H%s%N: The protocol '%H%s%N' is required and not found (%g).\r\n"
#string STR_GEN_OUT_MEM           #language en-US "%H%s%N: Memory allocation was not successful.\r\n"

#string STR_PING_INVALID_SOURCE      #language en-US "%Ping: Require source interface option\r\n"
#string STR_PING_CONFIG              #language en-US "Config %r\r\n"
#string STR_PING_GETMODE             #language en-US "GetModeData %r\r\n"
#string STR_PING_GETDATA             #language en-US "GetData %r\r\n"
#string STR_PING_RECEIVE             #language en-US "Receive %r\r\n"
#string STR_PING_SEND_REQUEST        #language en-US "Echo request sequence %d did not complete successfully.\r\n"
#string STR_PING_NOSOURCE_INDO       #language en-US "There are no sources in %s's multicast domain.\r\n"
#string STR_PING_NETWORK_ERROR       #language en-US "%H%s%N: Network function failed with %r\r\n"
#string STR_PING_CONFIGD_NIC_NF      #language en-US "%H%s%N: No configured interfaces were found.\r\n"
#string STR_PING_NOROUTE_FOUND       #language en-US "There is no route to the destination '%B%s%N' from the source '%B%s%N' was found.\r\n"
#string STR_PING_START               #language en-US "Ping %s %d data bytes.\r\n"
#string STR_PING_TIMEOUT             #language en-US "Echo request sequence %d timeout.\r\n"
#string STR_PING_REPLY_INFO          #language en-US "%d bytes from %s : icmp_seq=%d ttl=%d time=%d~%dms\r\n"
#string STR_PING_STAT                #language en-US "\n%d packets transmitted, %d received, %d%% packet loss, time %dms\r\n"
#string STR_PING_RTT                 #language en-US "\nRtt(round trip time) min=%d~%dms max=%d~%dms avg=%d~%dms\r\n"

#string STR_IFCONFIG_UNSUPPORTED_OPTION       #language en-US    "The option '%H%s%N' is unsupported now.\n"
#string STR_IFCONFIG_LACK_OPTION              #language en-US    "Flags lack. Please type 'ifConfig -?' for help info.\n"
#string STR_IFCONFIG_LACK_INTERFACE           #language en-US    "Lack interface name.\n"
#string STR_IFCONFIG_LACK_COMMAND             #language en-US    "Lack interface config option.\n"
#string STR_IFCONFIG_INVALID_INTERFACE        #language en-US    "Invalid interface name.\n"
#string STR_IFCONFIG_INVALID_IPADDRESS        #language en-US    "Invalid ipv4 address: '%H%s%N'\n"
#string STR_IFCONFIG_INVALID_GATEWAY          #language en-US    "Invalid gateway address: '%H%s%N'\n"
#string STR_IFCONFIG_DUPLICATE_COMMAND        #language en-US    "Duplicate commands. Bad command %H%s%N is skipped.\n"
#string STR_IFCONFIG_CONFLICT_COMMAND         #language en-US    "Conflict commands. Bad command %H%s%N is skipped.\n"
#string STR_IFCONFIG_UNKNOWN_COMMAND          #language en-US    "Unknown commands. Bad command %H%s%N is skipped.\n"
#string STR_IFCONFIG_SET_ADDR_FAILED          #language en-US    "Failed to set address.\n"
#string STR_IFCONFIG_ROUTES_SIZE              #language en-US     "\n%H  Routes (%d entries):\n"
#string STR_IFCONFIG_ROUTES_ENTRY_INDEX       #language en-US    "%H    Entry[%d]\n"
#string STR_IFCONFIG_SHOW_IP_ADDR             #language en-US    "%12s: %N%d.%d.%d.%d\n"
#string STR_IFCONFIG_INFO_NEWLINE             #language en-US    "\n"
#string STR_IFCONFIG_INFO_DNS_ADDR_BODY       #language en-US    "%8d.%d.%d.%d\n"
#string STR_IFCONFIG_INFO_BREAK               #language en-US    "\n-----------------------------------------------------------------\n"
#string STR_IFCONFIG_INFO_COLON               #language en-US    ":"
#string STR_IFCONFIG_INFO_IF_NAME             #language en-US    "\n%Hname         : %s%N\n"
#string STR_IFCONFIG_INFO_MEDIA_STATE         #language en-US    "%HMedia State  : %s%N\n"
#string STR_IFCONFIG_INFO_POLICY_DHCP         #language en-US    "%Hpolicy       : dhcp%N\n"
#string STR_IFCONFIG_INFO_POLICY_MAN          #language en-US    "%Hpolicy       : static%N\n"
#string STR_IFCONFIG_INFO_MAC_ADDR_HEAD       #language en-US    "%Hmac addr     : %N"
#string STR_IFCONFIG_INFO_MAC_ADDR_BODY       #language en-US    "%02x"
#string STR_IFCONFIG_INFO_IP_ADDR_HEAD        #language en-US    "\n%Hipv4 address : %N"
#string STR_IFCONFIG_INFO_SUBNET_MASK_HEAD    #language en-US    "\n%Hsubnet mask  : %N"
#string STR_IFCONFIG_INFO_GATEWAY_HEAD        #language en-US    "\n%Hdefault gateway: %N"
#string STR_IFCONFIG_INFO_DNS_ADDR_HEAD       #language en-US    "\n%HDNS server   : %N\n"
#string STR_IFCONFIG_INFO_IP_ADDR_BODY        #language en-US    "%d.%d.%d.%d\n"

#string STR_GET_HELP_PING         #language en-US ""
".TH ping 0 "Ping the target host with an IPv4 stack."\r\n"
".SH NAME\r\n"
"Ping the target host with an IPv4 stack.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"PING [-n count] [-l size] [-s SourceIp] TargetIp\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  -n       - Specifies the number of echo request datagrams to be sent.\r\n"
"  -l       - Specifies the size of the data buffer in the echo request datagram.\r\n"
"  -s       - Specifies the source adapter as IPv4 address.\r\n"
"  SourceIp - Specifies the IPv4 address of the source machine.\r\n"
"  TargetIp - Specifies the IPv4 address of the target machine.\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. This command uses the ICMPv4 ECHO_REQUEST datagram to elicit an\r\n"
"     ECHO_REPLY from a host.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To ping the target host with 64 bytes data:\r\n"
"    fs0:\> ping -l 64 ***********\r\n"
" \r\n"
"  * To ping the target host by sending 20 echo request datagrams:\r\n"
"    fs0:\> ping -n 20 *************\r\n"
" \r\n"
"  * To ping the target host by specifying the source adapter as IPv4 address:\r\n"
"    fs0:\> ping -s ************** *************\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  SHELL_SUCCESS             The action was completed as requested.\r\n"
"  SHELL_INVALID_PARAMETER   One of the passed-in parameters was incorrectly\r\n"
"                            formatted or its value was out of bounds.\r\n"

#string STR_GET_HELP_IFCONFIG                 #language en-US    ""
".TH ifconfig 0 "Modifies the default IP address of the UEFI IPv4 Network Stack."\r\n"
".SH NAME\r\n"
"Modifies the default IP address of the UEFI IPv4 Network Stack.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"IFCONFIG [-r [Name]] [-l [Name]]\r\n"
"IFCONFIG [-s <Name> dhcp | <static <IP> <Mask>  <Gateway>> | <dns <IP>>]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  -r          - Renew configuration of interface and set dhcp policy.\r\n"
"  -l          - Lists the configuration.\r\n"
"  -s          - Sets the configuration.\r\n"
"  Name        - Specifies an adapter name (for example, eth0).\r\n"
"  IP          - Specifies the IPv4 address in four integer values:\r\n"
"                  - Example: ***********0\r\n"
"  SubnetMask  - Specifies a subnet mask in four integer values:\r\n"
"                  - Example: *************\r\n"
"  GatewayMask - Specifies a default gateway in four integer values:\r\n"
"                  - Example: ***********\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. This command modifies the default IP address for the UEFI IPv4\r\n"
"     network stack.\r\n"
"  2. Use '-r' to renew configuration of interface and set dhcp policy.\r\n"
"  3. Use '-l' to list the DNS and other address related settings for all\r\n"
"     interfaces or the specified interface.\r\n"
"  4. Use '-s <Name> static <IP> <SubnetMask> <GatewayMask>' with \r\n"
"     static IPv4 address configuration for specified interface.\r\n"
"  5. Use '-s <Name> dhcp' for DHCPv4 to request the IPv4 address\r\n"
"     configuration dynamically for specified interface.\r\n"
"  6. Use '-s <Name> dns <IP>' must under manual policy.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To list the configuration for the eth0 interface:\r\n"
"    fs0:\> ifconfig -l eth0\r\n"
" \r\n"
"  * To use DHCPv4 to request the IPv4 address configuration dynamically for the\r\n"
"    eth0 interface:\r\n"
"    fs0:\> ifconfig -s eth0 dhcp\r\n"
" \r\n"
"  * To use the static IPv4 address configuration for the eth0 interface:\r\n"
"    fs0:\> ifconfig -s eth0 static *********** ************* ***********\r\n"
" \r\n"
"  * To configure DNS server address for the eth0 interface:\r\n"
"    fs0:\> ifconfig -s eth0 dns *********** ***********\r\n"




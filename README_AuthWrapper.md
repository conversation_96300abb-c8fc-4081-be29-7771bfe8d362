# Auth Wrapper - UEFI启动验证系统

## 概述

Auth Wrapper是一个UEFI启动验证系统，为1boot.efi提供硬件绑定的身份验证保护。系统通过扫描USB设备硬件信息生成唯一的硬件指纹，并要求匹配的密钥文件才能继续启动过程。

## 核心特性

- **硬件绑定验证**: 基于USB设备硬件信息生成唯一指纹
- **双重验证机制**: 文件存在性检查 + 内容匹配验证
- **安全加密算法**: 使用SHA256哈希和自定义Base32编码
- **透明启动过程**: 验证成功后无缝加载原始boot.efi
- **用户友好界面**: 中文提示信息和详细错误报告

## 系统架构

```
启动流程:
UEFI固件 → BOOT.EFI (Auth Wrapper) → 验证过程 → 1BOOT.EFI (原始启动程序)

验证过程:
1. 扫描USB设备 → 2. 生成硬件指纹 → 3. 计算密钥文件名 → 4. 检查文件存在
5. 读取文件内容 → 6. 生成验证码 → 7. 比较验证码 → 8. 决定是否放行
```

## 文件结构

```
项目根目录/
├── edk2-master/                    # EDK2开发框架
│   └── AuthWrapperPkg/            # Auth Wrapper包
│       ├── AuthWrapperPkg.dec     # 包声明文件
│       ├── AuthWrapperPkg.dsc     # 包构建描述
│       └── Application/
│           └── AuthWrapper/       # 主应用程序
│               ├── AuthWrapper.inf    # 模块描述文件
│               ├── AuthWrapper.h      # 头文件
│               ├── AuthWrapper.c      # 主程序
│               ├── UsbHardware.c      # USB硬件扫描
│               ├── CryptoUtils.c      # 加密工具
│               ├── FileSystem.c       # 文件系统访问
│               └── KeyVerification.c # 密钥验证和启动
├── auth_key_generator.py          # 密钥生成工具
├── build_auth_wrapper.bat         # 构建脚本
├── deploy_auth_wrapper.bat        # 部署脚本
└── README_AuthWrapper.md          # 本文档
```

## 安装和使用

### 1. 环境准备

**必需软件:**
- EDK2开发框架 (已包含在edk2-master目录)
- Visual Studio 2019/2022 或 GCC编译器
- Python 3.x (用于密钥生成工具)

**系统要求:**
- Windows 10/11 (开发环境)
- 支持UEFI启动的目标系统
- 至少一个USB设备用于硬件指纹生成

### 2. 构建系统

```batch
# 运行构建脚本
build_auth_wrapper.bat
```

构建脚本将:
- 设置EDK2开发环境
- 配置编译工具链
- 编译Auth Wrapper应用程序
- 生成AuthWrapper.efi文件

### 3. 部署系统

```batch
# 准备文件
1. 将原始boot.efi重命名为1BOOT.EFI
2. 运行部署脚本
deploy_auth_wrapper.bat
```

部署脚本将:
- 备份原始BOOT.EFI文件
- 部署AuthWrapper.efi为新的BOOT.EFI
- 可选择运行密钥生成工具

### 4. 生成密钥文件

```batch
# 运行密钥生成工具
python auth_key_generator.py [输出目录]
```

密钥生成工具将:
- 扫描当前系统的USB设备
- 生成硬件指纹
- 创建对应的.vtkey密钥文件
- 将文件保存到指定目录(默认C:\)

## 技术细节

### 硬件指纹算法

1. **USB设备扫描**: 枚举所有USB设备，获取VendorID、ProductID和SerialNumber
2. **设备排序**: 按VendorID:ProductID排序确保一致性
3. **字符串组合**: 将设备信息组合为"VID:PID:SerialNumber"格式
4. **SHA256哈希**: 对组合字符串进行SHA256哈希生成32字节指纹

### 密钥生成算法

**文件名生成 (16字符):**
```
硬件指纹 → SHA256哈希 → 取前8字节 → Base32编码 → 16字符文件名
```

**验证码生成 (32字符):**
```
硬件指纹 + 盐值 → SHA256哈希 → 取前16字节 → Base32编码 → 32字符验证码
```

**Base32字符集:**
```
"0123456789ABCDEFGHJKLMNPQRSTUVWXYZ" (排除I和O避免混淆)
```

### 验证流程

1. **USB设备枚举**: 使用EFI_USB_IO_PROTOCOL扫描USB设备
2. **硬件指纹生成**: 基于设备信息生成32字节指纹
3. **密钥文件名计算**: 从指纹生成16字符文件名
4. **文件存在性检查**: 在C盘查找{文件名}.vtkey
5. **文件内容读取**: 读取密钥文件内容
6. **验证码生成**: 基于当前硬件指纹生成期望验证码
7. **内容比较**: 比较文件内容与期望验证码
8. **启动决策**: 匹配则加载1BOOT.EFI，否则拒绝启动

## 安全特性

- **硬件绑定**: 密钥与特定USB设备组合绑定
- **双重验证**: 文件名和内容都必须匹配
- **盐值保护**: 验证码使用独立盐值防止逆向
- **内存安全**: 敏感数据使用后立即清零
- **防篡改**: 修改密钥文件内容将导致验证失败

## 故障排除

### 常见问题

**1. 构建失败**
- 检查Visual Studio环境是否正确安装
- 确认EDK2环境设置成功
- 检查源代码语法错误

**2. 验证失败**
- 确认密钥文件存在于C盘根目录
- 检查USB设备是否发生变化
- 重新生成密钥文件

**3. 启动失败**
- 检查1BOOT.EFI文件是否存在
- 确认文件路径正确
- 查看调试输出信息

### 调试信息

Auth Wrapper提供详细的调试输出:
- USB设备扫描结果
- 硬件指纹生成过程
- 密钥文件检查状态
- 验证码比较结果

### 恢复方法

如果系统无法启动:
1. 将BOOT.EFI.backup重命名为BOOT.EFI
2. 或者直接使用原始boot.efi文件
3. 重新配置验证系统

## 开发信息

**版本**: 1.0  
**许可证**: BSD-2-Clause-Patent  
**开发语言**: C (UEFI应用), Python (工具)  
**编译器**: Visual Studio 2019/2022, GCC  
**框架**: EDK2 UEFI开发框架  

## 注意事项

1. **备份重要**: 部署前务必备份原始启动文件
2. **硬件依赖**: USB设备变化会导致验证失败
3. **密钥管理**: 妥善保管生成的密钥文件
4. **测试环境**: 建议先在虚拟机中测试
5. **兼容性**: 确保目标系统支持UEFI启动

## 技术支持

如遇到问题，请检查:
1. 系统日志和调试输出
2. USB设备连接状态
3. 密钥文件完整性
4. UEFI固件兼容性

---

**重要提醒**: 此系统会影响系统启动过程，请在充分测试后再部署到生产环境。

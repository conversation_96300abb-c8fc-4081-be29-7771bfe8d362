## @file
# Sample UnitTest built for execution in SMM.
# This test case generates an exception. For some host-based environments, this
# is a fatal condition that terminates the unit tests and no additional test
# cases are executed. On other environments, this condition may be report a unit
# test failure and continue with additional unit tests.
#
# Copyright (c) 2024, Intel Corporation. All rights reserved.<BR>
# SPDX-License-Identifier: BSD-2-Clause-Patent
##

[Defines]
  INF_VERSION              = 0x00010006
  BASE_NAME                = SampleUnitTestSmmGenerateException
  FILE_GUID                = C28BCCD6-3B42-4896-9931-62CCC5DF91B8
  MODULE_TYPE              = DXE_SMM_DRIVER
  VERSION_STRING           = 1.0
  PI_SPECIFICATION_VERSION = 0x0001000A
  ENTRY_POINT              = DxeEntryPoint

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources]
  SampleUnitTestGenerateException.c

[Packages]
  MdePkg/MdePkg.dec

[LibraryClasses]
  UefiDriverEntryPoint
  BaseLib
  DebugLib
  UnitTestLib
  PrintLib

[Pcd]
  gEfiMdePkgTokenSpaceGuid.PcdDebugPropertyMask

[Depex]
  gEfiSmmCpuProtocolGuid

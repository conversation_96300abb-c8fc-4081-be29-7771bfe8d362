;------------------------------------------------------------------------------
; @file
; This file includes all other code files to assemble the reset vector code
;
; Copyright (c) 2008 - 2023, Intel Corporation. All rights reserved.<BR>
; SPDX-License-Identifier: BSD-2-Clause-Patent
;
;------------------------------------------------------------------------------

;
; If neither ARCH_IA32 nor ARCH_X64 are defined, then try to include
; Base.h to use the C pre-processor to determine the architecture.
;
%ifndef ARCH_IA32
  %ifndef ARCH_X64
    #include <Base.h>
    #if defined (MDE_CPU_IA32)
      %define ARCH_IA32
    #elif defined (MDE_CPU_X64)
      %define ARCH_X64
    #endif
  %endif
%endif

%ifdef ARCH_IA32
  %ifdef ARCH_X64
    %error "Only one of ARCH_IA32 or ARCH_X64 can be defined."
  %endif
%elifdef ARCH_X64
%else
  %error "Either ARCH_IA32 or ARCH_X64 must be defined."
%endif

%include "CommonMacros.inc"

%include "PostCodes.inc"

%include "PageTables.inc"

%ifdef ARCH_X64
  %include "X64/PageTables.asm"
%endif

%ifdef DEBUG_PORT80
  %include "Port80Debug.asm"
%elifdef DEBUG_SERIAL
  %include "SerialDebug.asm"
%else
  %include "DebugDisabled.asm"
%endif

%include "Ia32/SearchForBfvBase.asm"
%include "Ia32/SearchForSecEntry.asm"

%ifdef ARCH_X64
%include "Ia32/Flat32ToFlat64.asm"
%include "Ia32/PageTables64.asm"
%endif

%include "Ia16/Real16ToFlat32.asm"
%include "Ia16/Init16.asm"

%include "Main.asm"

%include "Ia16/ResetVectorVtf0.asm"


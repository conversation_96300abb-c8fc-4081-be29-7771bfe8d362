## @file
#  Standalone MM CPU specific programming.
#
#  Copyright (c) 2009 - 2023, Intel Corporation. All rights reserved.<BR>
#  Copyright (c) Microsoft Corporation.<BR>
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = StandaloneMmCpuFeaturesLib
  MODULE_UNI_FILE                = SmmCpuFeaturesLib.uni
  FILE_GUID                      = BB554A2D-F5DF-41D3-8C62-46476A2B2B18
  MODULE_TYPE                    = MM_STANDALONE
  VERSION_STRING                 = 1.0
  PI_SPECIFICATION_VERSION       = 0x00010032
  LIBRARY_CLASS                  = SmmCpuFeaturesLib
  CONSTRUCTOR                    = StandaloneMmCpuFeaturesLibConstructor

[Sources]
  CpuFeaturesLib.h
  IntelSmmCpuFeaturesLib.c
  SmmCpuFeaturesLibCommon.c
  StandaloneMmCpuFeaturesLib.c
  SmmCpuFeaturesLibNoStm.c

[Packages]
  MdePkg/MdePkg.dec
  UefiCpuPkg/UefiCpuPkg.dec

[LibraryClasses]
  BaseLib
  DebugLib
  MemoryAllocationLib
  PcdLib
  HobLib

[Guids]
  gSmmBaseHobGuid                ## CONSUMES

[FixedPcd]
  gUefiCpuPkgTokenSpaceGuid.PcdCpuMaxLogicalProcessorNumber        ## SOMETIMES_CONSUMES

[FeaturePcd]
  gUefiCpuPkgTokenSpaceGuid.PcdSmrrEnable  ## CONSUMES
  gUefiCpuPkgTokenSpaceGuid.PcdSmmFeatureControlEnable  ## CONSUMES

## @file
#  This module provides GoogleTest Library implementation.
#
#  Copyright (c) 2022, Intel Corporation. All rights reserved.<BR>
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION     = 0x00010018
  BASE_NAME       = GoogleTestLib
  MODULE_UNI_FILE = GoogleTestLib.uni
  FILE_GUID       = A90E4751-AD30-43CC-980B-01E356B49ADF
  MODULE_TYPE     = HOST_APPLICATION
  VERSION_STRING  = 0.1
  LIBRARY_CLASS   = GoogleTestLib

#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources]
  googletest/googletest/src/gtest-all.cc
  googletest/googlemock/src/gmock-all.cc

[Packages]
  MdePkg/MdePkg.dec
  UnitTestFrameworkPkg/UnitTestFrameworkPkg.dec

[BuildOptions]
  MSFT:*_*_*_CC_FLAGS   == /c /EHs /Zi /Od /MTd
  GCC:*_*_IA32_CC_FLAGS == -g -c -fshort-wchar -fexceptions -O0 -m32 -malign-double -fno-pie
  GCC:*_*_X64_CC_FLAGS  == -g -c -fshort-wchar -fexceptions -O0 -m64 -fno-pie "-DEFIAPI=__attribute__((ms_abi))"

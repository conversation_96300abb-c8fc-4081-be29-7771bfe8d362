## @file
# Sample UnitTest built for execution in SMM.
#
# Copyright (c) Microsoft Corporation.<BR>
# SPDX-License-Identifier: BSD-2-Clause-Patent
##

[Defines]
  INF_VERSION              = 0x00010006
  BASE_NAME                = SampleUnitTestSmm
  FILE_GUID                = 389B16DB-F622-424C-9000-9E43C69CBF71
  MODULE_TYPE              = DXE_SMM_DRIVER
  VERSION_STRING           = 1.0
  PI_SPECIFICATION_VERSION = 0x0001000A
  ENTRY_POINT              = DxeEntryPoint

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources]
  SampleUnitTest.c

[Packages]
  MdePkg/MdePkg.dec

[LibraryClasses]
  UefiDriverEntryPoint
  BaseLib
  DebugLib
  UnitTestLib
  PrintLib

[Pcd]
  gEfiMdePkgTokenSpaceGuid.PcdDebugPropertyMask

[Depex]
  gEfiSmmCpuProtocolGuid

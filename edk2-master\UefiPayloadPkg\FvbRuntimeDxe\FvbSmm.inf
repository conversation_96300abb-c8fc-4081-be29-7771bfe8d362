## @file
# This driver installs the EFI_SMM_FIRMWARE_VOLUMEN_PROTOCOL.
#
#
#  Copyright (c) 2014 - 2021, Intel Corporation. All rights reserved.<BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = FvbSmm
  FILE_GUID                      = A4EC8ADB-B7A8-47d1-8E52-EC820D0ACF6F
  MODULE_TYPE                    = DXE_SMM_DRIVER
  VERSION_STRING                 = 1.0
  PI_SPECIFICATION_VERSION       = 0x0001000A
  ENTRY_POINT                    = FvbSmmInitialize

[Sources]
  FvbInfo.c
  FvbService.h
  FvbService.c
  FvbServiceSmm.c
  FvbSmmCommon.h

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  UefiCpuPkg/UefiCpuPkg.dec
  UefiPayloadPkg/UefiPayloadPkg.dec

[LibraryClasses]
  FlashDeviceLib
  PcdLib
  MemoryAllocationLib
  CacheMaintenanceLib
  IoLib
  BaseMemoryLib
  DebugLib
  BaseLib
  UefiLib
  SmmServicesTableLib
  UefiBootServicesTableLib
  UefiDriverEntryPoint
  HobLib
  DxeServicesLib

[Guids]
  gEfiFirmwareFileSystem2Guid                   # ALWAYS_CONSUMED
  gEfiSystemNvDataFvGuid                        # ALWAYS_CONSUMED
  gEfiAuthenticatedVariableGuid
  gNvVariableInfoGuid

  [Protocols]
  gEfiDevicePathProtocolGuid                    # PROTOCOL ALWAYS_PRODUCED
  gEfiSmmFirmwareVolumeBlockProtocolGuid        # PROTOCOL ALWAYS_PRODUCED

[Pcd]
  gEfiMdeModulePkgTokenSpaceGuid.PcdFlashNvStorageVariableBase
  gEfiMdeModulePkgTokenSpaceGuid.PcdFlashNvStorageVariableSize
  gEfiMdeModulePkgTokenSpaceGuid.PcdFlashNvStorageFtwWorkingSize
  gEfiMdeModulePkgTokenSpaceGuid.PcdFlashNvStorageFtwSpareSize

  gEfiMdeModulePkgTokenSpaceGuid.PcdFlashNvStorageVariableBase64
  gEfiMdeModulePkgTokenSpaceGuid.PcdFlashNvStorageFtwWorkingBase
  gEfiMdeModulePkgTokenSpaceGuid.PcdFlashNvStorageFtwSpareBase
  gUefiPayloadPkgTokenSpaceGuid.PcdNvsDataFile

[Depex]
  TRUE

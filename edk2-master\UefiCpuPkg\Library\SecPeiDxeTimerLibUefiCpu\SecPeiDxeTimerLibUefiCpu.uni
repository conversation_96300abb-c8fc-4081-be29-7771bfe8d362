// /** @file
// Instance of Timer Library only using CPU resources.
//
// Timer Library that only uses CPU resources to provide calibrated delays
// on IA-32, x64, and IPF.
// Note: A driver of type DXE_RUNTIME_DRIVER and DXE_SMM_DRIVER can use this TimerLib
// in their initialization without any issues. They only have to be careful in
// the implementation of runtime services and SMI handlers.
// Because CPU Local APIC and ITC could be programmed by OS, it cannot be
// used by SMM drivers and runtime drivers, ACPI timer is recommended for SMM
// drivers and runtime drivers.
//
// This library differs with the SecPeiDxeTimerLibCpu library in the MdePkg in
// that it uses the local APIC library so that it supports x2APIC mode.
//
// Copyright (c) 2010 - 2018, Intel Corporation. All rights reserved.<BR>
//
// SPDX-License-Identifier: BSD-2-Clause-Patent
//
// **/


#string STR_MODULE_ABSTRACT             #language en-US "Instance of Timer Library only using CPU resources"

#string STR_MODULE_DESCRIPTION          #language en-US "Timer Library that only uses CPU resources to provide calibrated delays on IA-32, x64, and IPF. Note: A driver of type DXE_RUNTIME_DRIVER and DXE_SMM_DRIVER can use this TimerLib in their initialization without any issues. They only have to be careful in the implementation of runtime services and SMI handlers. Because CPU Local APIC and ITC could be programmed by OS, it cannot be used by SMM drivers and runtime drivers, ACPI timer is recommended for SMM drivers and runtime drivers. This library differs with the SecPeiDxeTimerLibCpu library in the MdePkg in that it uses the local APIC library so that it supports x2APIC mode."


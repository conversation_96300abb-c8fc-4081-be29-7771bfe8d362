## @file
#  MP Initialize Library instance for uniprocessor platforms.
#
#  Copyright (c) 2019, Intel Corporation. All rights reserved.<BR>
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = MpInitLibUp
  MODULE_UNI_FILE                = MpInitLibUp.uni
  FILE_GUID                      = 70E9818C-A4F0-4061-9FA2-2DFFC7016D6E
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.1
  LIBRARY_CLASS                  = MpInitLib

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources]
  MpInitLibUp.c

[Packages]
  MdePkg/MdePkg.dec
  UefiCpuPkg/UefiCpuPkg.dec

[LibraryClasses]
  DebugLib
  LocalApicLib
  HobLib
  BaseMemoryLib

[Ppis]
  gEfiSecPlatformInformationPpiGuid  ## SOMETIMES_CONSUMES

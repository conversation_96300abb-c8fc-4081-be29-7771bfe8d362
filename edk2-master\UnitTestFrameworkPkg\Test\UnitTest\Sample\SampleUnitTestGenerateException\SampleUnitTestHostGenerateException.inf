## @file
# Sample UnitTest built for execution on a Host/Dev machine.
# This test case generates an exception. For some host-based environments, this
# is a fatal condition that terminates the unit tests and no additional test
# cases are executed. On other environments, this condition may be report a unit
# test failure and continue with additional unit tests.
#
# Copyright (c) 2024, Intel Corporation. All rights reserved.<BR>
# SPDX-License-Identifier: BSD-2-Clause-Patent
##

[Defines]
  INF_VERSION    = 0x00010005
  BASE_NAME      = SampleUnitTestHostGenerateException
  FILE_GUID      = 842C65F7-E31A-4E67-85B2-72F2958636DF
  MODULE_TYPE    = HOST_APPLICATION
  VERSION_STRING = 1.0

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources]
  SampleUnitTestGenerateException.c

[Packages]
  MdePkg/MdePkg.dec

[LibraryClasses]
  BaseLib
  DebugLib
  UnitTestLib

[Pcd]
  gEfiMdePkgTokenSpaceGuid.PcdDebugPropertyMask

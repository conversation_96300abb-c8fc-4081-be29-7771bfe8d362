## @file
#  Instance of MM Unblock Page Library.
#
#  This library provides an interface to request non-MMRAM pages to be mapped/unblocked
#  from inside MM environment.
#
#  For MM modules that need to access regions outside of MMRAMs, the agents that set up
#  these regions are responsible for invoking this API in order for these memory areas
#  to be accessed from inside MM.
#
#  Copyright (c) Microsoft Corporation.
#  Copyright (c) 2024, Intel Corporation. All rights reserved.<BR>
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#
##

[Defines]
  INF_VERSION                    = 0x0001001B
  BASE_NAME                      = MmUnblockMemoryLib
  FILE_GUID                      = CBFE5800-70FD-4D9A-AA78-DB617294077E
  MODULE_TYPE                    = PEIM
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = MmUnblockMemoryLib

#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources]
  MmUnblockMemoryLib.c

[Packages]
  UefiCpuPkg/UefiCpuPkg.dec
  MdePkg/MdePkg.dec

[LibraryClasses]
  HobLib
  DebugLib
  BaseMemoryLib
  PeiServicesLib

[Ppis]
  gEfiPeiMmCommunicationPpiGuid

[Guids]
  gMmUnblockRegionHobGuid

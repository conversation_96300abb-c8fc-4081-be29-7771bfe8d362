;------------------------------------------------------------------------------
; @file
; First code exectuted by processor after resetting.
;
; Copyright (c) 2008 - 2013, Intel Corporation. All rights reserved.<BR>
; SPDX-License-Identifier: BSD-2-Clause-Patent
;
;------------------------------------------------------------------------------

BITS    16

ALIGN   16                      ; 0xffffffd0

applicationProcessorEntryPoint:
;
; Application Processors entry point
;
; GenFv generates code aligned on a 4k boundary which will jump to this
; location.  (0xffffffd0)  This allows the Local APIC Startup IPI to be
; used to wake up the application processors.
;
    jmp     short resetVector

ALIGN   16                      ; 0xffffffe0

peiCoreEntryPoint:
;
; PEI Core entry point
;
; GenFv fills the address of the PEI Core into this location
;
    DD      0x12345678

ALIGN   16                      ; 0xfffffff0

resetVector:
;
; Reset Vector
;
; This is where the processor will begin execution
;
    nop
    nop
    jmp     near $

ALIGN   8

ApStartupSegment:
    DD      0x12345678

BootFvBaseAddress:
    DD      0x12345678

ALIGN   16                      ; 0x100000000

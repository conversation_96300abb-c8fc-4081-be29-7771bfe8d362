## @file
# MM Smram save state service lib.
#
# This is MM Smram save state service lib that provide service to read and
# save savestate area registers.
#
# Copyright (C) 2023 Advanced Micro Devices, Inc. All rights reserved.<BR>
#
# SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 1.29
  BASE_NAME                      = AmdMmSaveStateLib
  FILE_GUID                      = FB7D0A60-E8D4-4EFA-90AA-B357BC569879
  MODULE_TYPE                    = DXE_SMM_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = MmSaveStateLib

[Sources]
  MmSaveState.h
  MmSaveStateCommon.c
  AmdMmSaveState.c

[Packages]
  MdePkg/MdePkg.dec
  UefiCpuPkg/UefiCpuPkg.dec

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  DebugLib
  MmServicesTableLib

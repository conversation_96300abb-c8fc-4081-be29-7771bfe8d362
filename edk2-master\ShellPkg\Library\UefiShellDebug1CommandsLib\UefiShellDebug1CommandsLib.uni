// /**
//
// Copyright (c) 2010 - 2018, Intel Corporation. All rights reserved.<BR>
// (C) Copyright 2013-2015 Hewlett-Packard Development Company, L.P.<BR>
// (C) Copyright 2016 - 2019 Hewlett Packard Enterprise Development LP<BR>
// SPDX-License-Identifier: BSD-2-Clause-Patent
//
// Module Name:
//
// UefiShellDebug1CommandsLib.uni
//
// Abstract:
//
// String definitions for UEFI Shell 2.0 Debug1 profile commands
//
//
// **/

/=#

#langdef   en-US "english"

#string STR_GEN_NO_MEM            #language en-US "%H%s%N: Memory is not available.\r\n"
#string STR_GEN_BOOT_ONLY         #language en-US "%H%s%N: Boot must be selected for hot key options.\r\n"
#string STR_GEN_FIND_FAIL         #language en-US "%H%s%N: File not found - '%H%s%N'\r\n"

#string STR_GEN_PROBLEM           #language en-US "%H%s%N: Unknown flag - '%H%s%N'\r\n"
#string STR_GEN_PROBLEM_VAL       #language en-US "%H%s%N: Bad value - '%H%s%N' for flag - '%H%s%N'\r\n"
#string STR_GEN_DUPLICATE         #language en-US "%H%s%N: Duplicate flag - '%H%s%N'\r\n"
#string STR_GEN_NO_VALUE          #language en-US "%H%s%N: Missing argument for flag - '%H%s%N'\r\n"
#string STR_GEN_TOO_FEW           #language en-US "%H%s%N: Too few arguments.\r\n"
#string STR_GEN_TOO_MANY          #language en-US "%H%s%N: Too many arguments.\r\n"
#string STR_GEN_NO_DRIVER_BOOT    #language en-US "%H%s%N: Driver or Boot must be selected.\r\n"
#string STR_GEN_PCIRBIO_NF        #language en-US "%H%s%N: Protocol - PciRootBridgeIo not found.\r\n"
#string STR_GEN_PCIRBIO_ER        #language en-US "%H%s%N: Problem accessing the data using Protocol - PciRootBridgeIo\r\n"
#string STR_GEN_PARAM_INV         #language en-US "%H%s%N: Invalid argument - '%H%s%N'\r\n"
#string STR_GEN_PARAM_INV_HEX     #language en-US "%H%s%N: Invalid parameter - '%H%s%N'. Must be hexadecimal.\r\n"
#string STR_GEN_PARAM_CONFLICT    #language en-US "%H%s%N: Flags conflict with - '%H%s%N' and '%H%s%N'\r\n"
#string STR_GEN_OUT_MEM           #language en-US "%H%s%N: Memory allocation was not successful.\r\n"
#string STR_GEN_MAP_PROTOCOL      #language en-US "%H%s%N: Mapped device '%B%s%N' does not have protocol %B%s%N\r\n"
#string STR_GEN_FILE_OPEN_FAIL    #language en-US "%H%s%N: Cannot open file - '%H%s%N'\r\n"
#string STR_GEN_FILE_DELETE_FAIL  #language en-US "%H%s%N: Cannot delete file - '%H%s%N'\r\n"
#string STR_GEN_NO_CWD            #language en-US "%H%s%N: Current directory not specified.\r\n"
#string STR_GEN_FILE_IS_DIRECTORY #language en-US "%H%s%N: The file '%H%s%N' is a directory.\r\n"
#string STR_GEN_SFO_HEADER        #language en-US "ShellCommand,"%s"\r\n"


#string STR_FILE_OPEN_FAIL        #language en-US "Unable to open file on '%B%s%N' with: %r.\r\n"
#string STR_FILE_FIND_FAIL        #language en-US "%H%s%N: File not found - '%H%s%N'\r\n"
#string STR_FILE_NOT_DIR          #language en-US "%H%s%N: Directories are not permitted - '%H%s%N'\r\n"
#string STR_SIZE_NOT_SPEC         #language en-US "%H%s%N: A valid size must be specified\r\n"
#string STR_FILE_NOT_SPEC         #language en-US "%H%s%N: File not specified\r\n"
#string STR_FILE_WRITE_FAIL       #language en-US "%H%s%N: Write file error - '%H%s%N'\r\n"
#string STR_FILE_READ_FAIL        #language en-US "%H%s%N: Read file error - '%H%s%N'\r\n"
#string STR_VOLUME_FULL           #language en-US "%H%s%N: The volume is full\r\n"

#string STR_SET_SIZE_FAIL         #language en-US "%H%s%N: Unable to change size on '%B%s%N'\r\n"
#string STR_SET_SIZE_DONE         #language en-US "Size changed on '%B%s%N'.\r\n"

#string STR_DBLK_HEADER           #language en-US "LBA %016LX Size %08x bytes BlkIo %0x\r\n"

#string STR_COMP_HEADER           #language en-US "Compare %s to %s.\r\n"
#string STR_COMP_DIFFERENCE_POINT #language en-US "Difference #% 2u:\r\n"
#string STR_COMP_END_OF_FILE      #language en-US " <EOF>"

#string STR_COMP_FOOTER_FAIL      #language en-US "[difference(s) encountered] \r\n"
#string STR_COMP_FOOTER_PASS      #language en-US "[no differences encountered]\r\n"

#string STR_MODE_SET_FAIL         #language en-US "%H%s%N: Unable to change the mode.\r\n"
#string STR_MODE_NO_MATCH         #language en-US "%H%s%N: No matching mode found to set\r\n"
#string STR_MODE_LIST_HEAD        #language en-US "Available modes for console output device.\r\n"
#string STR_MODE_LIST_ITEM        #language en-US "  Col % 5d Row % 5d  %c\r\n"

#string STR_MEMMAP_GET_FAILED     #language en-US "%H%s%N: Unable to get memory map\r\n"
#string STR_MEMMAP_LIST_HEAD      #language en-US "Type       Start            End              # Pages          Attributes\r\n"
#string STR_MEMMAP_LIST_ITEM      #language en-US "% -10s %016LX-%016LX %016LX %016LX\r\n"
#string STR_MEMMAP_LIST_ITEM_OTHER #language en-US "%08x   %016LX-%016LX %016LX %016LX\r\n"
#string STR_MEMMAP_LIST_SUMM      #language en-US " \r\n"
                                                  "  Reserved  : %,14ld Pages (%,ld Bytes)\r\n"
                                                  "  LoaderCode: %,14ld Pages (%,ld Bytes)\r\n"
                                                  "  LoaderData: %,14ld Pages (%,ld Bytes)\r\n"
                                                  "  BS_Code   : %,14ld Pages (%,ld Bytes)\r\n"
                                                  "  BS_Data   : %,14ld Pages (%,ld Bytes)\r\n"
                                                  "  RT_Code   : %,14ld Pages (%,ld Bytes)\r\n"
                                                  "  RT_Data   : %,14ld Pages (%,ld Bytes)\r\n"
                                                  "  ACPI_Recl : %,14ld Pages (%,ld Bytes)\r\n"
                                                  "  ACPI_NVS  : %,14ld Pages (%,ld Bytes)\r\n"
                                                  "  MMIO      : %,14ld Pages (%,ld Bytes)\r\n"
                                                  "  MMIO_Port : %,14ld Pages (%,ld Bytes)\r\n"
                                                  "  PalCode   : %,14ld Pages (%,ld Bytes)\r\n"
                                                  "  Unaccepted: %,14ld Pages (%,ld Bytes)\r\n"
                                                  "  Available : %,14ld Pages (%,ld Bytes)\r\n"
                                                  "  Persistent: %,14ld Pages (%,ld Bytes)\r\n"
#string STR_MEMMAP_LIST_SUMM_OTHER #language en-US    "  %08x  : %,14ld Pages (%,ld Bytes)\r\n"
#string STR_MEMMAP_LIST_SUMM2     #language en-US "              -------------- \r\n"
                                                  "Total Memory: %,14ld MB (%,ld Bytes)\r\n"
#string STR_MEMMAP_LIST_ITEM_SFO  #language en-US "MemoryMap,"%s","%LX","%LX","%LX","%LX"\r\n"
#string STR_MEMMAP_LIST_SUMM_SFO  #language en-US "MemoryMapSummary,"%Ld","%Ld","%Ld","%Ld","%Ld","%Ld","%Ld","%Ld","%Ld","%Ld","%Ld","%Ld","%Ld","%Ld","%Ld","%Ld", "%Ld"\r\n"

#string STR_EFI_COMPRESS_FAIL     #language en-US "Unable to compress: %r.\r\n"
#string STR_EFI_DECOMPRESS_FAIL   #language en-US "Unable to decompress: %r.\r\n"
#string STR_EFI_DECOMPRESS_NOPE   #language en-US "The file does not appear to be a compressed file.  Cannot continue. \"%H%s%N\"\r\n"

#string STR_DMEM_HEADER_ROW       #language en-US "Memory Address %016LX %X Bytes\r\n"
#string STR_DMEM_MMIO_HEADER_ROW  #language en-US "Memory Mapped IO Address %016LX %X Bytes\r\n"
#string STR_DMEM_SYSTEM_TABLE     #language en-US "\r\nValid EFI Header at Address %016Lx\r\n"
                                                  "---------------------------------------------\r\n"
                                                  "System: Table Structure size %08x revision %08x\r\n"
                                                  "ConIn (%016LX) ConOut (%016LX) StdErr (%016LX)\r\n"
                                                  "Runtime Services              %016LX\r\n"
                                                  "Boot Services                 %016LX\r\n"
                                                  "SAL System Table              %016LX\r\n"
                                                  "ACPI Table                    %016LX\r\n"
                                                  "ACPI 2.0 Table                %016LX\r\n"
                                                  "MPS Table                     %016LX\r\n"
                                                  "SMBIOS Table                  %016LX\r\n"
                                                  "DTB Table                     %016LX\r\n"
                                                  "Memory Attribute Table        %016LX\r\n"
                                                  "RT Properties Table           %016LX\r\n"
                                                  "System Resource Table         %016LX\r\n"
                                                  "Debug Image Info Table        %016LX\r\n"
                                                  "Image Execution Info Table    %016LX\r\n"
                                                  "Json Config Data Table        %016LX\r\n"
                                                  "Json Capsule Data Table       %016LX\r\n"
                                                  "Json Capsule Results Table    %016LX\r\n"
                                                  "Memory Range Capsule          %016LX\r\n"
                                                  "Hii Database Export Buffer    %016LX\r\n"
                                                  "Conformance Profile Table     %016LX\r\n"
#string STR_DMEM_RT_PROPERTIES    #language en-US "\r\nRT Properties Table\r\n"
                                                  "----------------------------------------\r\n"
                                                  "Version           0x%01LX\r\n"
                                                  "Runtime Services Supported:\r\n"
                                                  "  GET_TIME                         %d\r\n"
                                                  "  GET_WAKEUP_TIME                  %d\r\n"
                                                  "  SET_TIME                         %d\r\n"
                                                  "  SET_WAKEUP_TIME                  %d\r\n"
                                                  "  GET_VARIABLE                     %d\r\n"
                                                  "  GET_NEXT_VARIABLE_NAME           %d\r\n"
                                                  "  SET_VARIABLE                     %d\r\n"
                                                  "  SET_VIRTUAL_ADDRESS_MAP          %d\r\n"
                                                  "  CONVERT_POINTERS                 %d\r\n"
                                                  "  GET_NEXT_HIGH_MONOTONIC_COUNT    %d\r\n"
                                                  "  RESET_SYSTEM                     %d\r\n"
                                                  "  UPDATE_CAPSULE                   %d\r\n"
                                                  "  QUERY_CAPSULE_CAPABILITIES       %d\r\n"
                                                  "  QUERY_VARIABLE_INFO              %d\r\n"
#string STR_DMEM_IMG_EXE_TABLE    #language en-US "\r\nImage Execution Table\r\n"
                                                  "----------------------------------------\r\n"
#string STR_DMEM_IMG_EXE_ENTRY    #language en-US "%20s: %s\r\n"
#string STR_DMEM_CONF_PRO_TABLE   #language en-US "\r\nConformance Profile Table\r\n"
                                                  "----------------------------------------\r\n"
                                                  "Version     0x1\r\n"
                                                  "Profile GUIDs:\r\n"
#string STR_DMEM_CONF_PRO_ROW     #language en-US "    %s    %g\r\n"
#string STR_DMEM_ERR_NOT_FOUND    #language en-US "\r\n%H%s%N: Table address not found.\r\n"
#string STR_DMEM_ERR_GET_FAIL     #language en-US "\r\n%H%s%N: Unable to get table information.\r\n"

#string STR_LOAD_PCI_ROM_RES      #language en-US "Image '%B%s%N' load result: %r\r\n"
#string STR_LOADPCIROM_CORRUPT    #language en-US "%H%s%N: File '%B%s%N' Image %d is corrupt.\r\n"
#string STR_LOADPCIROM_LOAD_FAIL  #language en-US "%H%s%N: File '%B%s%N' Image %d unable to load.\r\n"
#string STR_LOADPCIROM_START_FAIL #language en-US "%H%s%N: File '%B%s%N' Image %d unable to start.\r\n"

#string STR_MM_NOT_ALIGNED        #language en-US "%H%s%N: Address parameter %016LX is not aligned.\r\n"
#string STR_MM_PCIE_ADDRESS_RANGE #language en-US "%H%s%N: Address parameter %016LX is not a valid PCI/PCIE address.\r\n"
#string STR_MM_MMIO               #language en-US "%HMMIO%N"
#string STR_MM_IO                 #language en-US "%HIO%N"
#string STR_MM_PCI                #language en-US "%HPCI%N"
#string STR_MM_MEM                #language en-US "%HMEM%N"
#string STR_MM_PCIE               #language en-US "%HPCIE%N"
#string STR_MM_ADDRESS            #language en-US "  0x%016lx : "
#string STR_MM_BUF                #language en-US "0x%0*lx"
#string STR_MM_ERROR              #language en-US "%H%s%N: Input had incorrect format\r\n"

#string STR_SETVAR_PRINT          #language en-US "%g - %s - %04x Bytes\r\n"
#string STR_SETVAR_ERROR_SET      #language en-US "%H%s%N: Unable to set - %H%g%N - %H%s%N\r\n"
#string STR_SETVAR_ERROR_GET      #language en-US "%H%s%N: Unable to get - %H%g%N - %H%s%N\r\n"
#string STR_SETVAR_ERROR_DPFT     #language en-US "%H%s%N: DevicePathFromText conversion was not successful.\r\n"

#string STR_SERMODE_NO_FOUND      #language en-US "%H%s%N: No serial ports found.\r\n"
#string STR_SERMODE_NOT_FOUND     #language en-US "%H%s%N: No serial port or specified serial port found.\r\n"
#string STR_SERMODE_BAD_HANDLE    #language en-US "%H%s%N: Handle %H%02x%N is not a serial device handle.\r\n"
#string STR_SERMODE_SET_HANDLE    #language en-US "Mode set on handle %H%02x%N successfully.\r\n"
#string STR_SERMODE_SET_FAIL      #language en-US "%H%s%N: Mode change on handle %H%02x%N was not successful.\r\n"
#string STR_SERMODE_DISPLAY       #language en-US "%x(%08x) - (%ld, %c, %d, %s)\r\n"
#string STR_SERMODE_SET_UNSUPPORTED #language en-US "%H%s%N: One or more of the new settings is not supported on handle %H%02x%N.\r\n"
#string STR_SERMODE_SET_DEV_ERROR  #language en-US "%H%s%N: The serial device on handle %H%02x%N is not functioning correctly.\r\n"

#string STR_PCI_HANDLE_CFG_ERR    #language en-US "%H%s%N: Handle protocol or configuration error.\r\n"
#string STR_PCI_BUS_RANGE_ERR     #language en-US "%H%s%N: Get next bus range error.\r\n"
#string STR_PCI_NO_FIND           #language en-US "%H%s%N: Cannot find protocol interface for segment %x, bus %x.\r\n"
#string STR_PCI_NO_CFG            #language en-US "%H%s%N: Cannot read configuration data.\r\n"
#string STR_PCI_INFO              #language en-US "%H  PCI Segment %02x Bus %02x Device %02x Func %02x%N [EFI %02x%02x%02x%02x00]\r\n"
#string STR_PCI_TITLE             #language en-US "   Seg  Bus  Dev  Func\r\n"
                                                  "   ---  ---  ---  ----\r\n"
#string STR_PCI_LINE_P1           #language en-US "    %E%02x   %02x   %02x    %02x ==> %N"
#string STR_PCI_LINE_P2           #language en-US "\r\n             Vendor %04x Device %04x Prog Interface %x\r\n"
#string STR_PCIEX_CAPABILITY_CAPID     #language en-US "CapID(%2x):          %E%02x%N"
#string STR_PCIEX_NEXTCAP_PTR          #language en-US "            NextCap Ptr(%2x):    %E%02x%N\r\n"
#string STR_PCIEX_CAP_REGISTER         #language en-US "Cap Register(%2x):             %E%04x%N\r\n"
#string STR_PCIEX_DEVICE_CAP           #language en-US "Device Capabilities(%2x):      %E%08x%N\r\n"
#string STR_PCIEX_DEVICE_CONTROL       #language en-US "Device Control(%2x):           %E%04x%N\r\n"
#string STR_PCIEX_DEVICE_STATUS        #language en-US "Device Status(%2x):            %E%04x%N\r\n"
#string STR_PCIEX_LINK_CAPABILITIES    #language en-US "Link Capabilities(%2x):        %E%08x%N\r\n"
#string STR_PCIEX_LINK_CONTROL         #language en-US "Link Control(%2x):             %E%04x%N\r\n"
#string STR_PCIEX_LINK_STATUS          #language en-US "Link Status(%2x):              %E%04x%N\r\n"
#string STR_PCIEX_SLOT_CAPABILITIES    #language en-US "Slot Capabilities(%2x):        %E%08x%N\r\n"
#string STR_PCIEX_SLOT_CONTROL         #language en-US "Slot Control(%2x):             %E%04x%N\r\n"
#string STR_PCIEX_SLOT_STATUS          #language en-US "Slot Status(%2x):              %E%04x%N\r\n"
#string STR_PCIEX_ROOT_CONTROL         #language en-US "Root Control(%2x):             %E%04x%N\r\n"
#string STR_PCIEX_RSVDP                #language en-US "Root Capabilities(%2x):        %E%04x%N\r\n"
#string STR_PCIEX_ROOT_STATUS          #language en-US "Root Status(%2x):              %E%08x%N\r\n"
#string STR_PCI_LINE_VID_DID      #language en-US "Vendor ID(%x): %E%04x%N     Device ID(%x): %E%04x%N\r\n"
#string STR_PCI_LINE_RID          #language en-US "Revision ID(%x): %E%02x%N "
#string STR_PCI_LINE_BIST         #language en-US "BIST(%02x): "
#string STR_PCI_LINE_CAP          #language en-US "Capable,Return: %E%02x%N\r\n"
#string STR_PCI_LINE_CAP_NO       #language en-US " Incapable\r\n"
#string STR_PCI2_CACHE_LINE_SIZE       #language en-US "Cache Line Size(%x): %E%02x%N                 "
#string STR_PCI2_LATENCY_TIMER         #language en-US "Latency Timer(%x): %E%02x%N\r\n"
#string STR_PCI2_HEADER_TYPE           #language en-US "Header Type(%02x):    %E%02x%N, "
#string STR_PCI2_MULTI_FUNCTION        #language en-US "Multi-function, "
#string STR_PCI2_SINGLE_FUNCTION       #language en-US "Single function, "
#string STR_PCI2_PCI_DEVICE            #language en-US "PCI device\r\n"
#string STR_PCI2_P2P_BRIDGE            #language en-US "P2P bridge\r\n"
#string STR_PCI2_CARDBUS_BRIDGE        #language en-US "CardBus bridge\r\n"
#string STR_PCI2_RESERVED              #language en-US "Reserved\r\n"
#string STR_PCI2_CLASS                 #language en-US "Class: "
#string STR_PCI2_BASE_ADDR             #language en-US "Base Address Registers(%x):\r\n"
#string STR_PCI2_START_TYPE            #language en-US "     Start_Address  Type  Space    Prefetchable?     Size             Limit\r\n"
#string STR_PCI2_NONE                  #language en-US "  (None)"
#string STR_PCI2_EXPANSION_ROM_DISABLED #language en-US "\r\nExpansion ROM Disabled(%x)\r\n\r\n"
#string STR_PCI2_EXPANSION_ROM_BASE    #language en-US "\r\nExpansion ROM Base Address(%x): %E%08x%N\r\n\r\n"
#string STR_PCI2_CARDBUS_CIS           #language en-US "Cardbus CIS ptr(%x):   %E%08x%N\r\n"
#string STR_PCI2_SUB_VENDOR_ID         #language en-US "Sub VendorID(%x):          %E%04x%N      "
#string STR_PCI2_SUBSYSTEM_ID          #language en-US "Subsystem ID(%x):      %E%04x%N\r\n"
#string STR_PCI2_CAPABILITIES_PTR      #language en-US "Capabilities Ptr(%x):        %E%02x%N\r\n"
#string STR_PCI2_INTERRUPT_LINE        #language en-US "Interrupt Line(%x):          %E%02x%N      "
#string STR_PCI2_INTERRUPT_PIN         #language en-US "Interrupt Pin(%x):       %E%02x%N\r\n"
#string STR_PCI2_MIN_GNT               #language en-US "Min_Gnt(%x):                 %E%02x%N      "
#string STR_PCI2_MAX_LAT               #language en-US "Max_Lat(%x):             %E%02x%N\r\n"
#string STR_PCI2_BASE_ADDRESS          #language en-US "Base Address Registers(%x):"
#string STR_PCI2_START_TYPE_2          #language en-US "     Start_Address  Type  Space    Prefetchable?     Size             Limit\r\n"
#string STR_PCI2_NO_EXPANSION_ROM      #language en-US "\r\nNo Expansion ROM(%x)    "
#string STR_PCI2_EXPANSION_ROM_BASE_2  #language en-US "\r\nExpansion ROM Base Address(%x): %E%08x%N\r\n"
#string STR_PCI2_BUS_NUMBERS           #language en-US "\r\n\r\n(Bus Numbers)  Primary(%x)     Secondary(%x)   Subordinate(%x)\r\n"
#string STR_PCI2_BRIDGE                #language en-US "               %E%02x%N"
#string STR_PCI2_SECONDARY_TIMER       #language en-US "\r\nSecondary Latency Timer(%x):       %E%02x%N\n\n"
#string STR_PCI2_CARDBUS_LATENCY       #language en-US "\r\nCardBus Latency Timer(%x):         %E%02x%N\r\n"
#string STR_PCI2_RESOURCE_TYPE_2       #language en-US "\r\nResource Type              Type              Base                Limit\r\n"
#string STR_PCI2_MEM_3                 #language en-US "Mem(%x)      %s          %E%08x%N             %E%08x%N\r\n"
#string STR_PCI2_IO_2                  #language en-US "I/O(%x)      %s          %E%08x%N             %E%08x%N\r\n"
#string STR_PCI2_INTERRUPT_LINE_3      #language en-US "\r\nInterrupt Line(%x):     %E%02x%N             Interrupt Pin(%x):       %E%02x%N\r\n"
#string STR_PCI2_SUB_VENDOR_ID_2       #language en-US "\r\nSub VendorID(%x):     %E%04x%N             Subsystem ID(%x):      %E%04x%N\r\n"
#string STR_PCI2_OPTIONAL              #language en-US "Optional 16-Bit PC Card legacy Mode Base Address(%x): %E%08x%N\r\n"
#string STR_PCI2_STATUS                #language en-US "Status(%x): %E%04x%N\r\n"
#string STR_PCI2_SECONDARY_STATUS      #language en-US "Secondary Status(%2x): %E%4x%N\r\n"
#string STR_PCI2_NEW_CAPABILITIES      #language en-US "  (04)New Capabilities linked list:   %E%d%N"
#string STR_PCI2_66_CAPABLE            #language en-US "  (05)66MHz Capable:                %EN/A%N\r\n"
#string STR_PCI2_66_CAPABLE_2          #language en-US "  (05)66MHz Capable:                  %E%d%N\r\n"
#string STR_PCI2_FAST_BACK             #language en-US "  (07)Fast Back-to-Back Capable:      %E%d%N"
#string STR_PCI2_NO                    #language en-US "No     "
#string STR_PCI2_YES                   #language en-US "YES    "
#string STR_PCI2_ONE_VAR_4             #language en-US "\r\n              %E%04x%N  "
#string STR_PCI2_NEWBAR_32             #language en-US "        %08x  "
#string STR_PCI2_NEWBAR_32_2           #language en-US "        %08x"
#string STR_PCI2_RSHIFT                #language en-US "%08x"
#string STR_PCI2_NEWBAR_32_3           #language en-US "%04x              "
#string STR_PCI2_NEWBAR_32_4           #language en-US "%04x"
#string STR_PCI2_CARDBUS_SOCKET        #language en-US "\r\nCardBus Socket Registers/ExCA Base Address Register(%x): %E%8x%N\r\n\r\n"
#string STR_PCI2_BUS_NUMBERS_2         #language en-US "\r\n(Bus Numbers)  Pci(%x)         CardBus(%x)     Subordinate(%x)\r\n"
#string STR_PCI2_CARDBUS               #language en-US "               %E%02x%N"
#string STR_PCI2_CARDBUS_2             #language en-US "                %E%02x%N"
#string STR_PCI2_CARDBUS_3             #language en-US "                %E%02x%N\r\n"
#string STR_PCI2_MASTER_DATA           #language en-US "  (08)Master Data Parity Error:       %E%d%N\r\n"
#string STR_PCI2_DEVSEL_TIMING         #language en-US "  (09)DEVSEL timing:           "
#string STR_PCI2_FAST                  #language en-US "%E    Fast%N"
#string STR_PCI2_MEDIUM                #language en-US "%E  Medium%N"
#string STR_PCI2_SLOW                  #language en-US "%E    Slow%N"
#string STR_PCI2_RESERVED_2            #language en-US "%EReserved%N"
#string STR_PCI2_SIGNALED_TARGET       #language en-US "  (11)Signaled Target Abort:          %E%d%N\r\n"
#string STR_PCI2_RECEIVED_TARGET       #language en-US "  (12)Received Target Abort:          %E%d%N"
#string STR_PCI2_RECEIVED_MASTER       #language en-US "  (13)Received Master Abort:          %E%d%N\r\n"
#string STR_PCI2_SIGNALED_ERROR        #language en-US "  (14)Signaled System Error:          %E%d%N"
#string STR_PCI2_RECEIVED_ERROR        #language en-US "  (14)Received System Error:          %E%d%N"
#string STR_PCI2_DETECTED_ERROR        #language en-US "  (15)Detected Parity Error:          %E%d%N\r\n"
#string STR_PCI2_COMMAND               #language en-US "Command(%x): %E%04x%N\r\n"
#string STR_PCI2_SPACE_ACCESS_DENIED   #language en-US "  (00)I/O space access enabled:       %E%d%N"
#string STR_PCI2_MEMORY_SPACE          #language en-US "  (01)Memory space access enabled:    %E%d%N\r\n"
#string STR_PCI2_BEHAVE_BUS_MASTER     #language en-US "  (02)Behave as bus master:           %E%d%N"
#string STR_PCI2_MONITOR_SPECIAL_CYCLE #language en-US "  (03)Monitor special cycle enabled:  %E%d%N\r\n"
#string STR_PCI2_MEM_WRITE_INVALIDATE  #language en-US "  (04)Mem Write & Invalidate enabled: %E%d%N"
#string STR_PCI2_PALETTE_SNOOPING      #language en-US "  (05)Palette snooping is enabled:    %E%d%N\r\n"
#string STR_PCI2_ASSERT_PERR           #language en-US "  (06)Assert PERR# when parity error: %E%d%N"
#string STR_PCI2_DO_ADDR_STEPPING      #language en-US "  (07)Do address/data stepping:       %E%d%N\r\n"
#string STR_PCI2_SERR_DRIVER           #language en-US "  (08)SERR# driver enabled:           %E%d%N"
#string STR_PCI2_FAST_BACK_2           #language en-US "  (09)Fast back-to-back transact...:  %E%d%N\r\n\r\n"
#string STR_PCI2_BRIDGE_CONTROL        #language en-US "Bridge Control(%x)     %E%04x%N\r\n"
#string STR_PCI2_PARITY_ERROR          #language en-US "  (00)Parity Error Response:          %E%d%N"
#string STR_PCI2_SERR_ENABLE           #language en-US "  (01)SERR# Enable:                   %E%d%N\r\n"
#string STR_PCI2_ISA_ENABLE            #language en-US "  (02)ISA Enable:                     %E%d%N"
#string STR_PCI2_RESOURCE_TYPE         #language en-US "\r\nResource Type                            Base                    Limit\r\n"
#string STR_PCI2_TWO_VARS              #language en-US "I/O(%x)                            %E%08x%N"
#string STR_PCI2_ONE_VAR               #language en-US "                 %E%08x%N\r\n"
#string STR_PCI2_MEMORY                #language en-US "Memory(%x)                         %E%08x%N"
#string STR_PCI2_PREFETCHABLE          #language en-US "Prefetchable Memory(%x)    %E%08x%08x%N"
#string STR_PCI2_TWO_VARS_2            #language en-US "         %E%08x%08x%N\r\n"
#string STR_PCI2_CAPABILITIES_PTR_2    #language en-US "\r\nCapabilities Ptr(%x):   %E%02x%N             \r\n\r\n"
#string STR_PCI2_INTERRUPT_LINE_2      #language en-US "\r\nInterrupt Line(%x)      %E%02x%N             "
#string STR_PCI2_BAR                   #language en-US "\r\n          %E%08x%N  "
#string STR_PCI2_MEM                   #language en-US "Mem   "
#string STR_PCI2_32_BITS               #language en-US "32 bits  "
#string STR_PCI2_ONE_VAR_2             #language en-US "\r\n  %E%08x"
#string STR_PCI2_ONE_VAR_3             #language en-US "%08x%N  "
#string STR_PCI2_64_BITS               #language en-US "64 bits  "
#string STR_PCI2_MEM_2                 #language en-US "Mem            "
#string STR_PCI2_VGA_ENABLE            #language en-US "  (03)VGA Enable:                     %E%d%N\r\n"
#string STR_PCI2_MASTER_ABORT          #language en-US "  (05)Master Abort Mode:              %E%d%N"
#string STR_PCI2_SECONDARY_BUS_RESET   #language en-US "  (06)Secondary Bus Reset:            %E%d%N\r\n"
#string STR_PCI2_FAST_ENABLE           #language en-US "  (07)Fast Back-to-Back Enable:       %E%d%N"
#string STR_PCI2_PRIMARY_DISCARD_TIMER #language en-US "  (08)Primary Discard Timer:       %E%s%N\r\n"
#string STR_PCI2_SECONDARY_DISCARD_TIMER #language en-US "  (09)Secondary Discard Timer:     %E%s%N"
#string STR_PCI2_DISCARD_TIMER_STATUS  #language en-US "  (10)Discard Timer Status:           %E%d%N\r\n"
#string STR_PCI2_DISCARD_TIMER_SERR    #language en-US "  (11)Discard Timer SERR# Enable:     %E%d%N\r\n"
#string STR_PCI2_CARDBUS_RESET         #language en-US "  (06)CardBus Reset:                  %E%d%N\r\n"
#string STR_PCI2_IREQ_ENABLE           #language en-US "  (07)IREQ/INT Enable:                %E%d%N"
#string STR_PCI2_WRITE_POSTING_ENABLE  #language en-US "  (10)Write Posting Enable:           %E%d%N\r\n"
#string STR_PCI_EXT_CAP_AER            #language en-US "  Advanced Error Reporting\r\n"
                                                       "    UncorrectableErrorStatus       %08x\r\n"
                                                       "    UncorrectableErrorMask         %08x\r\n"
                                                       "    UncorrectableErrorSeverity     %08x\r\n"
                                                       "    CorrectableErrorStatus         %08x\r\n"
                                                       "    CorrectableErrorMask           %08x\r\n"
                                                       "    AdvancedErrorCapAndControl     %08x\r\n"
                                                       "    HeaderLog1                     %08x\r\n"
                                                       "    HeaderLog2                     %08x\r\n"
                                                       "    HeaderLog3                     %08x\r\n"
                                                       "    HeaderLog4                     %08x\r\n"
                                                       "    RootErrorCommand               %08x\r\n"
                                                       "    RootErrorStatus                %08x\r\n"
                                                       "    ErrorSourceIdentification      %04x\r\n"
                                                       "    CorrectableErrorSourceIden     %04x\r\n"
                                                       "    TlpPrefixLog1                  %08x\r\n"
                                                       "    TlpPrefixLog2                  %08x\r\n"
                                                       "    TlpPrefixLog3                  %08x\r\n"
                                                       "    TlpPrefixLog4                  %08x\r\n"
#string STR_PCI_EXT_CAP_LINK_CONTROL   #language en-US "  Link Control\r\n"
                                                       "    RootComplexLinkCapabilities    %08x\r\n"
                                                       "    RootComplexLinkControl         %04x\r\n"
                                                       "    RootComplexLinkStatus          %04x\r\n"
#string STR_PCI_EXT_CAP_LINK_DECLAR    #language en-US "  Link Declaration\r\n"
                                                       "    ElementSelfDescription         %08x\r\n"
#string STR_PCI_EXT_CAP_LINK_DECLAR2   #language en-US "    LinkEntry[%x]                  %08x\r\n"
#string STR_PCI_EXT_CAP_SN             #language en-US "  Serial Number\r\n"
                                                       "    SerialNumber                   %L16x\r\n"
#string STR_PCI_EXT_CAP_POWER          #language en-US "  Power Budgeting\r\n"
                                                       "    DataSelect                     %02x\r\n"
                                                       "    Data                           %08x\r\n"
                                                       "    PowerBudgetCapability          %02x\r\n"
#string STR_PCI_EXT_CAP_ACS            #language en-US "  ACS\r\n"
                                                       "    CapabilityRegister             %04x\r\n"
                                                       "    ControlRegister                %04x\r\n"
#string STR_PCI_EXT_CAP_ACS2           #language en-US "    EgressControlVectorByte[%x]    %02x\r\n"
#string STR_PCI_EXT_CAP_LAT            #language en-US "  Latency Tolerance Reporting\r\n"
                                                       "    MaxSnoopLatency                %04x\r\n"
                                                       "    MaxNoSnoopLatency              %04x\r\n"
#string STR_PCI_EXT_CAP_ARI            #language en-US "  ARI\r\n"
                                                       "    AriCapability                  %04x\r\n"
                                                       "    AriControl                     %04x\r\n"
#string STR_PCI_EXT_CAP_RCRB           #language en-US "  RCRB\r\n"
                                                       "    VendorId                       %04x\r\n"
                                                       "    DeviceId                       %04x\r\n"
                                                       "    RcrbCapabilities               %04x\r\n"
                                                       "    RcrbControl                    %04x\r\n"
#string STR_PCI_EXT_CAP_VEN            #language en-US "  VendorSpecific\r\n"
                                                       "    VendorSpecificHeader           %04x\r\n"
#string STR_PCI_EXT_CAP_DPA            #language en-US "  DPA\r\n"
                                                       "    DpaCapability                  %04x\r\n"
                                                       "    DpaLatencyIndicator            %04x\r\n"
                                                       "    DpaStatus                      %04x\r\n"
                                                       "    DpaControl                     %04x\r\n"
#string STR_PCI_EXT_CAP_DPA2           #language en-US "    DpaPowerAllocationArray[%x]    %02x\r\n"
#string STR_PCI_EXT_CAP_ECEA           #language en-US "  Event Collector Endpoint Association\r\n"
                                                       "    AssociationBitmap              %04x\r\n"
#string STR_PCI_EXT_CAP_VC_BASE        #language en-US "  Virtual (Multi) Channel Capability\r\n"
                                                       "    ExtendedVcCount                %08x\r\n"
                                                       "    PortCapability1                %08x\r\n"
                                                       "    PortCapability2                %08x\r\n"
                                                       "    ArbitrationTableOffset         %08x\r\n"
                                                       "    PortVcControl                  %04x\r\n"
                                                       "    PortVcStatus                   %04x\r\n"
#string STR_PCI_EXT_CAP_VC_ITEM        #language en-US "  Virtual Channel Capability Extended Item[%x]\r\n"
                                                       "    ResourceCapability             %08x\r\n"
                                                       "    ArbitrationTableOffset         %08x\r\n"
                                                       "    ResourceControl                %08x\r\n"
                                                       "    ResourceStatus                 %04x\r\n"
#string STR_PCI_EXT_CAP_MULTICAST      #language en-US "  MultiCast Capability\r\n"
                                                       "    MultiCastCapability            %04x\r\n"
                                                       "    MulticastControl               %04x\r\n"
                                                       "    McBaseAddress                  %L16x\r\n"
                                                       "    McReceiveAddress               %L16x\r\n"
                                                       "    McBlockAll                     %L16x\r\n"
                                                       "    McBlockUntranslated            %L16x\r\n"
                                                       "    McOverlayBar                   %L16x\r\n"
#string STR_PCI_EXT_CAP_RESIZE_BAR     #language en-US "  Resizeable Bar Capability [%x]\r\n"
                                                       "    ResizableBarCapability         %08x\r\n"
                                                       "    ResizableBarControl            %04x\r\n"
#string STR_PCI_EXT_CAP_TPH            #language en-US "  TPH\r\n"
                                                       "    TphRequesterCapability         %08x\r\n"
                                                       "    TphRequesterControl            %04x\r\n"
                                                       "    TphTable (optional):\r\n"
#string STR_PCI_EXT_CAP_SECONDARY      #language en-US "  Secondary PCI Express Extended Capability\r\n"
                                                       "    LinkControl3                   %08x\r\n"
                                                       "    LaneErrorStatus                %08x\r\n"
                                                       "    EqualizationControl:\r\n"
#string STR_PCI_EXT_CAP_INTEGRITY_ENCRYPTION #language en-US "  Integrity and Data Encryption Capability\r\n"
                                                       "    IdeCapabilities                %08x\r\n"
                                                       "    IdeControl                     %04x\r\n"
                                                       "    IdeStatus                      %04x\r\n"
#string STR_PCI_EXT_CAP_ATS            #language en-US "  Address Translation Services (ATS) Capability\r\n"
                                                       "    AtsCapability                  %04x\r\n"
                                                       "    AtsControl                     %04x\r\n"
#string STR_PCI_EXT_CAP_SRIOV          #language en-US "  Single Root I/O Virtualization (SR-IOV) Capability\r\n"
                                                       "    SriovCapability                %04x\r\n"
                                                       "    SriovControl                   %04x\r\n"
                                                       "    SriovStatus                    %04x\r\n"
                                                       "    InitialVFs                     %04x\r\n"
                                                       "    TotalVFs                       %04x\r\n"
                                                       "    NumVFs                         %04x\r\n"
                                                       "    FunctionDependencyLink         %04x\r\n"
                                                       "    FirstVfOffset                  %04x\r\n"
                                                       "    VfStride                       %04x\r\n"
                                                       "    VfDeviceId                     %04x\r\n"
                                                       "    SupportedPageSizes             %08x\r\n"
                                                       "    SystemPageSize                 %08x\r\n"
#string STR_PCI_EXT_CAP_SRIOV_BARS     #language en-US "  VF BAR Information:\r\n"
                                                       "    VF BAR0                       %08x\r\n"
                                                       "    VF BAR1                       %08x\r\n"
                                                       "    VF BAR2                       %08x\r\n"
                                                       "    VF BAR3                       %08x\r\n"
                                                       "    VF BAR4                       %08x\r\n"
                                                       "    VF BAR5                       %08x\r\n"
                                                       "    VF Migration State Offset     %08x\r\n"
#string STR_PCI_EXT_CAP_PRI            #language en-US "  Page Request Interface (PRI) Capability\r\n"
                                                       "    PriControl                     %04x\r\n"
                                                       "    PriStatus                      %04x\r\n"
                                                       "    PriMaxRequestedPages           %08x\r\n"
#string STR_PCI_EXT_CAP_PASID          #language en-US "  Process Address Space ID (PASID) Capability\r\n"
                                                       "    PasidCapability                %04x\r\n"
                                                       "    PasidControl                   %04x\r\n"
                                                       "    PasidMaxValue                  %08x\r\n"
#string STR_PCI_EXT_CAP_L1_PM_SUBSTATES #language en-US "  L1 PM Substates Capability\r\n"
                                                       "    L1PmSubstatesCapability        %08x\r\n"
                                                       "    L1PmSubstatesControl1          %08x\r\n"
                                                       "    L1PmSubstatesControl2          %08x\r\n"
#string STR_PCI_EXT_CAP_DESIGNATED_VENDOR_SPECIFIC  #language en-US "  Designated Vendor Specific Extended Capability\r\n"
                                                       "    DVSEC Vendor ID:                %04x\r\n"
                                                       "    DVSEC Revision:                 %x\r\n"
                                                       "    DVSEC ID:                       %04x\r\n"
                                                       "    DVSEC Length:                   %x\r\n"
#string STR_PCI_EXT_CAP_VF_RESIZABLE_BAR_HEADER #language en-US "  VF Resizable BAR Extended Capability\r\n"
#string STR_PCI_EXT_CAP_VF_RESIZABLE_BAR_ENTRY #language en-US "  Entry[%d]:\r\n"
                                                       "    VfResizableBarCapability      %08x\r\n"
                                                       "    VfResizableBarControl         %08x\r\n"
#string STR_PCI_EXT_CAP_VF_RESIZABLE_BAR_DETAILS #language en-US "    Detailed Information:\r\n"
                                                       "      VfBarSizeCapability         %07x\r\n"
                                                       "      VfBarIndex                  %01x\r\n"
                                                       "      VfResizableBarNumber        %01x\r\n"
                                                       "      VfBarSize                   %02x\r\n"
                                                       "      VfBarSizeCapability (Ctrl)  %04x\r\n"
#string STR_PCI_EXT_CAP_DATA_LINK_FEATURE #language en-US "  Data Link Feature Capability\r\n"
                                                       "    DataLinkFeatureCapability      %08x\r\n"
                                                       "    DataLinkFeatureControl         %08x\r\n"
                                                       "    DataLinkFeatureStatus          %08x\r\n"
#string STR_PCI_EXT_CAP_PHYSICAL_LAYER_16 #language en-US "  Physical Layer 16.0 GT/s Extended Capability\r\n"
                                                       "    Capabilities:                     %08x\r\n"
                                                       "    Control:                          %08x\r\n"
                                                       "    Status:                           %08x\r\n"
#string STR_PCI_EXT_CAP_PHYSICAL_LAYER_16_STATUS #language en-US "  Physical Layer 16.0 Status Details:\r\n"
                                                       "    Equalization Complete:           %d\r\n"
                                                       "    Equalization Phase 1 Success:    %d\r\n"
                                                       "    Equalization Phase 2 Success:    %d\r\n"
                                                       "    Equalization Phase 3 Success:    %d\r\n"
                                                       "    Link Equalization Request:       %d\r\n"
#string STR_PCI_EXT_CAP_PHYSICAL_LAYER_16_PARITY #language en-US "  Physical Layer 16.0 Parity Status:\r\n"
                                                       "    Local Data Parity Mismatch:      %08x\r\n"
                                                       "    First Retimer Parity Mismatch:   %08x\r\n"
                                                       "    Second Retimer Parity Mismatch:  %08x\r\n"
#string STR_PCI_EXT_CAP_LANE_MARGINING #language en-US "  Lane Margining at Receiver Extended Capability\r\n"
                                                       "    Capability:                      %02x\r\n"
                                                       "    Control:                         %02x\r\n"
                                                       "    Status:                          %02x\r\n"
                                                       "    Error Counter:                   %08x\r\n"
#string STR_PCI_EXT_CAP_LANE_MARGINING_CAPABILITY #language en-US "  Lane Margining Capability Details:\r\n"
                                                       "    Max Lane Number:              %d\r\n"
#string STR_PCI_EXT_CAP_LANE_MARGINING_CONTROL #language en-US "  Lane Margining Control Details:\r\n"
                                                       "    Lane Number:                  %d\r\n"
                                                       "    Rcv Error Counter Select:     %d\r\n"
                                                       "    Lane Margin Step Select:      %d\r\n"
#string STR_PCI_EXT_CAP_LANE_MARGINING_STATUS #language en-US "  Lane Margining Status Details:\r\n"
                                                       "    Max Lanes Receiving Test Pattern: %d\r\n"
#string STR_PCI_EXT_CAP_PHYSICAL_LAYER_32 #language en-US "  Physical Layer 32.0 GT/s Extended Capability\r\n"
                                                       "    Capabilities:                     %08x\r\n"
                                                       "    Control:                          %08x\r\n"
                                                       "    Status:                           %08x\r\n"
#string STR_PCI_EXT_CAP_PHYSICAL_LAYER_32_CAPABILITIES #language en-US "  Physical Layer 32.0 GT/s Capabilities Details:\r\n"
                                                       "    Equalization Bypass Support:     %d\r\n"
                                                       "    No Equalization Needed Support:  %d\r\n"
                                                       "    TS Usage Mode 0 Support:         %d\r\n"
                                                       "    TS Usage Mode 1 Support:         %d\r\n"
                                                       "    TS Usage Mode 2 Support:         %d\r\n"
#string STR_PCI_EXT_CAP_PHYSICAL_LAYER_32_CONTROL #language en-US "  Physical Layer 32.0 GT/s Control Details:\r\n"
                                                       "    Equalization Bypass Disable:      %d\r\n"
                                                       "    No Equalization Needed Disable:   %d\r\n"
                                                       "    TS Usage Mode Selected:           %d\r\n"
#string STR_PCI_EXT_CAP_PHYSICAL_LAYER_32_STATUS #language en-US "  Physical Layer 32.0 GT/s Status Details:\r\n"
                                                       "    Equalization Complete:            %d\r\n"
                                                       "    Equalization Phase 1 Success:     %d\r\n"
                                                       "    Equalization Phase 2 Success:     %d\r\n"
                                                       "    Equalization Phase 3 Success:     %d\r\n"
                                                       "    Link Equalization Request:        %d\r\n"
                                                       "    Modified TS Received:             %d\r\n"
                                                       "    Received Enhanced Link Control:   %d\r\n"
                                                       "    Transmitter Precoding On:         %d\r\n"
                                                       "    Transmitter Precode Request:      %d\r\n"
                                                       "    No Equalization Needed Received:  %d\r\n"
#string STR_PCI_EXT_CAP_PHYSICAL_LAYER_32_TS_DATA #language en-US "  Physical Layer 32.0 GT/s TS Data:\r\n"
                                                       "    Received Modified TS Data 1:      %08x\r\n"
                                                       "    Received Modified TS Data 2:      %08x\r\n"
                                                       "    Transmit Modified TS Data 1:      %08x\r\n"
                                                       "    Transmit Modified TS Data 2:      %08x\r\n"
#string STR_PCI_EXT_CAP_ALTERNATE_PROTOCOL #language en-US "  Alternate Protocol Capability\r\n"
                                                       "    AlternateProtocolCapability    %08x\r\n"
                                                       "    AlternateProtocolStatus        %08x\r\n"
                                                       "    AlternateProtocolControl       %08x\r\n"
#string STR_PCI_EXT_CAP_DATA_OBJECT_EXCHANGE #language en-US "  Data Object Exchange Capability\r\n"
                                                       "    DataObjectExchangeCapability   %08x\r\n"
                                                       "    DataObjectExchangeControl      %08x\r\n"
                                                       "    DataObjectExchangeStatus       %08x\r\n"
#string STR_PCI_EXT_CAP_DEVICE3        #language en-US "  Device 3 Extended Capability\r\n"
                                                       "    Capabilities:                  %08x\r\n"
                                                       "    Control:                       %08x\r\n"
                                                       "    Status:                        %08x\r\n"
#string STR_PCI_EXT_CAP_DEVICE3_CAPABILITY #language en-US "  Device 3 Capability Details:\r\n"
                                                       "    DMWR Request Routing:           %d\r\n"
                                                       "    14-bit Tag Completer:           %d\r\n"
                                                       "    14-bit Tag Requester:           %d\r\n"
                                                       "    Receiver L0p Support:           %d\r\n"
                                                       "    Port L0p Exit Latency:          %d\r\n"
                                                       "    Retimer L0p Exit:               %d\r\n"
#string STR_PCI_EXT_CAP_DEVICE3_CONTROL #language en-US "  Device 3 Control Details:\r\n"
                                                       "    DMWR Requester Enable:          %d\r\n"
                                                       "    DMWR Egress Blocking:           %d\r\n"
                                                       "    14-bit Tag Requester Enable:    %d\r\n"
                                                       "    L0p Enable:                     %d\r\n"
                                                       "    Target Link Width:              %d\r\n"
#string STR_PCI_EXT_CAP_DEVICE3_STATUS #language en-US "  Device 3 Status Details:\r\n"
                                                       "    Initial Link Width:             %d\r\n"
                                                       "    Segment Captured:               %d\r\n"
                                                       "    Remote L0p Supported:           %d\r\n"
#string STR_PCI_EXT_CAP_INTEGRITY_ENCRYPTION #language en-US "  Integrity and Data Encryption Capability\r\n"
                                                       "    IdeCapabilities                %08x\r\n"
                                                       "    IdeControl                     %08x\r\n"
                                                       "    IdeStatus                      %08x\r\n"
#string STR_PCI_EXT_CAP_PHYSICAL_LAYER_64 #language en-US "  Physical Layer 64.0 GT/s Extended Capability\r\n"
                                                       "    Capabilities:                     %08x\r\n"
                                                       "    Control:                          %08x\r\n"
                                                       "    Status:                           %08x\r\n"
#string STR_PCI_EXT_CAP_PHYSICAL_LAYER_64_STATUS #language en-US "  Physical Layer 64.0 GT/s Status Details:\r\n"
                                                       "    Equalization Complete:            %d\r\n"
                                                       "    Equalization Phase 1 Success:     %d\r\n"
                                                       "    Equalization Phase 2 Success:     %d\r\n"
                                                       "    Equalization Phase 3 Success:     %d\r\n"
                                                       "    Link Equalization Request:        %d\r\n"
                                                       "    Transmitter Precoding On:         %d\r\n"
                                                       "    Transmitter Precode Request:      %d\r\n"
                                                       "    No Equalization Needed Received:  %d\r\n"
#string STR_PCI_EXT_FLIT_LOGGING #language en-US       "  FLIT Logging Capability\r\n"
                                                       "    FlitLoggingCapabilities        %08x\r\n"
                                                       "    FlitLoggingControl             %08x\r\n"
                                                       "    FlitLoggingStatus              %08x\r\n"
                                                       "    FlitMask                       %08x\r\n"
                                                       "    FlitErrorData1                 %08x\r\n"
                                                       "    FlitErrorData2                 %08x\r\n"
                                                       "    FlitErrorData3                 %08x\r\n"

#string STR_DMPSTORE_SAVE              #language en-US "Save variable to file: %H%s%N.\r\n"
#string STR_DMPSTORE_LOAD              #language en-US "Load and set variables from file: %H%s%N.\r\n"
#string STR_DMPSTORE_LOAD_GEN_FAIL     #language en-US "%H%s%N: Failed to set variable %H%s%N: %r.\r\n"
#string STR_DMPSTORE_LOAD_BAD_FILE     #language en-US "%H%s%N: Incorrect file format.\r\n"
#string STR_DMPSTORE_HEADER_LINE       #language en-US "Variable %H%s%N '%H%g%N:%H%s%N' DataSize = 0x%02x\r\n"
#string STR_DMPSTORE_HEADER_LINE2      #language en-US "Variable %H%s%N '%H%s%N:%H%s%N' DataSize = 0x%02x\r\n"
#string STR_DMPSTORE_DELETE_LINE       #language en-US "Delete variable '%H%g%N:%H%s%N': %r\r\n"
#string STR_DMPSTORE_NO_VAR_FOUND      #language en-US "%H%s%N: No matching variables found.\r\n"
#string STR_DMPSTORE_NO_VAR_FOUND_SFO  #language en-US "VariableInfo,\"\",\"\",\"\",\"\",\"\"\r\n"
#string STR_DMPSTORE_NO_VAR_FOUND_GN   #language en-US "%H%s%N: No matching variables found. Guid %g, Name %s\r\n"
#string STR_DMPSTORE_NO_VAR_FOUND_NG_SFO #language en-US "VariableInfo,\"%s\",\"%g\",\"\",\"\",\"\"\r\n"
#string STR_DMPSTORE_NO_VAR_FOUND_N    #language en-US "%H%s%N: No matching variables found. Name %s\r\n"
#string STR_DMPSTORE_NO_VAR_FOUND_N_SFO #language en-US #language en-US "VariableInfo,\"%s\",\"\",\"\",\"\",\"\"\r\n"
#string STR_DMPSTORE_NO_VAR_FOUND_G    #language en-US "%H%s%N: No matching variables found. Guid %g\r\n"
#string STR_DMPSTORE_NO_VAR_FOUND_G_SFO #language en-US "VariableInfo,\"\",\"%g\",\"\",\"\",\"\"\r\n"
#string STR_DMPSTORE_VAR_SFO           #language en-US "VariableInfo,\"%s\",\"%g\",\"0x%x\",\"0x%x\",\"%s\"\r\n"

#string STR_GET_HELP_COMP         #language en-US ""
".TH comp 0 "Compare 2 files"\r\n"
".SH NAME\r\n"
"Compares the contents of two files on a byte-for-byte basis.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"COMP [-b] file1 file2\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  -b    - Displays one screen at a time.\r\n"
"  file1 - Specifies a first file name  (directory name or wildcards not permitted).\r\n"
"  file2 - Specifies a second file name (directory name or wildcards not permitted).\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. This command compares the contents of two files in binary mode.\r\n"
"  2. It displays up to 10 differences between the two files. For each\r\n"
"     difference, up to 32 bytes from the location where the difference starts\r\n"
"     is dumped.\r\n"
"  3. It will exit immediately if the lengths of the compared files are\r\n"
"     different.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To compare two files with the same length but different contents:\r\n"
"    fs0:\> comp bios.inf bios2.inf\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  SHELL_SUCCESS              The function operated as expected.\r\n"
"  SHELL_NOT_EQUAL            The files were not identical.\r\n"
"  SHELL_INVALID_PARAMETER    One of the passed in parameters was incorrectly\r\n"
"                             formatted or its value was out of bounds.\r\n"
"  SHELL_SECURITY_VIOLATION   This function was not performed due to a security\r\n"
"                             violation.\r\n"
"  SHELL_NOT_FOUND            The requested file was not found.\r\n"

#string STR_GET_HELP_SETSIZE      #language en-US ""
".TH setsize 0 "Set file size"\r\n"
".SH NAME\r\n"
"Adjusts the size of a file.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"SETSIZE size file [file...]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  size - Specifies the size of the file after it is adjusted.\r\n"
"  file - Specifies the file or files to be adjusted.\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. Setting the size smaller than the actual data contained in this file will\r\n"
"     truncate its data.\r\n"
"  2. This command adjusts the size of a particular target file.\r\n"
"  3. This command automatically truncates or extends the size of a file based on the passed-in\r\n"
"     parameters. If the file does not exist, it is created.\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  SHELL_SUCCESS             The action was completed as requested.\r\n"
"  SHELL_VOLUME_FULL         The media has insufficient space to complete the\r\n"
"                            request.\r\n"
"  SHELL_INVALID_PARAMETER   One of the passed in parameters was incorrectly\r\n"
"                            formatted or its value was out of bounds.\r\n"

#string STR_GET_HELP_MODE         #language en-US ""
".TH mode 0 "Shows or changes ConOut mode."\r\n"
".SH NAME\r\n"
"Displays or changes the console output device mode.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"MODE [col row]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  col - Specifies the number of columns.\r\n"
"  row - Specifies the number of rows.\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. This command changes the display mode for the console output\r\n"
"     device.\r\n"
"  2. When this command is used without any parameters, it shows the list of\r\n"
"     modes that the standard output device currently supports.\r\n"
"  3. When used with the row and col parameter, this command\r\n"
"     changes the number of rows and columns on the standard output device.\r\n"
"  4. The current selected mode is indicated by a '*'. \r\n"
"  5. The display is cleared every time this command is used to change the\r\n"
"     currently selected display mode.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To display all available modes on standard output:\r\n"
"    Shell> mode\r\n"
" \r\n"
"  * To change the current mode setting:\r\n"
"    Shell> mode 80 50\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  SHELL_SUCCESS              The action was completed as requested.\r\n"
"  SHELL_SECURITY_VIOLATION   This function was not performed due to a security\r\n"
"                             violation.\r\n"
"  SHELL_INVALID_PARAMETER    One of the passed-in parameters was incorrectly\r\n"
"                             formatted or its value was out of bounds.\r\n"

#string STR_GET_HELP_MEMMAP       #language en-US ""
".TH memmap 0 "Displays the memory map."\r\n"
".SH NAME\r\n"
"Displays the memory map maintained by the UEFI environment.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"MEMMAP [-b] [-sfo]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  -b   - Displays one screen at a time\r\n"
"  -sfo - Displays information as described in Standard-Format Output.\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. The UEFI environment keeps track of all the physical memory in the system\r\n"
"     and how it is currently being used.\r\n"
"  2. Total Memory is the physical memory size excluding Reserved, Unusable,\r\n"
"     MemoryMappedIO, and MemoryMappedIOPortSpace memory types.\r\n"
"  3. Refer to the UEFI specification for memory type definitions.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To display the system memory map:\r\n"
"    Shell> memmap\r\n"
" \r\n"

#string STR_GET_HELP_EFICOMPRESS  #language en-US ""
".TH eficompress 0 "compresses a file."\r\n"
".SH NAME\r\n"
"Compresses a file using UEFI Compression Algorithm.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"EFICOMPRESS infile outfile\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  infile  - Specifies the file name of the uncompressed input file.\r\n"
"  outfile - Specifies the file name of the compressed output file.\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. This command compresses a file using UEFI Compression Algorithm\r\n"
"     and writes the compressed form out to a new file.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To compress a file named 'uncompressed' to a file named 'compressed':\r\n"
"    fs0:\> eficompress uncompressed compressed\r\n"

#string STR_GET_HELP_EFIDCOMPRESS #language en-US ""
".TH efidecompress 0 "Decompresses a file."\r\n"
".SH NAME\r\n"
"Decompresses a file using UEFI Decompression Algorithm.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"EFIDECOMPRESS infile outfile\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  infile  - Specifies the file name of the compressed input file.\r\n"
"  outfile - Specifies the file name of the decompressed output file.\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. This decompresses a file using UEFI Decompression\r\n"
"     Algorithm and writes the decompressed form out to a new file.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To decompress a file named 'compressed' to a file named 'uncompressed':\r\n"
"    fs0:\> efidecompress compressed uncompressed\r\n"

#string STR_GET_HELP_DMEM         #language en-US ""
".TH dmem 0 "Displays memory."\r\n"
".SH NAME\r\n"
"Displays the contents of system or device memory.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"DMEM [-b] [address] [size] [-MMIO]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  -b      - Displays one screen at a time.\r\n"
"  -MMIO   - Forces address cycles to the PCI bus.\r\n"
"  -verbose - Displays contents of certain EFI System Tables.\r\n"
"  address - Specifies a starting address in hexadecimal format.\r\n"
"  size    - Specifies the number of bytes to display in hexadecimal format.\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. This command displays the contents of system memory or device memory.\r\n"
"  2. Enter address and size in hexadecimal format.\r\n"
"  3. If address is not specified, the contents of the UEFI System Table\r\n"
"     are displayed. Otherwise, memory starting at the specified address is displayed.\r\n"
"  4. Size specifies the number of bytes to display. If size is not specified,\r\n"
"     512 bytes are displayed.\r\n"
"  5. If MMIO is not specified, main system memory is displayed. Otherwise,\r\n"
"     device memory is displayed through the use of the\r\n"
"     EFI_PCI_ROOT_BRIDGE_IO_PROTOCOL.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To display the UEFI system table pointer entries:\r\n"
"    fs0:\> dmem\r\n"
" \r\n"
"  * To display memory contents from 1af3088 with size of 16 bytes:\r\n"
"    Shell> dmem 1af3088 16\r\n"
" \r\n"
"  * To display memory mapped IO contents from 1af3088 with a size of 16 bytes:\r\n"
"    Shell> dmem 1af3088 16 -MMIO\r\n"

#string STR_GET_HELP_MM           #language en-US ""
".TH mm 0 "Displays or modifies address space memory."\r\n"
".SH NAME\r\n"
"Displays or modifies MEM/MMIO/IO/PCI/PCIE address space.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"MM Address [Value] [-w 1|2|4|8] [-MEM | -MMIO | -IO | -PCI | -PCIE] [-n]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  Address - Starting address in hexadecimal format.\r\n"
"  Value   - The value to write in hexadecimal format.\r\n"
"  -MEM    - Memory Address type\r\n"
"  -MMIO   - Memory Mapped IO Address type\r\n"
"  -IO     - IO Address type\r\n"
"  -PCI    - PCI Configuration Space Address type:\r\n"
"            Address format: ssssbbddffrr\r\n"
"              ssss - Segment\r\n"
"              bb   - Bus\r\n"
"              dd   - Device\r\n"
"              ff   - Function\r\n"
"              rr   - Register\r\n"
"  -PCIE   - PCIE Configuration Space Address type:\r\n"
"            Address format: ssssbbddffrrr\r\n"
"              ssss - Segment\r\n"
"              bb   - Bus\r\n"
"              dd   - Device\r\n"
"              ff   - Function\r\n"
"              rrr  - Register\r\n"
"  -w      - Unit size accessed in bytes:\r\n"
"              1    - 1 byte\r\n"
"              2    - 2 bytes\r\n"
"              4    - 4 bytes\r\n"
"              8    - 8 bytes\r\n"
"  -n      - Non-interactive mode\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1.  If the address type parameter is not specified, address type defaults\r\n"
"      to the 'MEM' type.\r\n"
"  2.  If the 'Value' parameter is specified, the '-n' option is used and\r\n"
"      the command writes the value to the\r\n"
"      specified address in non-interactive mode. If the 'Value' parameter is\r\n"
"      not specified, only the current contents in the address are displayed.\r\n"
"  3.  If the '-w' option is not specified, unit size defaults to 1 byte.\r\n"
"  4.  If the PCI address type is specified, the 'Address' parameter must\r\n"
"      follow the PCI Configuration Space Address format above. The 'PCI'\r\n"
"      command can be used to determine the address for a specified device.\r\n"
"      It is listed in the PCI configuration space dump information in the\r\n"
"      following format: "[EFI ssbbddffxx]".\r\n"
"  5.  If the PCIE address type is specified, the 'Address' parameter must\r\n"
"      follow the PCIE Configuration Space Address format above.\r\n"
"  6.  In interactive mode, type a hex value to modify, 'q' or '.' to exit.\r\n"
"      If the '-n' option is specified, it runs in non-interactive mode,\r\n"
"      which supports batch file operation without user intervention.\r\n"
"  7.  Not all PCI configuration register locations are writable.\r\n"
"  8.  MM will only write the specified value. Read-modify-write operations\r\n"
"      are not supported.\r\n"
"  9.  The 'Address' parameter must be aligned on a boundary of the\r\n"
"      specified width.\r\n"
"  10. Not all addresses are safe to access. Access to any improper address\r\n"
"      can bring unexpected results.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To display or modify memory:\r\n"
"    Address 0x1b07288, default width=1 byte:\r\n"
"    fs0:\> mm 1b07288\r\n"
"    MEM  0x0000000001B07288 : 0x6D >\r\n"
"    MEM  0x0000000001B07289 : 0x6D >\r\n"
"    MEM  0x0000000001B0728A : 0x61 > 80\r\n"
"    MEM  0x0000000001B0728B : 0x70 > q\r\n"
"    fs0:\> mm 1b07288\r\n"
"    MEM  0x0000000001B07288 : 0x6D >\r\n"
"    MEM  0x0000000001B07289 : 0x6D >\r\n"
"    MEM  0x0000000001B0728A : 0x80 >        *Modified\r\n"
"    MEM  0x0000000001B0728B : 0x70 > q\r\n"
" \r\n"
"  * To modify memory: Address 0x1b07288, width = 2 bytes:\r\n"
"    Shell> mm 1b07288 -w 2\r\n"
"    MEM  0x0000000001B07288 : 0x6D6D >\r\n"
"    MEM  0x0000000001B0728A : 0x7061 > 55aa\r\n"
"    MEM  0x0000000001B0728C : 0x358C > q\r\n"
"    Shell> mm 1b07288 -w 2\r\n"
"    MEM  0x0000000001B07288 : 0x6D6D >\r\n"
"    MEM  0x0000000001B0728A : 0x55AA >      *Modified\r\n"
"    MEM  0x0000000001B0728C : 0x358C > q\r\n"
" \r\n"
"  * To display IO space:   Address 80h, width = 4 bytes:\r\n"
"    Shell> mm 80 -w 4 -IO\r\n"
"    IO  0x0000000000000080 : 0x000000FE >\r\n"
"    IO  0x0000000000000084 : 0x00FF5E6D > q\r\n"
" \r\n"
"  * To modify IO space using non-interactive mode:\r\n"
"    Shell> mm 80 52 -w 1 -IO\r\n"
"    Shell> mm 80 -w 1 -IO\r\n"
"    IO  0x0000000000000080 : 0x52 > FE      *Modified\r\n"
"    IO  0x0000000000000081 : 0xFF >\r\n"
"    IO  0x0000000000000082 : 0x00 >\r\n"
"    IO  0x0000000000000083 : 0x00 >\r\n"
"    IO  0x0000000000000084 : 0x6D >\r\n"
"    IO  0x0000000000000085 : 0x5E >\r\n"
"    IO  0x0000000000000086 : 0xFF >\r\n"
"    IO  0x0000000000000087 : 0x00 > q\r\n"
" \r\n"
"  * To display PCI configuration space, ss=0000, bb=00, dd=00, ff=00, rr=00:\r\n"
"    Shell> mm 000000000000 -PCI\r\n"
"    PCI  0x0000000000000000 : 0x86 >\r\n"
"    PCI  0x0000000000000001 : 0x80 >\r\n"
"    PCI  0x0000000000000002 : 0x30 >\r\n"
"    PCI  0x0000000000000003 : 0x11 >\r\n"
"    PCI  0x0000000000000004 : 0x06 >\r\n"
"    PCI  0x0000000000000005 : 0x00 > q\r\n"
"    These contents can also be displayed by 'PCI 00 00 00'.\r\n"
" \r\n"
"  * To display PCIE configuration space, ss=0000, bb=06, dd=00, ff=00, rrr=000:\r\n"
"    Shell> mm 0000060000000 -PCIE\r\n"
"    PCIE  0x0000000060000000 : 0xAB >\r\n"
"    PCIE  0x0000000060000001 : 0x11 >\r\n"
"    PCIE  0x0000000060000002 : 0x61 >\r\n"
"    PCIE  0x0000000060000003 : 0x43 >\r\n"
" \r\n"

#string STR_GET_HELP_LOAD_PCI_ROM #language en-US ""
".TH loadpcirom 0 "Loads a PCI option ROM file."\r\n"
".SH NAME\r\n"
"Loads a PCI Option ROM.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"LoadPciRom [-nc] romfile [romfile...]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  -nc     - Loads the ROM image(s) but does not connect drivers.\r\n"
"  romfile - Specifies the PCI option ROM image file (wildcards are permitted).\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. This command loads PCI option ROM images into memory for\r\n"
"     execution.\r\n"
"  2. The file can contain legacy images and multiple PE32 images, in which case\r\n"
"     all PE32 images are loaded.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To load a rom file 'rom.bin':\r\n"
"    fs0:\> LoadPciRom rom.bin\r\n"
" \r\n"
"  * To load '*.bin' files without connecting drivers:\r\n"
"    fs0:\> LoadPciRom -nc *.bin\r\n"

#string STR_GET_HELP_SETVAR       #language en-US ""
".TH setvar 0 "Displays or modifies a UEFI variable."\r\n"
".SH NAME\r\n"
"Displays or modifies a UEFI variable.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"SETVAR variable-name [-guid guid][-bs][-rt][-nv] [=data]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  variable-name - Specifies the name of the UEFI variable to modify or display.\r\n"
"  -guid         - Specifies the GUID of the UEFI variable to modify or display.\r\n"
"                  If not present, GUID EFI_GLOBAL_VARIABLE is assumed.\r\n"
"  -bs           - Indicates that the variable is a boot variable. Applies to a new variable;\r\n"
"                  otherwise, it is ignored. \r\n"
"  -rt           - Indicates that the variable is a runtime variable. Applies to a new variable;\r\n"
"                  otherwise, it is ignored. \r\n"
"  -nv           - Indicates that the variable is non-volatile. If not present,\r\n"
"                  then the variable is assumed to be volatile. Applies to a new variable;\r\n"
"                  otherwise, it is ignored. \r\n"
"  =data         - Specifies there is new data for the variable. If there is nothing after the '='\r\n"
"                  then the variable is deleted. If '=' is not present, then the\r\n"
"                  current value of the variable is dumped as hex bytes.\r\n"
"                  The data can consist of zero or more of the following:\r\n"
"                    xx[xx]         - Hexadecimal bytes\r\n"
"                    ^"ascii-string^" - ASCII-string with no null-terminator\r\n"
"                    L^"UCS2-string^" - UCS-2 encoded string with no\r\n"
"                                     null-terminator\r\n"
"                    --device       - Device path text format\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. This command changes the UEFI variable specified by name and GUID.\r\n"
"  2. If = is specified, but data is not, the variable is deleted, if it exists.\r\n"
"  3. If = is not specified, then the current variable contents are displayed.\r\n"
"  4. If =data is specified, then the variable's value is changed to the value\r\n"
"     specified by data.\r\n"
"  5. -bs, -rt and -nv are only useful if the variable does not exist.\r\n"
"  6. If the variable already exists, the attributes cannot be changed, and the"
"     flags will be ignored.\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  SHELL_SUCCESS             The shell has stored the variable and its data with\r\n"
"                            the defined attributes.\r\n"
"  SHELL_INVALID_PARAMETER   Incorrect attributes were used.\r\n"
"  SHELL_OUT_OF_RESOURCES    Insufficient resources were available for storing\r\n"
"                            the variable and its data.\r\n"
"  SHELL_DEVICE_ERROR The    Variable could not be saved due to a hardware\r\n"
"                            error.\r\n"
"  SHELL_WRITE_PROTECTED     The variable in question is read-only.\r\n"
"  SHELL_WRITE_PROTECTED     The variable in question cannot be deleted.\r\n"
"  SHELL_NOT_FOUND           The variable could not be found.\r\n"

#string STR_GET_HELP_SERMODE      #language en-US ""
".TH sermode 0 "configure serial port"\r\n"
".SH NAME\r\n"
"Sets serial port attributes.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"SERMODE [handle [baudrate parity databits stopbits]]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  handle   - Specifies a device handle for a serial port in hexadecimal format.\r\n"
"  baudrate - Specifies a baud rate for specified serial port.\r\n"
"  parity   - Sets parity bit settings for specified serial port. Valid values\r\n"
"             are:\r\n"
"               d   - Default parity\r\n"
"               n   - No parity\r\n"
"               e   - Even parity\r\n"
"               o   - Odd parity\r\n"
"               m   - Mark parity\r\n"
"               s   - Space parity\r\n"
"  databits - Sets the data bits for the specified serial port.\r\n"
"  stopbits - Sets the stop bits for the specified serial port.\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. The 'handle' parameter is the device handle of the desired serial port.\r\n"
"     The 'DH' command can be used to retrieve this information.\r\n"
"  2. The 'stopbits' parameter supports the following settings:\r\n"
"       0  (0 stop bits - default setting)\r\n"
"       1  (1 stop bit)\r\n"
"       2  (2 stop bits)\r\n"
"       15 (1.5 stop bits)\r\n"
"     All other settings are invalid.\r\n"
"  3. The 'baudrate' parameter supports the following settings:\r\n"
"     50, 75, 110, 150, 300, 600, 1200, 1800, 2000, 2400, 3600, 4800,\r\n"
"     7200, 9600(default), 19200, 38400, 57600, 115200, 230400, 460800\r\n"
"     All other values will be converted to the next highest setting.\r\n"
"  4. The 'databits' parameter supports the following settings:\r\n"
"       4\r\n"
"       7\r\n"
"       8 (default)\r\n"
"     All other settings are invalid.\r\n"
"  5. Parity attributes are mutually exclusive.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To display the settings for all serial port devices:\r\n"
"    Shell> sermode\r\n"
" \r\n"
"  * To display the settings for the serial port device whose handle is 0x6B:\r\n"
"    Shell> sermode 6B\r\n"
" \r\n"
"  * To configure the serial port settings for handle 0x6B to 9600bps, even\r\n"
"    parity, 8 data bits, and 1 stop bit:\r\n"
"    Shell> sermode 6B 9600 e 8 1\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  SHELL_SUCCESS             The new attributes were set on the serial device.\r\n"
"  SHELL_INVALID_PARAMETER   One or more of the attributes has an unsupported\r\n"
"                            value.\r\n"
"  SHELL_DEVICE_ERROR        The serial device is not functioning correctly.\r\n"

#string STR_GET_HELP_PCI          #language en-US ""
".TH pci 0 "Displays PCI device information."\r\n"
".SH NAME\r\n"
"Displays PCI device list or PCI function configuration space and PCIe extended\r\n"
"configuration space.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"PCI [Bus Dev [Func] [-s Seg] [-i [-ec ID]]]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  -s   - Specifies optional segment number (hexadecimal number).\r\n"
"  -i   - Displays interpreted information.\r\n"
"  -ec  - Displays detailed interpretation of specified PCIe extended capability\r\n"
"         ID (hexadecimal number).\r\n"
"  Bus  - Specifies a bus number (hexadecimal number).\r\n"
"  Dev  - Specifies a device number (hexadecimal number).\r\n"
"  Func - Specifies a function number (hexadecimal number).\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. This command displays a list of all the PCI devices found in the system. It\r\n"
"     also displays the configuration space of a PCI device according to the\r\n"
"     specified bus (Bus), device (Dev), and function (Func) addresses. If the\r\n"
"     function address is not specified, it defaults to 0.\r\n"
"  2. The -i option displays verbose information for the specified PCI\r\n"
"     device. The PCI configuration space for the device is displayed with\r\n"
"     a detailed interpretation.\r\n"
"  3. If no parameters are specified, all PCI devices are listed.\r\n"
"  4. If the 'Bus' and 'Dev' parameters are specified but the 'Func' or\r\n"
"     'Seg' parameters are not, Function or Seg are set to the default value of 0.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To display all PCI devices in the system:\r\n"
"    Shell> pci\r\n"
" \r\n"
"  * To display the configuration space of Bus 0, Device 0, Function 0:\r\n"
"    Shell> pci 00 00 00 -i\r\n"
" \r\n"
"  * To display configuration space of Segment 0, Bus 0, Device 0, Function 0:\r\n"
"    Shell> pci 00 00 00 -s 0\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  SHELL_SUCCESS        Data was displayed as requested.\r\n"
"  SHELL_DEVICE_ERROR   The specified device parameters did not match a physical\r\n"
"                       device in the system.\r\n"

#string STR_GET_HELP_SMBIOSVIEW   #language en-US ""
".TH smbiosview 0 "Displays SMBIOS information."\r\n"
".SH NAME\r\n"
"Displays SMBIOS information.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"SMBIOSVIEW [-t SmbiosType]|[-h SmbiosHandle]|[-s]|[-a]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  -t            - Displays all structures of SmbiosType.\r\n"
"  -h            - Displays structure of SmbiosHandle.\r\n"
"  -s            - Displays a statistics table.\r\n"
"  -a            - Displays all information.\r\n"
"  SmbiosType    - Specifies a SMBIOS structure type.\r\n"
"  SmbiosHandle  - Specifies a SMBIOS structure unique 16-bit handle.\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. The SmbiosType parameter supports the following types:\n"
"       0  - BIOS Information\r\n"
"       1  - System Information\r\n"
"       2  - Baseboard Information\r\n"
"       3  - System Enclosure\r\n"
"       4  - Processor Information\r\n"
"       5  - Memory Controller Information\r\n"
"       6  - Memory Module Information\r\n"
"       7  - Cache Information\r\n"
"       8  - Port Connector Information\r\n"
"       9  - System Slots\r\n"
"       10 - On Board Devices Information\r\n"
"       11 - OEM Strings\r\n"
"       12 - System Configuration Options\r\n"
"       13 - BIOS Language Information\r\n"
"       14 - Group Associations\r\n"
"       15 - System Event Log\r\n"
"       16 - Physical Memory Array\r\n"
"       17 - Memory Device\r\n"
"       18 - 32-bit Memory Error Information\r\n"
"       19 - Memory Array Mapped Address\r\n"
"       20 - Memory Device Mapped Address\r\n"
"       21 - Built-in Pointing Device\r\n"
"       22 - Portable Battery\r\n"
"       23 - System Reset\r\n"
"       24 - Hardware Security\r\n"
"       25 - System Power Controls\r\n"
"       26 - Voltage Probe\r\n"
"       27 - Cooling Device\r\n"
"       28 - Temperature Probe\r\n"
"       29 - Electrical Current Probe\r\n"
"       30 - Out-Of-Band Remote Access\r\n"
"       31 - Boot Integrity Services (BIS) Entry Point\r\n"
"       32 - System Boot Information\r\n"
"       33 - 64-Bit Memory Error Information\r\n"
"       34 - Management Device\r\n"
"       35 - Management Device Component\r\n"
"       36 - Management Device Threshold Data\r\n"
"       37 - Memory Channel\r\n"
"       38 - IPMI Device Information\r\n"
"       39 - System Power Supply\r\n"
"       40 - Additional Information\r\n"
"       41 - Onboard Devices Extended Information\r\n"
"       42 - Management Controller Host Interface\r\n"
"       43 - TPM Device\r\n"
"       44 - Processor Additional Information\r\n"
"  2. Enter the SmbiosHandle parameter in hexadecimal format.\r\n"
"     Do not use the '0x' prefix format for hexadecimal values.\r\n"
"  3. Internal commands:\r\n"
"       :q --------  quit smbiosview\r\n"
"       :0 --------  Change smbiosview display NONE info\r\n"
"       :1 --------  Change smbiosview display OUTLINE info\r\n"
"       :2 --------  Change smbiosview display NORMAL info\r\n"
"       :3 --------  Change smbiosview display DETAIL info\r\n"
"       /? --------  Show help\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  SHELL_SUCCESS        Data was displayed as requested.\r\n"
"  SHELL_DEVICE_ERROR   The requested structure was not found.\r\n"

#string STR_GET_HELP_DMPSTORE     #language en-US ""
".TH dmpstore 0 "Manages all UEFI variables."\r\n"
".SH NAME\r\n"
"Manages all UEFI variables.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"DMPSTORE [-b] [-d] [-all | ([variable] [-guid guid])] [-sfo]\r\n"
"DMPSTORE [-all | ([variable] [-guid guid])] [-s file]\r\n"
"DMPSTORE [-all | ([variable] [-guid guid])] [-l file]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  -b       - Displays one screen at a time.\r\n"
"  -guid    - Specifies the GUID of the variables to be displayed in\r\n"
"             standard text format. If not specified and -all is not\r\n"
"             specified, the EFI_GLOBAL_VARIABLE GUID is assumed.\r\n"
"  -sfo     - Displays information as described in Standard-Format Output.\r\n"
"  -all     - Dumps all variables, including those\r\n"
"             with a different GUID than EFI_GLOBAL_VARIABLE.\r\n"
"  -d       - Delete variables.\r\n"
"  -s       - Saves variables to a file.\r\n"
"  -l       - Loads and sets variables from a file.\r\n"
"  variable - Specifies a variable name. This can be a literal name or\r\n"
"             a pattern as specified in the MetaiMatch() function of the\r\n"
"             EFI_UNICODE_COLLATION2_PROCOOL.\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. This command manages the UEFI variables. The variables\r\n"
"     displayed or deleted depend on the command line options, as specified in\r\n"
"     the following table:\r\n"
"     Variable GUID  -all  Description\r\n"
"     ---      ---   ---   All variables with the GUID EFI_GLOBAL_VARIABLE will\r\n"
"                          be operated on.\r\n"
"     ---      ---    X    All variables (regardless of GUID or name) will be\r\n"
"                          operated on.\r\n"
"     ---       X    ---   All variables with the specified GUID will be\r\n"
"                          operated on.\r\n"
"      X       ---   ---   The variable with the GUID EFI_GLOBAL_VARIABLE and\r\n"
"                          the name Variable will be operated on.\r\n"
"      X        X    ---   The variable with the specified GUID and name\r\n"
"                          Variable will be operated on.\r\n"
"  2. The variable value is printed as a hexadecimal dump.\r\n"
"  3. Option -d is used to delete variables. Option -s and -l are used to save\r\n"
"     and load variables to and from a file. The variable name can be specified\r\n"
"     when using these flags so that the operation only takes effect on\r\n"
"     that variable.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To dump all variables with the GUID EFI_GLOBAL_VARIABLE:\r\n"
"    Shell> dmpstore\r\n"
" \r\n"
"  * To dump all variables (regardless of GUID or name):\r\n"
"    Shell> dmpstore -all\r\n"
" \r\n"
"  * To dump the 'path' variable with the GUID '158DEF5A-F656-419C-B027-\r\n"
"    7A3192C079D2':\r\n"
"    Shell> dmpstore path -guid 158DEF5A-F656-419C-B027-7A3192C079D2\r\n"
" \r\n"
"  * To save all variables (regardless of GUID or name) to a file 'VarDump.txt':\r\n"
"    fs0:\> dmpstore -all -s VarDump.txt\r\n"
" \r\n"
"  * To delete the 'BootOrder' variable with the GUID EFI_GLOBAL_VARIABLE:\r\n"
"    Shell> dmpstore -d BootOrder\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  SHELL_SUCCESS             The action was completed as requested.\r\n"
"  SHELL_INVALID_PARAMETER   One of the passed-in parameters was incorrectly\r\n"
"                            formatted or its value was out of bounds.\r\n"

#string STR_GET_HELP_DBLK         #language en-US ""
".TH dblk 0 "Displays one or more blocks from a block device."\r\n"
".SH NAME\r\n"
"Displays one or more blocks from a block device.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"DBLK device [lba] [blocks] [-b]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  -b     - Displays one screen at a time.\r\n"
"  device - Blocks the device name.\r\n"
"  lba    - Specifies the index of the first block to be displayed (a hexadecimal number).\r\n"
"           The default is 0.\r\n"
"  blocks - Specifies the number of blocks to display (a hexadecimal number). The default\r\n"
"           is 1. If larger than 0x10, then only 0x10 are displayed.\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. This command displays the contents of one or more blocks from a block\r\n"
"     device. Enter a hexidecimal value for the lba and blocks variables.  If lba is not\r\n"
"     specified, block #0 is assumed. If blocks is not specified, on1y one\r\n"
"     block is displayed. The maximum number of blocks that can be\r\n"
"     displayed at one time is 0x10.\r\n"
"  2. If an MBR is found on the block, the partition information is displayed\r\n"
"     after all the block contents are displayed.\r\n"
"  3. If the block is a FAT partition, some FAT parameters are displayed\r\n"
"     (label, systemid, oemid, sectorsize, clustersize, media, and so forth) after all the\r\n"
"     blocks are displayed.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To display one block of blk0, beginning from block 0:\r\n"
"    Shell> dblk blk0\r\n"
" \r\n"
"  * To display one block of fs0, beginning from block 0x2:\r\n"
"    Shell> dblk fs0 2\r\n"
" \r\n"
"  * To display 0x5 blocks of fs0, beginning from block 0x12:\r\n"
"    Shell> dblk fs0 12 5\r\n"
" \r\n"
"  * To display 0x10 blocks of fs0, beginning from block 0x12:\r\n"
"    Shell> dblk fs0 12 10\r\n"
" \r\n"
"  * To attempt to display more than 0x10 blocks, resulting in only 0x10\r\n"
"    blocks being displayed:\r\n"
"    Shell> dblk fs0 12 20\r\n"
" \r\n"
"  * To display one block of blk2, beginning from the first block (blk0):\r\n"
"    fs1:\tmps1> dblk blk2 0 1\r\n"
" \r\n"

#string STR_GET_HELP_EDIT         #language en-US ""
".TH edit 0 "Provides a full screen text editor for ASCII or UCS-2 files."\r\n"
".SH NAME\r\n"
"Provides a full screen text editor for ASCII or UCS-2 files.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"EDIT [file]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  file - Specifies the name of file to be edited. If none is specified, an empty file\r\n"
"         is created with a default file name.\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. This command enables full screen file editing.\r\n"
"  2. The editor supports both UCS-2 and ASCII file types.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To edit the 'shell.log' file:\r\n"
"    fs0:\> edit shell.log\r\n"

#string STR_GET_HELP_HEXEDIT      #language en-US ""
".TH hexedit 0 "Provides a full screen hex editor for files, block devices, or memory."\r\n"
".SH NAME\r\n"
"Provides a full screen hex editor for files, block devices, or memory.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"HEXEDIT [[-f] filename| [-d diskname offset size] | [-m address size]]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  -f - Specifies the name of the file to edit.\r\n"
"  -d - Specifies the disk block to edit:\r\n"
"         DiskName - Name of the disk to edit (for example fs0)\r\n"
"         Offset   - Starting block number (beginning from 0)\r\n"
"         Size     - Number of blocks to edit\r\n"
"  -m - Specifies the memory region to edit:\r\n"
"         Address  - Starting 32-bit memory address (beginning from 0)\r\n"
"         Size     - Size of memory region to edit in bytes\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. This command enables you to edit a file, block device, or memory region.\r\n"
"  2. The region being edited is displayed as hexadecimal bytes. The\r\n"
"     contents can be modified and saved.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To edit a file in hex mode:\r\n"
"    fs0:\> hexedit test.bin\r\n"
" \r\n"
"  * To edit block device fs0 starting at block 0 with size of 2 blocks:\r\n"
"    fs0:\> hexedit -d fs0 0 2\r\n"
" \r\n"
"  * To edit memory region starting at address 0x00000000 with size of 2 bytes:\r\n"
"    fs0:\> hexedit -m 0 2\r\n"

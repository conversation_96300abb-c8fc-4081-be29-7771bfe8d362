##  @file
#   Library for microcode verification and load.
#
#  Copyright (c) 2021, Intel Corporation. All rights reserved.<BR>
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
#
##

[Defines]
  INF_VERSION                    = 0x00010006
  BASE_NAME                      = MicrocodeLib
  FILE_GUID                      = EB8C72BC-8A48-4F80-996B-E52F68416D57
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = MicrocodeLib

#
#  VALID_ARCHITECTURES           = IA32 X64 EBC
#

[Sources.common]
  MicrocodeLib.c

[Packages]
  MdePkg/MdePkg.dec
  UefiCpuPkg/UefiCpuPkg.dec

[LibraryClasses]
  BaseLib
  DebugLib

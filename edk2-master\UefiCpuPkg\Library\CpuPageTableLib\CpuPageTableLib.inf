## @file
#  This library implements CpuPageTableLib that are generic for IA32 family CPU.
#
#  Copyright (c) 2022, Intel Corporation. All rights reserved.<BR>
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = CpuPageTableLib
  FILE_GUID                      = 524ed6a1-f661-451b-929b-b54d755c914a
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = CpuPageTableLib

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources]
  CpuPageTableMap.c
  CpuPageTableParse.c
  CpuPageTable.h

[Packages]
  MdePkg/MdePkg.dec
  UefiCpuPkg/UefiCpuPkg.dec

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  DebugLib

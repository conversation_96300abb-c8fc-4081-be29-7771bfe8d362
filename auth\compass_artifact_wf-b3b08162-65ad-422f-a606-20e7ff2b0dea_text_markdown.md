# Creating a UEFI Authentication Wrapper Program

Building a UEFI authentication wrapper that embeds a boot.efi binary and implements dual verification requires combining multiple sophisticated UEFI programming techniques. This comprehensive guide provides working implementations and security best practices for creating such a system.

## Architecture overview

The Auth.efi wrapper implements a **three-stage authentication process**: first verifying a boot.key file from the filesystem, then checking USB hardware identifiers, and finally extracting and executing the embedded boot.efi binary upon successful dual authentication. The wrapper uses UEFI boot services for file access, USB device enumeration, and in-memory binary loading.

## Binary embedding implementation

### EDK2 approach using FDF files

The most robust method for embedding binaries in EDK2 uses Flash Description Format files to include the boot.efi during compilation:

```fdf
# Platform.fdf
[FV.FVMAIN_COMPACT]
FILE RAW = 12345678-ABCD-EFGH-IJKL-123456789ABC {
    Align = 4K
    SECTION RAW = Payloads/boot.efi
}
```

Access the embedded binary at runtime using the Firmware Volume Protocol:

```c
EFI_STATUS GetEmbeddedBootEfi(
    OUT VOID  **Buffer,
    OUT UINTN *BufferSize
) {
    EFI_STATUS                       Status;
    EFI_FIRMWARE_VOLUME2_PROTOCOL    *Fv2;
    EFI_GUID                         FileGuid = {0x12345678, 0xABCD, 0xEFGH, 
                                                {0xIJ, 0xKL, 0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC}};
    UINT32                           AuthenticationStatus;
    
    Status = gBS->LocateProtocol(&gEfiFirmwareVolume2ProtocolGuid, NULL, (VOID**)&Fv2);
    if (EFI_ERROR(Status)) return Status;
    
    return Fv2->ReadFile(Fv2, &FileGuid, Buffer, BufferSize, &FileType, &Attributes, &AuthenticationStatus);
}
```

### GNU-EFI objcopy method

For GNU-EFI environments, use objcopy to convert the binary to an object file:

```makefile
# Convert boot.efi to object file
boot_efi_data.o: boot.efi
	objcopy -I binary -O elf64-x86-64 -B i386:x86-64 \
		--rename-section .data=.rodata,alloc,load,readonly,data,contents \
		--redefine-sym _binary_boot_efi_start=boot_efi_start \
		--redefine-sym _binary_boot_efi_end=boot_efi_end \
		--redefine-sym _binary_boot_efi_size=boot_efi_size \
		boot.efi boot_efi_data.o

# Link with main application
auth.efi: main.o boot_efi_data.o
	$(LD) $(LDFLAGS) $(EFI_CRT_OBJS) $^ -o auth.so -lefi -lgnuefi
	$(OBJCOPY) -j .text -j .sdata -j .data -j .rodata \
		-j .dynamic -j .dynsym -j .rel -j .rela -j .reloc \
		--target=efi-app-x86_64 --subsystem=10 auth.so auth.efi
```

Access in C code:

```c
extern char boot_efi_start[];
extern char boot_efi_end[];
extern char boot_efi_size[];

#define BOOT_EFI_SIZE ((UINTN)boot_efi_size)
#define BOOT_EFI_DATA ((VOID*)boot_efi_start)
```

## USB hardware identifier access

### Device enumeration and identification

Use the EFI_USB_IO_PROTOCOL to enumerate USB devices and read hardware identifiers:

```c
EFI_STATUS EnumerateUSBDevices(
    OUT USB_DEVICE_INFO **DeviceList,
    OUT UINTN           *DeviceCount
) {
    EFI_STATUS    Status;
    UINTN         HandleCount;
    EFI_HANDLE    *HandleBuffer;
    
    // Find all USB device handles
    Status = gBS->LocateHandleBuffer(
        ByProtocol,
        &gEfiUsbIoProtocolGuid,
        NULL,
        &HandleCount,
        &HandleBuffer
    );
    
    if (EFI_ERROR(Status)) return Status;
    
    *DeviceList = AllocatePool(HandleCount * sizeof(USB_DEVICE_INFO));
    *DeviceCount = 0;
    
    for (UINTN i = 0; i < HandleCount; i++) {
        EFI_USB_IO_PROTOCOL *UsbIo;
        Status = gBS->HandleProtocol(HandleBuffer[i], &gEfiUsbIoProtocolGuid, (VOID**)&UsbIo);
        
        if (!EFI_ERROR(Status)) {
            ReadUSBDeviceInfo(UsbIo, &(*DeviceList)[(*DeviceCount)++]);
        }
    }
    
    gBS->FreePool(HandleBuffer);
    return EFI_SUCCESS;
}
```

### Hardware ID extraction

Extract vendor ID, product ID, and serial number from USB devices:

```c
VOID ReadUSBDeviceInfo(
    IN  EFI_USB_IO_PROTOCOL *UsbDevIo,
    OUT USB_DEVICE_INFO     *DeviceInfo
) {
    EFI_USB_DEVICE_DESCRIPTOR UsbDevDescriptor;
    CHAR16                   *SerialNumber = NULL;
    UINT16                   *LangIDTable = NULL;
    UINT16                   LangIDSize = 0;
    
    // Get device descriptor with Vendor/Product IDs
    UsbDevIo->UsbGetDeviceDescriptor(UsbDevIo, &UsbDevDescriptor);
    
    DeviceInfo->VendorId = UsbDevDescriptor.IdVendor;
    DeviceInfo->ProductId = UsbDevDescriptor.IdProduct;
    DeviceInfo->DeviceClass = UsbDevDescriptor.DeviceClass;
    
    // Get serial number if available
    if (UsbDevDescriptor.StrSerialNumber != 0) {
        UsbDevIo->UsbGetSupportedLanguages(UsbDevIo, &LangIDTable, &LangIDSize);
        
        if (LangIDSize > 0) {
            UsbDevIo->UsbGetStringDescriptor(UsbDevIo, LangIDTable[0], 
                UsbDevDescriptor.StrSerialNumber, &SerialNumber);
            
            if (SerialNumber) {
                StrCpyS(DeviceInfo->SerialNumber, sizeof(DeviceInfo->SerialNumber)/sizeof(CHAR16), SerialNumber);
                gBS->FreePool(SerialNumber);
            }
        }
        
        if (LangIDTable) gBS->FreePool(LangIDTable);
    }
}
```

## Secure key verification methods

### File-based key authentication

Implement secure boot.key file reading and verification:

```c
EFI_STATUS VerifyBootKeyFile(
    IN EFI_FILE_PROTOCOL *RootDir,
    IN UINT8             *ExpectedKeyHash
) {
    EFI_STATUS         Status;
    EFI_FILE_PROTOCOL  *KeyFile;
    VOID               *KeyBuffer = NULL;
    UINTN              KeySize;
    UINT8              ComputedHash[SHA256_DIGEST_SIZE];
    
    // Open boot.key file
    Status = RootDir->Open(RootDir, &KeyFile, L"boot.key", EFI_FILE_MODE_READ, 0);
    if (EFI_ERROR(Status)) return Status;
    
    // Get file size and read data
    EFI_FILE_INFO *FileInfo;
    UINTN InfoSize = sizeof(EFI_FILE_INFO) + 256;
    FileInfo = AllocatePool(InfoSize);
    
    KeyFile->GetInfo(KeyFile, &gEfiFileInfoGuid, &InfoSize, FileInfo);
    KeySize = FileInfo->FileSize;
    
    KeyBuffer = AllocatePool(KeySize);
    Status = KeyFile->Read(KeyFile, &KeySize, KeyBuffer);
    KeyFile->Close(KeyFile);
    gBS->FreePool(FileInfo);
    
    // Verify key file integrity using SHA256
    VOID *HashCtx = AllocatePool(Sha256GetContextSize());
    Sha256Init(HashCtx);
    Sha256Update(HashCtx, KeyBuffer, KeySize);
    Sha256Final(HashCtx, ComputedHash);
    
    // Secure memory comparison
    BOOLEAN KeyValid = TRUE;
    for (UINTN i = 0; i < SHA256_DIGEST_SIZE; i++) {
        if (ComputedHash[i] != ExpectedKeyHash[i]) {
            KeyValid = FALSE;
        }
    }
    
    // Cleanup sensitive data
    ZeroMem(KeyBuffer, KeySize);
    ZeroMem(HashCtx, Sha256GetContextSize());
    gBS->FreePool(KeyBuffer);
    gBS->FreePool(HashCtx);
    
    return KeyValid ? EFI_SUCCESS : EFI_SECURITY_VIOLATION;
}
```

### Hardware-based authentication

Generate and verify hardware fingerprints using system-specific identifiers:

```c
EFI_STATUS GenerateHardwareFingerprint(
    IN  USB_DEVICE_INFO *UsbDevices,
    IN  UINTN           DeviceCount,
    OUT UINT8           *Fingerprint
) {
    VOID  *HashCtx;
    UINTN CtxSize;
    
    CtxSize = Sha256GetContextSize();
    HashCtx = AllocatePool(CtxSize);
    
    Sha256Init(HashCtx);
    
    // Hash all USB device identifiers
    for (UINTN i = 0; i < DeviceCount; i++) {
        Sha256Update(HashCtx, &UsbDevices[i].VendorId, sizeof(UINT16));
        Sha256Update(HashCtx, &UsbDevices[i].ProductId, sizeof(UINT16));
        if (UsbDevices[i].SerialNumber[0] != L'\0') {
            Sha256Update(HashCtx, UsbDevices[i].SerialNumber, 
                StrSize(UsbDevices[i].SerialNumber));
        }
    }
    
    Sha256Final(HashCtx, Fingerprint);
    gBS->FreePool(HashCtx);
    
    return EFI_SUCCESS;
}
```

## In-memory EFI binary loading and execution

### LoadImage and StartImage implementation

Execute the embedded boot.efi after successful authentication:

```c
EFI_STATUS LoadAndExecuteEmbeddedEfi(
    IN VOID  *EmbeddedBinary,
    IN UINTN BinarySize
) {
    EFI_STATUS Status;
    EFI_HANDLE ImageHandle = NULL;
    EFI_LOADED_IMAGE_PROTOCOL *LoadedImage;
    
    // Verify PE/COFF signature integrity
    Status = VerifyPeImageSignature(EmbeddedBinary, BinarySize);
    if (EFI_ERROR(Status)) {
        DEBUG((DEBUG_ERROR, "Embedded binary signature verification failed: %r\n", Status));
        return Status;
    }
    
    // Load image from memory buffer
    Status = gBS->LoadImage(
        FALSE,              // BootPolicy
        gImageHandle,       // ParentImageHandle
        NULL,               // DevicePath (NULL is acceptable)
        EmbeddedBinary,     // SourceBuffer
        BinarySize,         // SourceSize
        &ImageHandle        // ImageHandle
    );
    
    if (EFI_ERROR(Status)) {
        DEBUG((DEBUG_ERROR, "LoadImage failed: %r\n", Status));
        return Status;
    }
    
    // Verify loaded image properties
    Status = gBS->HandleProtocol(ImageHandle, &gEfiLoadedImageProtocolGuid, (VOID**)&LoadedImage);
    if (!EFI_ERROR(Status)) {
        DEBUG((DEBUG_INFO, "Image loaded at: 0x%p, Size: 0x%lx\n", 
               LoadedImage->ImageBase, LoadedImage->ImageSize));
    }
    
    // Transfer control to loaded image
    Status = gBS->StartImage(ImageHandle, NULL, NULL);
    
    // Cleanup on failure
    if (EFI_ERROR(Status)) {
        DEBUG((DEBUG_ERROR, "StartImage failed: %r\n", Status));
        gBS->UnloadImage(ImageHandle);
    }
    
    return Status;
}
```

## Complete wrapper implementation

### Main authentication flow

Here's the complete Auth.efi main function implementing dual authentication:

```c
EFI_STATUS
EFIAPI
UefiMain(
    IN EFI_HANDLE        ImageHandle,
    IN EFI_SYSTEM_TABLE  *SystemTable
)
{
    EFI_STATUS                       Status;
    EFI_SIMPLE_FILE_SYSTEM_PROTOCOL  *FileSystem;
    EFI_FILE_PROTOCOL                *RootDir;
    USB_DEVICE_INFO                  *UsbDevices;
    UINTN                            UsbDeviceCount;
    UINT8                            HardwareFingerprint[32];
    UINT8                            ExpectedKeyHash[32] = { /* Hardcoded expected hash */ };
    UINT8                            ExpectedHwFingerprint[32] = { /* Hardcoded expected fingerprint */ };
    VOID                             *EmbeddedBootEfi;
    UINTN                            BootEfiSize;
    
    DEBUG((DEBUG_INFO, "UEFI Authentication Wrapper starting...\n"));
    
    // Step 1: Access file system for boot.key
    Status = gBS->LocateProtocol(&gEfiSimpleFileSystemProtocolGuid, NULL, (VOID**)&FileSystem);
    if (EFI_ERROR(Status)) {
        DEBUG((DEBUG_ERROR, "File system access failed: %r\n", Status));
        return Status;
    }
    
    Status = FileSystem->OpenVolume(FileSystem, &RootDir);
    if (EFI_ERROR(Status)) {
        DEBUG((DEBUG_ERROR, "Volume access failed: %r\n", Status));
        return Status;
    }
    
    // Step 2: Verify boot.key file
    Status = VerifyBootKeyFile(RootDir, ExpectedKeyHash);
    RootDir->Close(RootDir);
    
    if (EFI_ERROR(Status)) {
        DEBUG((DEBUG_ERROR, "Boot key verification failed: %r\n", Status));
        return EFI_ACCESS_DENIED;
    }
    
    DEBUG((DEBUG_INFO, "Boot key verification successful\n"));
    
    // Step 3: Enumerate and verify USB hardware identifiers
    Status = EnumerateUSBDevices(&UsbDevices, &UsbDeviceCount);
    if (EFI_ERROR(Status)) {
        DEBUG((DEBUG_ERROR, "USB enumeration failed: %r\n", Status));
        return Status;
    }
    
    Status = GenerateHardwareFingerprint(UsbDevices, UsbDeviceCount, HardwareFingerprint);
    gBS->FreePool(UsbDevices);
    
    if (EFI_ERROR(Status)) {
        DEBUG((DEBUG_ERROR, "Hardware fingerprint generation failed: %r\n", Status));
        return Status;
    }
    
    // Verify hardware fingerprint
    BOOLEAN FingerprintValid = TRUE;
    for (UINTN i = 0; i < 32; i++) {
        if (HardwareFingerprint[i] != ExpectedHwFingerprint[i]) {
            FingerprintValid = FALSE;
        }
    }
    
    if (!FingerprintValid) {
        DEBUG((DEBUG_ERROR, "Hardware fingerprint verification failed\n"));
        return EFI_ACCESS_DENIED;
    }
    
    DEBUG((DEBUG_INFO, "Hardware fingerprint verification successful\n"));
    
    // Step 4: Extract embedded boot.efi
    Status = GetEmbeddedBootEfi(&EmbeddedBootEfi, &BootEfiSize);
    if (EFI_ERROR(Status)) {
        DEBUG((DEBUG_ERROR, "Failed to extract embedded boot.efi: %r\n", Status));
        return Status;
    }
    
    // Step 5: Load and execute authenticated boot.efi
    DEBUG((DEBUG_INFO, "Loading embedded boot.efi (%lu bytes)\n", BootEfiSize));
    Status = LoadAndExecuteEmbeddedEfi(EmbeddedBootEfi, BootEfiSize);
    
    if (EmbeddedBootEfi) {
        gBS->FreePool(EmbeddedBootEfi);
    }
    
    return Status;
}
```

## Security considerations and protection mechanisms

### Anti-tampering measures

Implement multiple layers of protection for the embedded EFI files:

```c
// Verify PE/COFF signature and structure integrity
EFI_STATUS VerifyPeImageSignature(
    IN VOID  *ImageBuffer,
    IN UINTN ImageSize
) {
    EFI_IMAGE_DOS_HEADER    *DosHeader;
    EFI_IMAGE_NT_HEADERS64  *NtHeaders;
    
    // Basic PE structure validation
    DosHeader = (EFI_IMAGE_DOS_HEADER*)ImageBuffer;
    if (DosHeader->e_magic != EFI_IMAGE_DOS_SIGNATURE) {
        return EFI_INVALID_PARAMETER;
    }
    
    NtHeaders = (EFI_IMAGE_NT_HEADERS64*)((UINT8*)ImageBuffer + DosHeader->e_lfanew);
    if (NtHeaders->Signature != EFI_IMAGE_NT_SIGNATURE) {
        return EFI_INVALID_PARAMETER;
    }
    
    // Additional integrity checks
    if (NtHeaders->OptionalHeader.Magic != EFI_IMAGE_NT_OPTIONAL_HDR64_MAGIC) {
        return EFI_INVALID_PARAMETER;
    }
    
    return EFI_SUCCESS;
}
```

### Secure memory management

Ensure sensitive data is properly cleared:

```c
VOID SecureClearMemory(
    IN VOID  *Buffer,
    IN UINTN Size
) {
    // Use volatile to prevent compiler optimization
    volatile UINT8 *ByteBuffer = (volatile UINT8*)Buffer;
    
    for (UINTN i = 0; i < Size; i++) {
        ByteBuffer[i] = 0;
    }
}
```

## Build process and compilation

### EDK2 build configuration

Create a package description file (AuthWrapper.dsc):

```ini
[Defines]
  PLATFORM_NAME           = AuthWrapper
  PLATFORM_GUID           = 12345678-ABCD-EFGH-IJKL-123456789ABC
  PLATFORM_VERSION        = 1.0
  DSC_SPECIFICATION       = 1.28
  OUTPUT_DIRECTORY        = Build/AuthWrapper
  SUPPORTED_ARCHITECTURES = X64
  BUILD_TARGETS           = DEBUG|RELEASE
  SKUID_IDENTIFIER        = DEFAULT

[LibraryClasses]
  BaseLib|MdePkg/Library/BaseLib/BaseLib.inf
  UefiApplicationEntryPoint|MdePkg/Library/UefiApplicationEntryPoint/UefiApplicationEntryPoint.inf
  UefiLib|MdePkg/Library/UefiLib/UefiLib.inf
  MemoryAllocationLib|MdePkg/Library/UefiMemoryAllocationLib/UefiMemoryAllocationLib.inf
  BaseCryptLib|CryptoPkg/Library/BaseCryptLib/BaseCryptLib.inf

[Components]
  AuthWrapperPkg/Applications/AuthWrapper/AuthWrapper.inf
```

Create the application INF file (AuthWrapper.inf):

```ini
[Defines]
  INF_VERSION     = 1.25
  BASE_NAME       = AuthWrapper
  FILE_GUID       = 87654321-ABCD-EFGH-IJKL-123456789ABC
  MODULE_TYPE     = UEFI_APPLICATION
  VERSION_STRING  = 1.0
  ENTRY_POINT     = UefiMain

[Sources]
  Main.c
  UsbEnumeration.c
  KeyVerification.c
  EfiLoader.c

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  CryptoPkg/CryptoPkg.dec

[LibraryClasses]
  UefiApplicationEntryPoint
  UefiLib
  BaseMemoryLib
  MemoryAllocationLib
  BaseCryptLib
  FileHandleLib

[Protocols]
  gEfiSimpleFileSystemProtocolGuid
  gEfiUsbIoProtocolGuid
  gEfiFirmwareVolume2ProtocolGuid
  gEfiLoadedImageProtocolGuid

[Guids]
  gEfiFileInfoGuid
```

### Build commands

```bash
# Setup EDK2 environment
export WORKSPACE="$(pwd)"
export EDK_TOOLS_PATH="$WORKSPACE/edk2/BaseTools"
cd edk2 && source edksetup.sh BaseTools && cd ..

# Build the authentication wrapper
build -a X64 -t GCC5 -p AuthWrapperPkg/AuthWrapper.dsc

# The resulting Auth.efi will be in:
# Build/AuthWrapper/DEBUG_GCC5/X64/AuthWrapper.efi
```

### Renaming and deployment

After building, rename the wrapper to boot.efi and deploy to the customer's USB drive:

```bash
# Copy and rename wrapper
cp Build/AuthWrapper/DEBUG_GCC5/X64/AuthWrapper.efi /mnt/usb/EFI/BOOT/boot.efi

# The original boot.efi is now embedded within the wrapper
# and will be extracted and executed after dual authentication
```

## GRUB integration considerations

The renamed Auth.efi (now boot.efi) integrates seamlessly with GRUB bootloader environments. When GRUB chainloads to the EFI binary, the wrapper performs authentication before loading the original bootloader. This maintains full compatibility with existing GRUB configurations while adding the security layer.

The dual authentication approach combining file-based keys with USB hardware identifiers provides robust protection against unauthorized access while maintaining the flexibility of the original UEFI boot process. The embedded binary extraction and in-memory execution ensure the protected bootloader remains functional after successful authentication.

This implementation provides enterprise-grade security for UEFI boot processes while maintaining compatibility with existing bootloader ecosystems and UEFI Secure Boot requirements.
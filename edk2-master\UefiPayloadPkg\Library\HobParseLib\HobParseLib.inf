## @file
#  UPL Hob Parse Library.
#
#  Copyright (c) 2024, Intel Corporation. All rights reserved.<BR>
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##
[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = HobParseLib
  FILE_GUID                      = EFB05FE7-604B-40DA-9A59-E2F998528754
  MODULE_TYPE                    = DXE_DRIVER
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = HobParseLib|DXE_DRIVER DXE_RUNTIME_DRIVER SMM_CORE DXE_SMM_DRIVER UEFI_APPLICATION UEFI_DRIVER

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources]
  HobParseLib.c

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  UefiPayloadPkg/UefiPayloadPkg.dec

[LibraryClasses]
  BaseLib
  BaseMemoryLib
  IoLib
  DebugLib
  PcdLib
  HobLib

[Pcd.IA32,Pcd.X64,Pcd.RISCV64]
  gUefiPayloadPkgTokenSpaceGuid.PcdSystemMemoryUefiRegionSize
  gUefiPayloadPkgTokenSpaceGuid.PcdHandOffFdtEnable

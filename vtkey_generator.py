#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VTKey密钥生成器
基于逆向分析的BOOT.EFI密钥生成算法
"""

import os
import hashlib
import struct
from typing import Optional

class VTKeyGenerator:
    # 自定义Base32字符集（缺少I和O）
    BASE32_CHARSET = "0123456789ABCDEFGHJKLMNPQRSTUVWXYZ"
    
    def __init__(self):
        self.charset = self.BASE32_CHARSET
    
    def get_hardware_id(self) -> bytes:
        """
        获取硬件标识符
        这里使用多种硬件信息的组合来生成唯一标识
        """
        # 获取系统信息
        import platform
        import uuid
        
        # 组合多种硬件信息
        hw_info = []
        
        # CPU信息
        hw_info.append(platform.processor())
        
        # 主板UUID（如果可用）
        try:
            hw_info.append(str(uuid.getnode()))
        except:
            hw_info.append("default_node")
        
        # 系统信息
        hw_info.append(platform.system())
        hw_info.append(platform.machine())
        
        # 组合所有信息
        combined = "|".join(hw_info).encode('utf-8')
        
        # 使用SHA256生成16字节标识符
        hash_obj = hashlib.sha256(combined)
        return hash_obj.digest()[:16]
    
    def bytes_to_base32(self, data: bytes) -> str:
        """
        将字节数据转换为自定义Base32格式
        """
        if len(data) != 16:
            raise ValueError("数据必须是16字节")
        
        # 将16字节转换为整数
        value = int.from_bytes(data, byteorder='big')
        
        # 转换为Base32
        result = []
        charset_len = len(self.charset)
        
        for _ in range(16):  # 生成16个字符
            result.append(self.charset[value % charset_len])
            value //= charset_len
        
        return ''.join(reversed(result))
    
    def generate_vtkey_name(self, hardware_id: Optional[bytes] = None) -> str:
        """
        生成vtkey文件名
        """
        if hardware_id is None:
            hardware_id = self.get_hardware_id()
        
        # 转换为Base32
        key_name = self.bytes_to_base32(hardware_id)
        
        return f"{key_name}.vtkey"
    
    def generate_from_wpsettings(self, wpsettings_data: bytes) -> str:
        """
        从WPSettings.dat数据生成vtkey文件名
        """
        if len(wpsettings_data) < 12:
            raise ValueError("WPSettings.dat数据不完整")
        
        # 提取8字节密钥部分（跳过前4字节的长度标识）
        key_data = wpsettings_data[4:12]
        
        # 扩展到16字节（重复一次）
        extended_key = key_data + key_data
        
        return self.bytes_to_base32(extended_key)
    
    def create_vtkey_file(self, filename: str, content: str = "") -> bool:
        """
        创建vtkey文件
        """
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        except Exception as e:
            print(f"创建文件失败: {e}")
            return False

def main():
    generator = VTKeyGenerator()
    
    print("=== VTKey密钥生成器 ===")
    print()
    
    # 方法1：基于当前硬件生成
    print("方法1：基于当前硬件生成密钥")
    hardware_key = generator.generate_vtkey_name()
    print(f"生成的vtkey文件名: {hardware_key}")
    
    # 创建文件
    if generator.create_vtkey_file(hardware_key):
        print(f"已创建文件: {hardware_key}")
    
    print()
    
    # 方法2：从WPSettings.dat生成
    print("方法2：从WPSettings.dat生成密钥")
    
    # 示例数据（您可以替换为实际的WPSettings.dat内容）
    example_wpsettings = bytes.fromhex('0c000000ce7c8417a1124ad9')
    
    try:
        wps_key = generator.generate_from_wpsettings(example_wpsettings)
        wps_filename = f"{wps_key}.vtkey"
        print(f"从WPSettings生成的密钥: {wps_filename}")
        
        if generator.create_vtkey_file(wps_filename):
            print(f"已创建文件: {wps_filename}")
    except Exception as e:
        print(f"从WPSettings生成失败: {e}")
    
    print()
    print("=== 使用说明 ===")
    print("1. 将生成的.vtkey文件放到C盘根目录")
    print("2. 或者放到C:\\Windows\\System32\\目录")
    print("3. 使用修改后的BOOT.EFI启动")

if __name__ == "__main__":
    main()

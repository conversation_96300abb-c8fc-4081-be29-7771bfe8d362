
if [ "$grub_platform" = "pc" ]; then
    menuentry "$VTLANG_LB_SBOOT_WINDOWS" --class=boot_windows --class=F4boot {
    
        set partid=3
        while [ $partid -le 128 ]; do
            if vt_check_part_exist $partid; then
                for bt in bootmgr BOOTMGR Bootmgr BootMGR; do
                    if [ -f ($vtoydev,$partid)/$bt ]; then
                        set root=($vtoydev,$partid)
                        ntldr /$bt
                        boot
                    fi
                done
            else
                break
            fi
            vt_incr partid 1
        done
    
        if search -n -s -f /Boot/BCD; then
            for bt in bootmgr BOOTMGR Bootmgr BootMGR; do
                if [ -f /$bt ]; then
                    if regexp '^hd0' $root; then
                        ntldr /$bt
                    else
                        drivemap -s hd0 $root
                        ntldr /$bt
                    fi
                    break
                fi
            done
        elif search -n -s -f /NTDETECT.COM; then
            drivemap -s hd0 $root
            ntldr /ntldr
        else
            echo "Windows NOT found ..."
        fi        
    }
    
    menuentry "$VTLANG_LB_SBOOT_G4D" --class=boot_g4d --class=F4boot {
        if search -n -s -f /grldr; then
            ntldr /grldr
        else
            echo "Grub4dos NOT found ..."
        fi        
    }
    
    menuentry "$VTLANG_LB_SBOOT_HDD1" --class=boot_disk --class=F4boot {
        set root=(hd0,1)
        chainloader +1
        boot
    }
    
    menuentry "$VTLANG_LB_SBOOT_HDD2" --class=boot_disk --class=F4boot {
        set root=(hd1,1)
        chainloader +1
        boot
    }
    
    menuentry "$VTLANG_LB_SBOOT_HDD3" --class=boot_disk --class=F4boot {
        set root=(hd2,1)
        chainloader +1
        boot
    }
    
else

    menuentry "$VTLANG_LB_SBOOT_WINDOWS" --class=boot_windows --class=F4boot {    
        
        set partid=3
        while [ $partid -le 128 ]; do
            if vt_check_part_exist $partid; then
                if [ -f ($vtoydev,$partid)/EFI/Microsoft/Boot/bootmgfw.efi ]; then
                    set root=($vtoydev,$partid)
                    terminal_output  console
                    chainloader /EFI/Microsoft/Boot/bootmgfw.efi
                    boot
                elif [ -f ($vtoydev,$partid)/efi/Microsoft/Boot/bootmgfw.efi ]; then
                    set root=($vtoydev,$partid)
                    terminal_output  console
                    chainloader /efi/Microsoft/Boot/bootmgfw.efi
                    boot
                elif [ -f ($vtoydev,$partid)/VTEFI/Microsoft/Boot/bootmgfw.efi ]; then
                    set root=($vtoydev,$partid)
                    terminal_output  console
                    chainloader /VTEFI/Microsoft/Boot/bootmgfw.efi
                    boot
                fi
            else
                break
            fi
            vt_incr partid 1
        done

        if search -n -s -f /EFI/Microsoft/Boot/bootmgfw.efi; then
            terminal_output  console
            chainloader /EFI/Microsoft/Boot/bootmgfw.efi
            boot
        elif search -n -s -f /efi/Microsoft/Boot/bootmgfw.efi; then
            terminal_output  console
            chainloader /efi/Microsoft/Boot/bootmgfw.efi
            boot
        else
            echo "Windows NOT found ..."
        fi        
    }
    
    if [ "$grub_cpu" = "i386" ]; then
        menuentry "$VTLANG_LB_SBOOT_IA32EFI" --class=boot_uefi --class=F4boot {
            set VTOY_SEARCH_NO_VTOYEFI=1
            if search -n -s -f /efi/boot/bootia32.efi; then
                unset VTOY_SEARCH_NO_VTOYEFI
                terminal_output  console
                chainloader /efi/boot/bootia32.efi
                boot
            elif search -n -s -f /vtefi/boot/bootia32.efi; then
                unset VTOY_SEARCH_NO_VTOYEFI
                terminal_output  console
                chainloader /vtefi/boot/bootia32.efi
                boot
            else
                unset VTOY_SEARCH_NO_VTOYEFI
                echo "BOOTIA32.EFI NOT found ..."
            fi        
        }
        
        menuentry "$VTLANG_LB_SBOOT_XORBOOT" --class=boot_xorboot --class=F4boot {
            set VTOY_SEARCH_NO_VTOYEFI=1
            if search -n -s -f /efi/xorboot/xorboot32.xor; then
                unset VTOY_SEARCH_NO_VTOYEFI
                terminal_output  console
                if [ -f /efi/xorboot/bootia32.efi ]; then
                    chainloader /efi/xorboot/bootia32.efi
                elif [ -f /efi/xorboot/xorboot.efi ]; then
                    chainloader /efi/xorboot/xorboot.efi
                fi
                boot
            elif search -n -s -f /vtefi/xorboot/xorboot32.xor; then
                unset VTOY_SEARCH_NO_VTOYEFI
                terminal_output  console
                if [ -f /vtefi/xorboot/bootia32.efi ]; then
                    chainloader /vtefi/xorboot/bootia32.efi
                elif [ -f /vtefi/xorboot/xorboot.efi ]; then
                    chainloader /vtefi/xorboot/xorboot.efi
                fi
                boot
            else
                unset VTOY_SEARCH_NO_VTOYEFI
                echo "xorboot NOT found ..."
            fi        
        }
    elif [ "$grub_cpu" = "arm64" ]; then
        menuentry "$VTLANG_LB_SBOOT_AA64EFI" --class=boot_uefi --class=F4boot {
            set VTOY_SEARCH_NO_VTOYEFI=1
            if search -n -s -f /efi/boot/bootaa64.efi; then
                unset VTOY_SEARCH_NO_VTOYEFI
                terminal_output  console
                chainloader /efi/boot/bootaa64.efi
                boot
            elif search -n -s -f /vtefi/boot/bootaa64.efi; then
                unset VTOY_SEARCH_NO_VTOYEFI
                terminal_output  console
                chainloader /vtefi/boot/bootaa64.efi
                boot
            else
                unset VTOY_SEARCH_NO_VTOYEFI
                echo "BOOTAA64.EFI NOT found ..."
            fi        
        }
    else
        menuentry "$VTLANG_LB_SBOOT_X64EFI" --class=boot_uefi --class=F4boot {
            set VTOY_SEARCH_NO_VTOYEFI=1
            if search -n -s -f /efi/boot/bootx64.efi; then
                unset VTOY_SEARCH_NO_VTOYEFI
                terminal_output  console
                chainloader /efi/boot/bootx64.efi
                boot
            elif search -n -s -f /vtefi/boot/bootx64.efi; then
                unset VTOY_SEARCH_NO_VTOYEFI
                terminal_output  console
                chainloader /vtefi/boot/bootx64.efi
                boot
            else
                unset VTOY_SEARCH_NO_VTOYEFI
                echo "BOOTX64.EFI NOT found ..."
            fi        
        }

        menuentry "$VTLANG_LB_SBOOT_XORBOOT" --class=boot_xorboot --class=F4boot {
            set VTOY_SEARCH_NO_VTOYEFI=1
            if search -n -s -f /efi/xorboot/xorboot.xor; then
                unset VTOY_SEARCH_NO_VTOYEFI
                terminal_output  console
                if [ -f /efi/xorboot/bootx64.efi ]; then
                    chainloader /efi/xorboot/bootx64.efi
                elif [ -f /efi/xorboot/xorboot.efi ]; then
                    chainloader /efi/xorboot/xorboot.efi
                fi
                boot
            elif search -n -s -f /vtefi/xorboot/xorboot.xor; then
                unset VTOY_SEARCH_NO_VTOYEFI
                terminal_output  console
                if [ -f /vtefi/xorboot/bootx64.efi ]; then
                    chainloader /vtefi/xorboot/bootx64.efi
                elif [ -f /vtefi/xorboot/xorboot.efi ]; then
                    chainloader /vtefi/xorboot/xorboot.efi
                fi
                boot
            else
                unset VTOY_SEARCH_NO_VTOYEFI
                echo "xorboot NOT found ..."
            fi        
        }
    fi
    

fi

menuentry "$VTLANG_RETURN_PREVIOUS" --class=vtoyret VTOY_RET {
    echo "Return ..."
}

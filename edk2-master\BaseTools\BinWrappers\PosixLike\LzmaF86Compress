#!/usr/bin/env bash
#
# This script will exec LzmaCompress tool with --f86 option that enables converter for x86 code.
#
# (C) Copyright 2016 Hewlett Packard Enterprise Development LP<BR>
# Copyright (c) 2012, Intel Corporation. All rights reserved.<BR>
# SPDX-License-Identifier: BSD-2-Clause-Patent
#

for arg; do
  case $arg in
    -e|-d)
      set -- "$@" --f86
      break
    ;;
  esac
done

exec LzmaCompress "$@"

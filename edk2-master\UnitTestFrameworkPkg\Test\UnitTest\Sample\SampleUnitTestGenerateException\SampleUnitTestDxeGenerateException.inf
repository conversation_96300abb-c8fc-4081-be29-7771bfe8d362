## @file
# Sample UnitTest built for execution in DXE.
# This test case generates an exception. For some host-based environments, this
# is a fatal condition that terminates the unit tests and no additional test
# cases are executed. On other environments, this condition may be report a unit
# test failure and continue with additional unit tests.
#
# Copyright (c) 2024, Intel Corporation. All rights reserved.<BR>
# SPDX-License-Identifier: BSD-2-Clause-Patent
##

[Defines]
  INF_VERSION    = 0x00010006
  BASE_NAME      = SampleUnitTestDxeGenerateException
  FILE_GUID      = 2E8C07AF-FAC7-44F3-9108-7F548D347EE1
  MODULE_TYPE    = DXE_DRIVER
  VERSION_STRING = 1.0
  ENTRY_POINT    = DxeEntryPoint

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources]
  SampleUnitTestGenerateException.c

[Packages]
  MdePkg/MdePkg.dec

[LibraryClasses]
  UefiDriverEntryPoint
  BaseLib
  DebugLib
  UnitTestLib
  PrintLib

[Pcd]
  gEfiMdePkgTokenSpaceGuid.PcdDebugPropertyMask

[Depex]
  TRUE

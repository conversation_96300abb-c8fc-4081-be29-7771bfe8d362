## @file
#  Kvmtool virtual memory map library.
#
#  Copyright (c) 2018, ARM Limited. All rights reserved.
#
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x0001001B
  BASE_NAME                      = KvmtoolVirtMemInfoLib
  FILE_GUID                      = B752E953-394F-462C-811C-F8BE35C8C071
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = ArmVirtMemInfoLib

[Sources]
  KvmtoolVirtMemInfoLib.c

[Packages]
  ArmPkg/ArmPkg.dec
  ArmVirtPkg/ArmVirtPkg.dec
  EmbeddedPkg/EmbeddedPkg.dec
  MdeModulePkg/MdeModulePkg.dec
  MdePkg/MdePkg.dec

[LibraryClasses]
  ArmLib
  BaseLib
  BaseMemoryLib
  DebugLib
  MemoryAllocationLib
  PcdLib

[Pcd]
  gArmTokenSpaceGuid.PcdFvBaseAddress
  gArmTokenSpaceGuid.PcdSystemMemoryBase
  gArmTokenSpaceGuid.PcdSystemMemorySize

[FixedPcd]
  gArmTokenSpaceGuid.PcdFvSize

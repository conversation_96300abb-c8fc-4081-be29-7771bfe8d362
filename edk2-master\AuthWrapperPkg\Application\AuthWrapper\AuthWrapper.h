/** @file
  Auth Wrapper Application Header File

  Copyright (c) 2024, Auth Wrapper Project. All rights reserved.
  SPDX-License-Identifier: BSD-2-Clause-Patent

**/

#ifndef __AUTH_WRAPPER_H__
#define __AUTH_WRAPPER_H__

#include <Uefi.h>
#include <Library/UefiLib.h>
#include <Library/BaseLib.h>
#include <Library/BaseMemoryLib.h>
#include <Library/MemoryAllocationLib.h>
#include <Library/UefiBootServicesTableLib.h>
#include <Library/UefiRuntimeServicesTableLib.h>
#include <Library/PrintLib.h>
#include <Library/DebugLib.h>
#include <Library/DevicePathLib.h>
#include <Library/FileHandleLib.h>
#include <Library/ShellLib.h>
#include <Library/BaseCryptLib.h>

#include <Protocol/SimpleFileSystem.h>
#include <Protocol/UsbIo.h>
#include <Protocol/BlockIo.h>
#include <Protocol/LoadedImage.h>

#include <Guid/FileInfo.h>

//
// Constants
//
#define SHA256_DIGEST_SIZE          32
#define BASE32_CHARSET              "0123456789ABCDEFGHJKLMNPQRSTUVWXYZ"
#define KEY_FILENAME_LENGTH         16
#define VERIFICATION_CODE_LENGTH    32
#define HARDWARE_FINGERPRINT_SIZE   32
#define SALT_STRING                 "VTKEY_VERIFICATION_SALT_2024"

//
// USB Device Information Structure
//
typedef struct {
  UINT16    VendorId;
  UINT16    ProductId;
  CHAR16    SerialNumber[64];
} USB_DEVICE_INFO;

//
// Function Prototypes
//

/**
  Main entry point for Auth Wrapper Application.

  @param[in] ImageHandle    The firmware allocated handle for the EFI image.
  @param[in] SystemTable    A pointer to the EFI System Table.

  @retval EFI_SUCCESS       The entry point executed successfully.
  @retval other             Some error occurred when executing this entry point.

**/
EFI_STATUS
EFIAPI
AuthWrapperMain (
  IN EFI_HANDLE        ImageHandle,
  IN EFI_SYSTEM_TABLE  *SystemTable
  );

/**
  Enumerate USB devices and extract hardware information.

  @param[out] DeviceList    Pointer to array of USB device information.
  @param[out] DeviceCount   Number of USB devices found.

  @retval EFI_SUCCESS       USB devices enumerated successfully.
  @retval other             Error occurred during enumeration.

**/
EFI_STATUS
EnumerateUSBDevices (
  OUT USB_DEVICE_INFO **DeviceList,
  OUT UINTN           *DeviceCount
  );

/**
  Generate hardware fingerprint from USB device information.

  @param[in]  DeviceList        Array of USB device information.
  @param[in]  DeviceCount       Number of USB devices.
  @param[out] HardwareFingerprint Generated hardware fingerprint.

  @retval EFI_SUCCESS           Hardware fingerprint generated successfully.
  @retval other                 Error occurred during generation.

**/
EFI_STATUS
GenerateHardwareFingerprint (
  IN  USB_DEVICE_INFO *DeviceList,
  IN  UINTN           DeviceCount,
  OUT UINT8           *HardwareFingerprint
  );

/**
  Convert binary data to Base32 string.

  @param[in]  Data              Binary data to convert.
  @param[in]  DataSize          Size of binary data.
  @param[out] Base32String      Output Base32 string.
  @param[in]  StringSize        Size of output string buffer.

  @retval EFI_SUCCESS           Conversion successful.
  @retval other                 Error occurred during conversion.

**/
EFI_STATUS
ConvertToBase32 (
  IN  UINT8   *Data,
  IN  UINTN   DataSize,
  OUT CHAR16  *Base32String,
  IN  UINTN   StringSize
  );

/**
  Generate key filename from hardware fingerprint.

  @param[in]  HardwareFingerprint Hardware fingerprint data.
  @param[out] KeyFilename         Generated key filename.

  @retval EFI_SUCCESS             Key filename generated successfully.
  @retval other                   Error occurred during generation.

**/
EFI_STATUS
GenerateKeyFilename (
  IN  UINT8   *HardwareFingerprint,
  OUT CHAR16  *KeyFilename
  );

/**
  Generate verification code from hardware fingerprint.

  @param[in]  HardwareFingerprint Hardware fingerprint data.
  @param[out] VerificationCode    Generated verification code.

  @retval EFI_SUCCESS             Verification code generated successfully.
  @retval other                   Error occurred during generation.

**/
EFI_STATUS
GenerateVerificationCode (
  IN  UINT8   *HardwareFingerprint,
  OUT CHAR16  *VerificationCode
  );

/**
  Check if key file exists on C drive.

  @param[in] KeyFilename          Key filename to check.

  @retval TRUE                    Key file exists.
  @retval FALSE                   Key file does not exist.

**/
BOOLEAN
CheckKeyFileExists (
  IN CHAR16 *KeyFilename
  );

/**
  Read key file content from C drive.

  @param[in]  KeyFilename         Key filename to read.
  @param[out] FileContent         Content of the key file.
  @param[in]  ContentSize         Size of content buffer.

  @retval EFI_SUCCESS             Key file read successfully.
  @retval other                   Error occurred during reading.

**/
EFI_STATUS
ReadKeyFileContent (
  IN  CHAR16  *KeyFilename,
  OUT CHAR16  *FileContent,
  IN  UINTN   ContentSize
  );

/**
  Load and execute the original boot.efi file.

  @retval EFI_SUCCESS             Boot.efi loaded and executed successfully.
  @retval other                   Error occurred during loading/execution.

**/
EFI_STATUS
LoadAndExecuteBootEfi (
  VOID
  );

/**
  Perform complete authentication verification.

  @retval EFI_SUCCESS             Authentication successful.
  @retval EFI_ACCESS_DENIED       Authentication failed.
  @retval other                   Error occurred during verification.

**/
EFI_STATUS
PerformAuthentication (
  VOID
  );

#endif // __AUTH_WRAPPER_H__

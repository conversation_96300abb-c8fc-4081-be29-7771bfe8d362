## @file
# This is a sample to demonstrates the use of GoogleTest that supports host
# execution environments for test case that are always expected to fail to
# demonstrate the format of the log file and reports when failures occur.
#
# Copyright (c) 2024, Intel Corporation. All rights reserved.<BR>
# SPDX-License-Identifier: BSD-2-Clause-Patent
##

[Defines]
  INF_VERSION    = 0x00010005
  BASE_NAME      = SampleGoogleTestHostExpectFail
  FILE_GUID      = 6042ADD2-E024-4FD5-8CD7-B2A146BF88D7
  MODULE_TYPE    = HOST_APPLICATION
  VERSION_STRING = 1.0

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources]
  SampleGoogleTestExpectFail.cpp

[Packages]
  MdePkg/MdePkg.dec
  UnitTestFrameworkPkg/UnitTestFrameworkPkg.dec

[LibraryClasses]
  GoogleTestLib
  BaseLib
  DebugLib

[Pcd]
  gEfiMdePkgTokenSpaceGuid.PcdDebugPropertyMask

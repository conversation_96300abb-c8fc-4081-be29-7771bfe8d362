// /**
//
// (C) Copyright 2013-2015 Hewlett-Packard Development Company, L.P.<BR>
// Copyright (c) 2009 - 2018, Intel Corporation. All rights reserved.<BR>
// SPDX-License-Identifier: BSD-2-Clause-Patent
//
// Module Name:
//
// UefiShellLevel2CommandsLib.uni
//
// Abstract:
//
// String definitions for UEFI Shell 2.0 level 2 commands
//
//
// **/

/=#

#langdef   en-US "english"

#string STR_GEN_NO_MEM            #language en-US "%H%s%N: Memory is not available.\r\n"
#string STR_GEN_TOO_MANY          #language en-US "%H%s%N: Too many arguments.\r\n"
#string STR_GEN_TOO_FEW           #language en-US "%H%s%N: Too few arguments.\r\n"
#string STR_GEN_PARAM_INV         #language en-US "%H%s%N: Invalid argument - '%H%s%N'\r\n"
#string STR_GEN_PROBLEM           #language en-US "%H%s%N: Unknown flag - '%H%s%N'\r\n"
#string STR_GEN_PROBLEM_VAL       #language en-US "%H%s%N: Bad value - '%H%s%N' for flag - '%H%s%N'\r\n"
#string STR_GEN_ATTRIBUTE         #language en-US "%H%s%N: Invalid argument - '%H-a%s%N'\r\n"
#string STR_GEN_NO_VALUE          #language en-US "%H%s%N: Missing argument for flag - '%H%s%N'\r\n"
#string STR_GEN_ERR_AD            #language en-US "%H%s%N: Access denied.\r\n"
#string STR_GEN_ERR_FILE          #language en-US "%H%s%N: File '%H%s%N' error - %r\r\n"
#string STR_GEN_ERR_UK            #language en-US "%H%s%N: Status: %r\r\n"
#string STR_GEN_PARAM_CON         #language en-US "%H%s%N: Parameters conflict.\r\n"
#string STR_GEN_PARAM_CONFLICT    #language en-US "%H%s%N: Flags conflict with - '%H%s%N' and '%H%s%N'\r\n"
#string STR_GEN_FILE_OPEN_FAIL    #language en-US "%H%s%N: Cannot open file - '%H%s%N'\r\n"
#string STR_GEN_FILE_CLOSE_FAIL   #language en-US "%H%s%N: Cannot close file - '%H%s%N'\r\n"
#string STR_GEN_FILE_AD           #language en-US "%H%s%N: File access error - '%H%s%N'\r\n"
#string STR_GEN_FILE_NF           #language en-US "%H%s%N: File not found - '%H%s%N'\r\n"
#string STR_GEN_CRLF              #language en-US "\r\n"
#string STR_GEN_NO_CWD            #language en-US "%H%s%N: Current directory not specified.\r\n"
#string STR_GEN_NO_FILES          #language en-US "%H%s%N: No matching files were found.\r\n"
#string STR_GEN_DIR_NF            #language en-US "%H%s%N: Directory not found - '%H%s%N'\r\n"
#string STR_GEN_RES_OK            #language en-US "- [ok]\r\n"
#string STR_GEN_NOT_DIR           #language en-US "%H%s%N: '%H%s%N' is not a directory.\r\n"
#string STR_GEN_NOT_FILE          #language en-US "%H%s%N: '%H%s%N' is not a file.\r\n"
#string STR_GEN_SFO_HEADER        #language en-US "ShellCommand,"%s"\r\n"
#string STR_GEN_MARG_ERROR        #language en-US "%H%s%N: The destination '%H%s%N' is ambiguous.\r\n"
#string STR_GEN_FILE_ERROR        #language en-US "%H%s%N: The destination is an existing file '%H%s%N'.\r\n"
#string STR_GEN_UEFI_FUNC_ERROR   #language en-US "%H%s%N: UEFI function '%H%s%N' returned an incorrect value for: %s (%x).\r\n"
#string STR_GEN_UEFI_FUNC_WARN    #language en-US "%H%s%N: UEFI function '%H%s%N' returned: %r\r\n"
#string STR_GEN_DEST_EXIST_OVR    #language en-US "Destination file already exists.  Overwrite? %BY%Nes, %BN%No, %BA%Nll, %BC%Nancel "
#string STR_GEN_CPY_FAIL          #language en-US "%H%s%N: Copy failure: insufficient capacity on destination media.\r\n"
#string STR_GEN_CPY_READ_ERROR    #language en-US "%H%s%N: reading '%B%s%N': IO Error \r\n"
#string STR_GEN_CPY_WRITE_ERROR   #language en-US "%H%s%N: writing '%B%s%N': IO Error \r\n"
#string STR_GEN_OUT_MEM           #language en-US "%H%s%N: Memory allocation was not successful.\r\n"

#string STR_SET_DISP              #language en-US "%V%8s %N= %H%s%N\r\n"
#string STR_SET_NF                #language en-US "%H%s%N: Environment Variable '%H%s%N' not defined.\r\n"
#string STR_SET_ND                #language en-US "%H%s%N: Environment Variable '%H%s%N' could not be deleted.\r\n"
#string STR_SET_ERROR_SET         #language en-US "%H%s%N: Unable to set %H%s%N\r\n"

#string STR_CD_PRINT              #language en-US "%s\r\n"
#string STR_CD_NF                 #language en-US "%H%s%N: No mapping found.\r\n"

#string STR_MAP_NF                #language en-US "%H%s%N: Cannot find mapped device - '%H%s%N'\r\n"
#string STR_MAP_NOF               #language en-US "%H%s%N: No mappable target found - '%H%s%N'\r\n"
#string STR_MAP_SFO_MAPPINGS      #language en-US "Mappings,"%s","%s","%s"\r\n"
#string STR_MAP_HEADER            #language en-US "%EMapping table%N\r\n"
#string STR_MAP_ENTRY             #language en-US "%E%10s%N %HAlias(s):%N%s\r\n          %s\r\n"
#string STR_MAP_ENTRY_VERBOSE     #language en-US "         Handle:      [%H%02x%N]\r\n"
                                                  "         Media Type:  %s\r\n"
                                                  "         Removable:   %s\r\n"
                                                  "         Current Dir: %s\r\n"

#string STR_ATTRIB_OUTPUT_LINE    #language en-US "Attrib: %1c%1c%1c%1c%1c %s\r\n"

#string STR_MAP_MEDIA_FLOPPY      #language en-US "Floppy"
#string STR_MAP_MEDIA_UNKNOWN     #language en-US "Unknown"
#string STR_MAP_MEDIA_HARDDISK    #language en-US "HardDisk"
#string STR_MAP_MEDIA_CDROM       #language en-US "CD-Rom"

#string STR_MKDIR_ALREADY         #language en-US "Directory '%B%s%N' already exists.\r\n"
#string STR_MKDIR_CREATEFAIL      #language en-US "Directory '%B%s%N' unable to create.\r\n"

#string STR_DATE_FORMAT           #language en-US "%02d/%02d/%04d\r\n"
#string STR_DATE_SFO_FORMAT       #language en-US "Date,"%02d","%02d","%04d"\r\n"

#string STR_TIME_FORMAT           #language en-US "%02d:%02d:%02d (UTC%1s%02d:%02d)"
#string STR_TIME_FORMAT_LOCAL     #language en-US "%02d:%02d:%02d (LOCAL)"
#string STR_TIME_DST0             #language en-US " DST: Not Affected\r\n"
#string STR_TIME_DST1             #language en-US " DST: Affected\r\n"
#string STR_TIME_DST2             #language en-US " DST: Adjusted\r\n"
#string STR_TIME_DST3             #language en-US " DST: Affected and Adjusted\r\n"

#string STR_TIMEZONE_M12          #language en-US "UTC-12:00, International Date Line West\r\n"
#string STR_TIMEZONE_M11          #language en-US "UTC-11:00, Midway Island, Samoa\r\n"
#string STR_TIMEZONE_M10          #language en-US "UTC-10:00, Hawaii\r\n"
#string STR_TIMEZONE_M9           #language en-US "UTC-09:00, Alaska\r\n"
#string STR_TIMEZONE_M8           #language en-US "UTC-08:00, Pacific Time(US & Canada), Tijuana, Portland\r\n"
#string STR_TIMEZONE_M7           #language en-US "UTC-07:00, Arizona, Chihuahua, La Paz, Mazatlan, Mountain Time (US & Canada)\r\n"
#string STR_TIMEZONE_M6           #language en-US "UTC-06:00, Central America, Central Time(US & Canada)\r\n"
#string STR_TIMEZONE_M5           #language en-US "UTC-05:00, Bogota, Lima, Quito, Eastern Time(US & Canada)\r\n"
#string STR_TIMEZONE_M430         #language en-US "UTC-04:30, Caracas\r\n"
#string STR_TIMEZONE_M4           #language en-US "UTC-04:00, Atlantic Time(Canada), Caracas, Santiago, Georgetown\r\n"
#string STR_TIMEZONE_M330         #language en-US "UTC-03:30, Newfoundland\r\n"
#string STR_TIMEZONE_M3           #language en-US "UTC-03:00, Brasilia, Buenos Aires, Greenland\r\n"
#string STR_TIMEZONE_M2           #language en-US "UTC-02:00, Mid-Atlantic\r\n"
#string STR_TIMEZONE_M1           #language en-US "UTC-01:00, Azores, Cape Verde Is.\r\n"
#string STR_TIMEZONE_0            #language en-US "UTC      , Greenwich Mean Time, Casablanca, Monrovia, Dublin, London\r\n"
#string STR_TIMEZONE_P1           #language en-US "UTC+01:00, Amsterdam, Berlin, Bern, Rome, Paris, West Central Africa\r\n"
#string STR_TIMEZONE_P2           #language en-US "UTC+02:00, Athens, Bucharest, Cairo, Jerusalem\r\n"
#string STR_TIMEZONE_P3           #language en-US "UTC+03:00, Baghdad, Kuwait, Riyadh, Moscow, Nairobi, Istanbul\r\n"
#string STR_TIMEZONE_P330         #language en-US "UTC+03:30, Tehran\r\n"
#string STR_TIMEZONE_P4           #language en-US "UTC+04:00, Abu Dhabi, Muscat, Baku, Tbilisi, Yerevan\r\n"
#string STR_TIMEZONE_P430         #language en-US "UTC+04:30, Kabul\r\n"
#string STR_TIMEZONE_P5           #language en-US "UTC+05:00, Ekaterinburg, Islamabad, Karachi, Tashkent\r\n"
#string STR_TIMEZONE_P530         #language en-US "UTC+05:30, Chennai, Kolkata, Mumbai, New Delhi\r\n"
#string STR_TIMEZONE_P545         #language en-US "UTC+05:45, Kathmandu\r\n"
#string STR_TIMEZONE_P6           #language en-US "UTC+06:00, Almaty, Astana, Dhaka, Sri Jayawardenepura\r\n"
#string STR_TIMEZONE_P630         #language en-US "UTC+06:30, Rangoon\r\n"
#string STR_TIMEZONE_P7           #language en-US "UTC+07:00, Bangkok, Hanio, Jakarta, Krasnoyarsk, Novosibirsk\r\n"
#string STR_TIMEZONE_P8           #language en-US "UTC+08:00, Beijing, Chongqing, Hong Kong, Urumqi, Taipei, Perth\r\n"
#string STR_TIMEZONE_P9           #language en-US "UTC+09:00, Osaka, Sapporo, Tokyo, Seoul, Yakutsk\r\n"
#string STR_TIMEZONE_P930         #language en-US "UTC+09:30, Adelaide, Darwin\r\n"
#string STR_TIMEZONE_P10          #language en-US "UTC+10:00, Canberra, Melbourne, Sydney, Guam, Hobart, Vladivostok\r\n"
#string STR_TIMEZONE_P11          #language en-US "UTC+11:00, Magadan, Solomon Is., New Caledonia\r\n"
#string STR_TIMEZONE_P12          #language en-US "UTC+12:00, Auckland, Wellington, Fiji, Kamchatka, Marshall Is.\r\n"
#string STR_TIMEZONE_P13          #language en-US "UTC+13:00, Nuku'alofa\r\n"
#string STR_TIMEZONE_P14          #language en-US "UTC+14:00, Line Islands\r\n"
#string STR_TIMEZONE_LOCAL        #language en-US "LOCAL    , Local Time\r\n"
#string STR_TIMEZONE_SIMPLE       #language en-US "UTC%1s%02d:%02d\r\n"
#string STR_TIMEZONE_SIMPLE_LOCAL #language en-US "LOCAL\r\n"
#string STR_TIMEZONE_NI           #language en-US "No additional information known."

#string STR_LOAD_NOT_IMAGE        #language en-US "Image '%s' is not an image.\r\n"
#string STR_LOAD_NOT_DRIVER       #language en-US "Image '%s' is not a driver.\r\n"
#string STR_LOAD_LOADED           #language en-US "Image '%s' loaded at %p - %r\r\n"
#string STR_LOAD_ERROR            #language en-US "Image '%s' error in StartImage: %r\r\n"

#string STR_LS_LINE_START_ALL     #language en-US "%t %5s %1c % ,L11d  "
#string STR_LS_LINE_END_FILE      #language en-US "%s\r\n"
#string STR_LS_LINE_END_EXE       #language en-US "%V%s%N\r\n"
#string STR_LS_LINE_END_DIR       #language en-US "%B%s%N\r\n"
#string STR_LS_FOOTER_LINE        #language en-US "% ,L11d File(s) % ,L11d bytes\r\n% ,L11d Dir(s)\r\n"
#string STR_LS_HEADER_LINE1       #language en-US "Directory of: %H%s%N\r\n"
#string STR_LS_FILE_NOT_FOUND     #language en-US "%H%s%N: File Not Found - '%H%s%N'\r\n"
#string STR_LS_SFO_VOLINFO        #language en-US "VolumeInfo,"%s","%Ld","%5s","%Ld","%Ld"\r\n"
#string STR_LS_SFO_FILEINFO       #language en-US "FileInfo,"%s","%Ld","%Ld","%s%s%s%s%s","%02d:%02d:%02d","%02d.%02d.%04d","%02d:%02d:%02d","%02d.%02d.%04d","%02d:%02d:%02d","%02d.%02d.%04d"\r\n"

#string STR_VOL_VOLINFO           #language en-US "Volume %s (%s)\r\n"
                                                  "%Ld bytes total disk space\r\n"
                                                  "%Ld bytes available on disk\r\n"
                                                  "%d bytes in each allocation unit\r\n"

#string STR_RM_LOG_DELETE_CONF    #language en-US "Remove Subtree '%B%s%N' [y/n]?"
#string STR_RM_LOG_DELETE         #language en-US "Deleting '%B%s%N'\r\n"
#string STR_RM_LOG_DELETE_ERR     #language en-US "Delete error: %r\r\n"
#string STR_RM_LOG_DELETE_ERR2    #language en-US "Delete error.  Couldn't open file: %r\r\n"
#string STR_RM_LOG_DELETE_ERR3    #language en-US "Delete error.  Invalid target '%B%s%N'\r\n"
#string STR_RM_LOG_DELETE_COMP    #language en-US "Delete successful.\r\n"
#string STR_RM_LOG_DETELE_RO      #language en-US "%H%s%N: '%H%s%N' is read-only\r\n"

#string STR_MV_OUTPUT             #language en-US "Moving %s -> %s\r\n"
#string STR_MV_INV_SUB            #language en-US "Cannot move a directory into itself or its subdirectory.\r\n"
#string STR_MV_INV_RO             #language en-US "Cannot move to or from a read-only file or directory '%B%s%N'\r\n"
#string STR_MV_INV_CWD            #language en-US "Cannot move current working directory or its subdirectory.\r\n"

#string STR_CP_OUTPUT             #language en-US "Copying %s -> %s\r\n"
#string STR_CP_ERROR              #language en-US "%H%s%N: Could not copy - '%H%s%N'\r\n"
#string STR_CP_DIR_REQ            #language en-US "%H%s%N: Copying a directory requires -r.\r\n"
#string STR_CP_DIR_WNF            #language en-US "%H%s%N: The specified path does not exist - '%H%s%N'\r\n"
#string STR_CP_SD_SAME            #language en-US "%H%s%N: The source and destination are the same.\r\n"
#string STR_CP_SD_PARENT          #language en-US "%H%s%N: The destination is a parent of the source.\r\n"
#string STR_CP_DEST_ERROR         #language en-US "%H%s%N: The destination is read-only.\r\n"
#string STR_CP_DEST_OPEN_FAIL     #language en-US "%H%s%N: The destination file '%B%s%N' failed to open with create.\r\n"
#string STR_CP_DEST_DIR_FAIL      #language en-US "%H%s%N: The destination directory '%B%s%N' could not be created.\r\n"
#string STR_CP_SRC_OPEN_FAIL     #language en-US "%H%s%N: The source file '%B%s%N' failed to open with read.\r\n"

#string STR_GET_HELP_ATTRIB       #language en-US ""
".TH attrib 0 "Displays or modifies the attributes of files or directories."\r\n"
".SH NAME\r\n"
"Displays or modifies the attributes of files or directories.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"ATTRIB [+a|-a] [+s|-s] [+h|-h] [+r|-r] [file...] [directory...]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  +a|-a     - Sets or clears the 'archive' attribute.\r\n"
"  +s|-s     - Sets or clears the 'system' attribute.\r\n"
"  +h|-h     - Sets or clears the 'hidden' attribute.\r\n"
"  +r|-r     - Sets or clears the 'read-only' attribute.\r\n"
"  file      - Specifies the file name (wild cards are permitted).\r\n"
"  directory - Specifies the directory name (wildcards are permitted).\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. Four attribute types are supported in the UEFI file system:\r\n"
"     - Archive [A]\r\n"
"     - System [S]\r\n"
"     - Hidden [H]\r\n"
"     - Read only [R]\r\n"
"  2. If a file (in general meaning) is a directory, then it is also shown\r\n"
"     to have the attribute [D].\r\n"
"  3. If any file in the file list that is specified \r\n"
"     does not exist, attrib will continue processing the remaining files\r\n"
"     while reporting the error.\r\n"
"  4. If no attributes parameters are specified, the current attributes of\r\n"
"     the specified files or directories are displayed.\r\n"
"  5. If no files or directories are specified, the command applies to\r\n"
"     all files and sub-directories within the current directory.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To display the attributes of a directory:\r\n"
"    fs0:\> attrib fs0:\ \r\n"
" \r\n"
"  * To display the attributes of all files and sub-directories in the current\r\n"
"    directory:\r\n"
"    fs0:\> attrib *\r\n"
" \r\n"
"  * To add the system attribute to all files with extension '.efi':\r\n"
"    fs0:\> attrib +s *.efi\r\n"
" \r\n"
"  * To remove the read-only attribute from all files with extension '.inf':\r\n"
"    fs0:\> attrib -r *.inf\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  SHELL_SUCCESS             The action was completed as requested.\r\n"
"  SHELL_NOT_FOUND           The requested file was not found.\r\n"
"  SHELL_INVALID_PARAMETER   One of the passed-in parameters was incorrectly\r\n"
"                            formatted or its value was out of bounds.\r\n"
"  SHELL_SECURITY_VIOLATION  This function was not performed due to a security\r\n"
"                            violation.\r\n"
"  SHELL_WRITE_PROTECTED     The media that the action was to take place on is\r\n"
"                            write-protected.\r\n"

#string STR_GET_HELP_CD           #language en-US ""
".TH cd 0 "Displays or changes the current directory."\r\n"
".SH NAME\r\n"
"Displays or changes the current directory.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"CD [path]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  path - Specifies the relative or absolute directory path.\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. This command changes the current working directory that is used by the\r\n"
"     UEFI Shell environment. If a file system mapping is specified, then the\r\n"
"     current working directory is changed for that device. Otherwise, the\r\n"
"     current working directory is changed for the current device.\r\n"
"  2. If path is not present, then the current working directory (including\r\n"
"     file system mapping) is displayed to standard out.\r\n"
"  3. The table below describes the conventions that are used to refer to the\r\n"
"     directory, its parent, and the root directory in the UEFI Shell\r\n"
"     environment.\r\n"
"     Convention  Description\r\n"
"      '.'        Refers to the current directory.\r\n"
"      '..'       Refers to the directory's parent.\r\n"
"      '\\\'        Refers to the root of the current file system.\r\n"
"  4. The current working directory is maintained in the environment\r\n"
"     variable %cwd%.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To change the current file system to the mapped fs0 file system:\r\n"
"    Shell> fs0:\r\n"
" \r\n"
"  * To change the current directory to subdirectory 'efi':\r\n"
"    fs0:\> cd efi\r\n"
" \r\n"
"  * To change the current directory to the parent directory (fs0:\):\r\n"
"    fs0:\efi\> cd ..\r\n"
" \r\n"
"  * To change the current directory to 'fs0:\efi\Tools':\r\n"
"    fs0:\> cd efi\Tools\r\n"
" \r\n"
"  * To change the current directory to the root of the current fs (fs0):\r\n"
"    fs0:\efi\Tools\> cd \ \r\n"
" \r\n"
"  * To move between volumes and maintain the current path, and then copy\r\n"
"    all of files in fs0:\efi\Tools into the fs1:\Tmp directory:\r\n"
"    fs0:\> cd \efi\Tools\r\n"
"    fs0:\efi\Tools\> fs1:\r\n"
"    fs1:\> cd Tmp\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  SHELL_SUCCESS             The action was completed as requested.\r\n"
"  SHELL_SECURITY_VIOLATION  This function was not performed due to a security\r\n"
"                            violation.\r\n"
"  SHELL_INVALID_PARAMETER   One of the passed-in parameters was incorrectly\r\n"
"                            formatted or its value was out of bounds.\r\n"

#string STR_GET_HELP_CP           #language en-US ""
".TH cp 0 "Copies files or directories."\r\n"
".SH NAME\r\n"
"Copies one or more files or directories to another location.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"CP [-r] [-q] src [src...] [dst]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  -r  - Makes a recursive copy.\r\n"
"  -q  - Makes a quiet copy (without a prompt).\r\n"
"  src - Specifies a source file/directory name (wildcards are permitted).\r\n"
"  dst - Specifies a destination file/directory name (wildcards are not permitted). \r\n"
"        If more than one directory is specified, the last directory is\r\n"
"        assumed to be the destination.\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. '-r' must be specified if src is a directory. If '-r' is specified,\r\n"
"     the source directory is recursively copied to the destination.\r\n"
"     'src' itself is copied.\r\n"
"  2. If a destination is not specified, the current working directory is\r\n"
"     assumed to be the destination.\r\n"
"  3. 'CP -r src1 src2 dst' copies all files and subdirectories in 'src1' and\r\n"
"     'src2' to the destination 'dst'. 'src1' and 'src2' themselves are also\r\n"
"     copied. The 'dst' parameter is interpreted as a directory.\r\n"
"  4. Copying a directory or file to itself is not allowed.\r\n"
"  5. If an error occurs, this command exits immediately and the remaining files or\r\n"
"     directories are not copied.\r\n"
"  6. When 'cp' is executed with a script file, it always performs quiet\r\n"
"     copying, regardless of whether the '-q' option is specified.\r\n"
"  7. If you are copying multiple files, the destination must be an existing\r\n"
"     directory.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To display the contents of the current directory:\r\n"
"    fs0:\> ls\r\n"
" \r\n"
"  * To copy a file in the same directory and change the file name:\r\n"
"    fs0:\> cp temp.txt readme.txt\r\n"
" \r\n"
"  * To copy multiple files to another directory:\r\n"
"    fs0:\> cp temp.txt isaBus.efi \Test\r\n"
" \r\n"
"  * To copy multiple directories recursively to another directory:\r\n"
"    fs0:\> cp -r test1 test2 boot \Test\r\n"
" \r\n"
"  * To see the results of the above operations:\r\n"
"    fs0:\> ls \Test\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  SHELL_SUCCESS             The action was completed as requested.\r\n"
"  SHELL_INVALID_PARAMETER   One of the passed-in parameters was incorrectly \r\n"
"                            formatted or its value was out of bounds.\r\n"
"  SHELL_OUT_OF_RESOURCES    There was insufficient space to save the \r\n"
"                            requested file at the destination.\r\n"
"  SHELL_SECURITY_VIOLATION  This function was not performed due to a security \r\n"
"                            violation.\r\n"
"  SHELL_WRITE_PROTECTED     An attempt was made to create a file on media that\r\n"
"                            was write-protected.\r\n"

#string STR_GET_HELP_MAP          #language en-US ""
".TH map 0 "Displays or defines file system mappings"\r\n"
".SH NAME\r\n"
"Displays or defines file system mappings.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"MAP [-d <sname>]\r\n"
"MAP [[-r][-v][-c][-f][-u][-t <type[,type...]>][sname]]\r\n"
"MAP [sname handle | mapping]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  -d      - Deletes a file system mapping.\r\n"
"  -r      - Resets file system mappings to default values.\r\n"
"  -u      - Adds file system mappings for newly-installed devices and\r\n"
"            removes mappings for uninstalled devices. This does not change\r\n"
"            the mappings of existing devices and preserves user-defined mappings.\r\n"
"  -v      - Displays verbose information about all file system mappings.\r\n"
"  -c      - Displays the consistent mappings.\r\n"
"  -f      - Displays the normal mappings (not the consistent mappings).\r\n"
"  -t      - Displays the device mappings, filtered according to the device type.\r\n"
"            Supported types are:\r\n"
"              fp - Floppy\r\n"
"              hd - Hard Disk\r\n"
"              cd - CD-ROM\r\n"
"            Types can be combined by putting a comma between two types. Spaces\r\n"
"            are not allowed between types.\r\n"
"  -sfo    - Displays information in Standard-Format Output.\r\n"
"  sname   - Specifies a mapping name.\r\n"
"  handle  - Specifies the number of a handle. Use the same value that is\r\n"
"            displayed by the 'dh' command.\r\n"
"  mapping - Specifies a new mapping name to assign to a device.\r\n"
"            This value must end with a ':'.\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. This command creates a mapping between a user-defined name and a device.\r\n"
"     The most common use of this command is to create a mapped name for\r\n"
"     devices that support a file system protocol. After these mappings are\r\n"
"     created, the names can be used with all the file manipulation commands.\r\n"
"  2. The UEFI Shell environment creates default mappings for all of the\r\n"
"     devices that support a recognized file system.\r\n"
"  3. This command can be used to create additional mappings, or \r\n"
"     when used with the -d option, to delete an existing mapping. If it is\r\n"
"     used without any parameters, all of the current mappings are listed.\r\n"
"     If the -v option is used, the mappings are shown with additional\r\n"
"     information about each device.\r\n"
"  4. The -r option is used to reset all the default mappings in a system,\r\n"
"     which is useful if the system configuration has changed since the\r\n"
"     last boot.\r\n"
"  5. The -u option adds mappings for newly-installed devices and removes\r\n"
"     mappings for uninstalled devices without changing the mappings of\r\n"
"     existing devices. User-defined mappings are also preserved. A mapping\r\n"
"     history is saved, which preserves the original mapping name for\r\n"
"     a device with a specific device path. The current directory is also\r\n"
"     preserved if the current device is not changed.\r\n"
"  6. Each device in the system has a consistent mapping. If the hardware\r\n"
"     configuration has not changed, the device's consistent mappings do not\r\n"
"     change. If two or more machines have the same hardware configurations,\r\n"
"     the device's consistent mapping remains the same. Use the -c option to\r\n"
"     list all the consistent mappings in the system.\r\n"
"  7. The mapping value must consist of digits and characters. Other\r\n"
"     characters are illegal.\r\n"
"  8. This command support wildcards. You can use the wildcards to delete\r\n"
"     or show the mapping. However, when you assign the mapping, wildcards\r\n"
"     are forbidden.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To display verbose mapping table information:\r\n"
"    Shell> map -v\r\n"
" \r\n"
"  * To assign a different name to fs0:\r\n"
"    Shell> map floppy fs0:\r\n"
" \r\n"
"  * To operate with the mapped name:\r\n"
"    Shell> floppy:\r\n"
" \r\n"
"  * To delete a mapped name:\r\n"
"    Shell> map -d floppy:\r\n"
" \r\n"
"  * To display all the mapped names starting with 'f': \r\n"
"    Shell> map f* \r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  SHELL_SUCCESS             The action was completed as requested.\r\n"
"  SHELL_SECURITY_VIOLATION  This function was not performed due to a security\r\n"
"                            violation.\r\n"
"  SHELL_INVALID_PARAMETER   One of the passed-in parameters was incorrectly\r\n"
"                            formatted or its value was out of bounds.\r\n"

#string STR_GET_HELP_MKDIR        #language en-US ""
".TH mkdir 0 "Creates directories."\r\n"
".SH NAME\r\n"
"Creates one or more new directories.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"MKDIR dir [dir...]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  dir - Specifies the name of a directory or directories to create.\r\n"
"        (Wildcards are not allowed)\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. Mkdir can create one or more new directories.\r\n"
"  2. If dir includes nested directories, then parent directories will be\r\n"
"     created before child directories.\r\n"
"  3. If the directory already exists, mkdir will exit with an error.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To create a new directory:\r\n"
"    fs0:\> mkdir rafter\r\n"
" \r\n"
"  * To create multiple directories:\r\n"
"    fs0:\> mkdir temp1 temp2\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  SHELL_SUCCESS             The action was completed as requested.\r\n"
"  SHELL_INVALID_PARAMETER   One of the passed-in parameters was incorrectly \r\n"
"                            formatted or its value was out of bounds.\r\n"
"  SHELL_OUT_OF_RESOURCES    There was insufficient space on the destination \r\n"
"                            to create the requested directory.\r\n"
"  SHELL_SECURITY_VIOLATION  This function was not performed due to a security \r\n"
"                            violation.\r\n"
"  SHELL_WRITE_PROTECTED     An attempt was made to create a directory when the\r\n"
"                            target media was write-protected.\r\n"

#string STR_GET_HELP_MV           #language en-US ""
".TH mv 0 "Moves files."\r\n"
".SH NAME\r\n"
"Moves one or more files to a destination within or between file systems.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"MV src [src...] [dst]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  src - Specifies a source file/directory name (wildcards are permitted).\r\n"
"  dst - Specifies a destination file/directory name (wildcards are permitted).\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. This command moves one or more files to a destination within or between\r\n"
"     file systems.\r\n"
"  2. If the destination is an existing directory, the sources are moved\r\n"
"     into that directory. You cannot move the sources to a non-existing\r\n"
"     directory.\r\n"
"  3. If a destination is not specified, the current directory is assumed to be\r\n"
"     the destination. If there is more than one argument on the command line,\r\n"
"     the last one is assumed to be the destination.\r\n"
"  4. Attempting to move a read-only file/directory results in an error.\r\n"
"     Moving a directory that contains read-only files is allowed.\r\n"
"  5. You cannot move a directory into itself or its subdirectories.\r\n"
"  6. You cannot move a directory if the current working directory is itself or\r\n"
"     its subdirectories.\r\n"
"  7. If an error occurs, the remaining files or directories are still be\r\n"
"     moved.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To rename a file:\r\n"
"    fs0:\> mv IsaBus.efi Bus.efi\r\n"
" \r\n"
"  * To move a directory to the current directory:\r\n"
"    fs0:\> mkdir Test1\Temp\r\n"
"    fs0:\> mv Test1\Temp\r\n"
" \r\n"
"  * To rename a directory:\r\n"
"    fs0:\> mv efi efi1.1\r\n"
" \r\n"
"  * To move multiple directories at a time:\r\n"
"    fs0:\> mv Test1 Test2 Test\r\n"
" \r\n"
"  * To attempt moving a read-only directory, which results in a failure:\r\n"
"    fs0:\Test> attrib +r Temp1\r\n"
"    DA  R fs0:\Test\Temp1\r\n"
"    fs0:\Test> mv Temp1 Temp2\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  SHELL_SUCCESS             The action was completed as requested.\r\n"
"  SHELL_SECURITY_VIOLATION  This function was not performed due to a security\r\n"
"                            violation.\r\n"
"  SHELL_INVALID_PARAMETER   One of the passed-in parameters was incorrectly\r\n"
"                            formatted or its value was out of bounds.\r\n"
"  SHELL_NOT_FOUND           The source file was not able to be found.\r\n"
"  SHELL_OUT_OF_RESOURCES    There was insufficient free space to move the\r\n"
"                            requested file to its destination.\r\n"
"  SHELL_WRITE_PROTECTED     An attempt was made to create a file on media that\r\n"
"                            was write-protected.\r\n"

#string STR_GET_HELP_PARSE        #language en-US ""
".TH parse 0 "Parses standard format output files."\r\n"
".SH NAME\r\n"
"Retrieves a value from a standard format output file.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"PARSE filename tablename column [-i <Instance>] [-s <Instance>]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  filename      - Specifies a source file name.\r\n"
"  tablename     - Specifies the name of the table to be parsed.\r\n"
"  column        - Specifies the one-based column index to use to determine which value\r\n"
"                  from a particular record to parse.\r\n"
"  -i <Instance> - Specifies an instance number to use to start parsing the ShellCommand table,\r\n"
"                  and then the specified tablename. If not specified, all instances are returned.\r\n"
"  -s <Instance> - Specifies an instance number to use to start parsing the ShellCommand\r\n"
"                  table. If not present, then 1 is assumed.\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. This command enables you to parse data from a file that has been output\r\n"
"     using the -sfo parameter.\r\n"
"  2. Since the standard formatted output has a well known means of parsing,\r\n"
"     this command is intended to provide an easy way of enabling\r\n"
"     scripts to consume retrieved data from such constructed output files, and\r\n"
"     use it in the logic of scripts written for the UEFI shell.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * The following data is contained in a temporary file (temp.txt):\r\n"
"    ShellCommand,"ls"\r\n"
"    VolumeInfo,"MikesVolume","400000000","FALSE","32000000","16000000"\r\n"
"    FileInfo,"FS0:\efi\\boot\winloader.efi","45670","45900","arsh","08:30:12","01.08.2013","00:00:00","01.08.2013","08:30:12","01.08.2013"\r\n"
"    FileInfo,"FS0:\efi\\boot\mikesfile.txt","1250","1280","a","08:30:12","01.08.2013","00:00:00","01.08.2013","08:30:12","01.08.2013"\r\n"
"    FileInfo,"FS0:\efi\\boot\\readme.txt","795","900","a","08:30:12","01.08.2013","00:00:00","01.08.2013","08:30:12","01.08.2013"\r\n"
" \r\n"
"  * To display VolumeInfo column 2 in temp.txt:\r\n"
"    fs0:\> parse temp.txt VolumeInfo 2\r\n"
"    MikesVolume\r\n"
" \r\n"
"  * To display FileInfo column 3 in temp.txt, starting with instance 3:\r\n"
"    fs0:\> parse temp.txt FileInfo 3 -i 3\r\n"
"    795\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  SHELL_SUCCESS             The action was completed as requested.\r\n"
"  SHELL_SECURITY_VIOLATION  This function was not performed due to a security\r\n"
"                            violation.\r\n"
"  SHELL_INVALID_PARAMETER   One of the passed-in parameters was incorrectly\r\n"
"                            formatted or its value was out of bounds.\r\n"
"  SHELL_NOT_FOUND           The source file was not able to be found.\r\n"

#string STR_GET_HELP_RESET        #language en-US ""
".TH reset 0 "Reset the system."\r\n"
".SH NAME\r\n"
"Resets the system.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"RESET [-w [string]]\r\n"
"RESET [-s [string]]\r\n"
"RESET [-c [string]]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  -s     - Performs a shutdown.\r\n"
"  -w     - Performs a warm boot.\r\n"
"  -c     - Performs a cold boot.\r\n"
"  string - Describes a reason for the reset.\r\n"
"  -fwui  - If the system firmware supports it, perform a reset back\r\n"
"           to the firmware user interface (FW UI)"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. This command resets the system.\r\n"
"  2. The default is to perform a cold reset unless the -w parameter is\r\n"
"     specified.\r\n"
"  3. If a reset string is specified, it is passed into the Reset() \r\n"
"     function, and the system records the reason for the system reset.\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  SHELL_INVALID_PARAMETER   One of the passed in parameters was incorrectly \r\n"
"                            formatted or its value was out of bounds.\r\n"

#string STR_GET_HELP_RM           #language en-US ""
".TH rm 0 "Deletes one or more files or directories."\r\n"
".SH NAME\r\n"
"Deletes one or more files or directories.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"RM [-q] file/directory [file/directory ...]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  -q        - Specifies quiet mode. Does not prompt for a confirmation.\r\n"
"  file      - Specifies a file name (wildcards are permitted).\r\n"
"  directory - Specifies a directory name (wildcards are permitted).\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. This command deletes one or more files or directories.\r\n"
"  2. If the target is a directory, it deletes the directory, including all\r\n"
"     its subdirectories.\r\n"
"  3. Redirecting a file whose parent directory (or the file\r\n"
"     itself) is being deleted is not allowed.\r\n"
"  4. Removing a read-only file/directory results in a failure.\r\n"
"  5. Removing a directory containing read-only file(s) results in\r\n"
"     a failure. If an error occurs, the command exits immediately and stops\r\n"
"     removing files/directories.\r\n"
"  6. You cannot remove a directory when the current directory is itself or its\r\n"
"     subdirectory. If a file contains wildcards, you are not prompted for\r\n"
"     confirmation.\r\n"
"  7. The root directory cannot be removed.\r\n"
"  8. The current directory or its ancestor directories cannot be removed.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To remove multiple directories at a time:\r\n"
"    fs0:\> rm Test\Temp1 Temp2\r\n"
" \r\n"
"  * To remove multiple directories with wildcards:\r\n"
"    fs0:\> rm Test\Temp*\r\n"
" \r\n"
"  * To attempt removing a directory that contains a read-only file,\r\n"
"    which results in a failure:\r\n"
"    fs0:\> attrib +r Test\Temp1\readme.txt\r\n"
"    A R fs0:\Test\Temp1\readme.txt\r\n"
"    fs0:\> rm Test\Temp1\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  SHELL_SUCCESS             The action was completed as requested.\r\n"
"  SHELL_SECURITY_VIOLATION  This function was not performed due to a security\r\n"
"                            violation.\r\n"
"  SHELL_NOT_FOUND           The source file was not able to be found.\r\n"
"  SHELL_WRITE_PROTECTED     The target was write protected.\r\n"

#string STR_GET_HELP_SET          #language en-US ""
".TH set 0 "Displays or modifies UEFI Shell environment variables."\r\n"
".SH NAME\r\n"
"Displays or modifies UEFI Shell environment variables.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"SET [-v] [sname [value]]\r\n"
"SET [-d <sname>]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  -d    - Deletes the environment variable.\r\n"
"  -v    - Displays or modifies a volatile variable.\r\n"
"  sname - Specifies an environment variable name.\r\n"
"  value - Specifies an environment variable value.\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. This command is used to maintain the UEFI Shell environment variables.\r\n"
"     This command can do the following:\r\n"
"       - Display environment variables.\r\n"
"       - Create new environment variables.\r\n"
"       - Change the value of existing environment variables.\r\n"
"       - Delete environment variables.\r\n"
"  2. This command sets an environment variable to a specified \r\n"
"     value. You can use it to create a new environment\r\n"
"     variable or to modify an existing environment variable.\r\n"
"  3. If used without any parameters, all the environment variables\r\n"
"     are displayed.\r\n"
"  4. If used with the -d option, the environment variable that\r\n"
"     is specified by sname is deleted.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To add an environment variable:\r\n"
"    Shell> set DiagnosticPath fs0:\efi\diag;fs1:\efi\diag\r\n"
" \r\n"
"  * To display environment variables:\r\n"
"    Shell> set\r\n"
" \r\n"
"  * To delete an environment variable:\r\n"
"    Shell> set -d diagnosticpath\r\n"
" \r\n"
"  * To change an environment variable:\r\n"
"    fs0:\> set src efi\r\n"
"    fs0:\> set src efi1.1\r\n"
" \r\n"
"  * To append an environment variable:\r\n"
"    Shell> set path %path%;fs0:\efi\Tools;fs0:\efi\boot;fs0:\\r\n"
" \r\n"
"  * To set a volatile variable that will disappear at the next boot:\r\n"
"    Shell> set -v EFI_SOURCE c:\project\EFI1.1\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  SHELL_SUCCESS             The action was completed as requested.\r\n"
"  SHELL_SECURITY_VIOLATION  This function was not performed due to a security\r\n"
"                            violation.\r\n"
"  SHELL_OUT_OF_RESOURCES    A request to set a variable in a non-volatile \r\n"
"                            fashion could not be completed. The resulting \r\n"
"                            non-volatile request has been converted into a \r\n"
"                            volatile request.\r\n"

#string STR_GET_HELP_DATE         #language en-US ""
".TH date 0 "Displays and sets the current date for the system."\r\n"
".SH NAME\r\n"
"Displays and sets the current date for the system.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"DATE [mm/dd/[yy]yy][-sfo]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  -sfo    - Displays information in Standard-Format Output.\r\n"
"  mm      - Specifies the month of the date to be set. (1-12)\r\n"
"  dd      - Specifies the day of the date to be set (1-31)\r\n"
"  yy/yyyy - Specifies the year of the date to be set. If only two digits,\r\n"
"            then enter 9x = 199x. Otherwise enter 20xx.\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. This command displays and/or sets the current date for the system.\r\n"
"     If no parameters are used, it shows the current date. If a valid month,\r\n"
"     day, and year are specified, the system's date is updated.\r\n"
"     The following rules apply:\r\n"
"       - Except for numeric characters and /, all other characters in the\r\n"
"         argument are invalid.\r\n"
"       - The Shell reports an error if the number is in the wrong\r\n"
"         month/date/year range.\r\n"
"       - A space before or after the numeric character is not allowed. Inserting\r\n"
"         a space into the number is invalid.\r\n"
"       - Repeated zeros are allowed before the number. For example:\r\n"
"           Shell > date 0000008/000004/000097\r\n"
"           Shell > date\r\n"
"           08/04/2097\r\n"
"           Shell >\r\n"
"       - The year range must be greater than or equal to 1998.\r\n"
"       - Two numeric characters indicate the year. Numbers below 98 are\r\n"
"         regarded as 20xx, and numbers equal to or above 98 are regarded as\r\n"
"         19xx. 00 means 2000. For example:\r\n"
"           Shell > date 8/4/97\r\n"
"           Shell > date\r\n"
"           08/04/2097\r\n"
"           Shell >\r\n"
"           Shell > date 8/4/98\r\n"
"           Shell > date\r\n"
"           08/04/1998\r\n"
"           Shell >\r\n"
"  2. The range of valid years is from 1998-2099.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To display the current date in the system:\r\n"
"    fs0:\> date\r\n"
" \r\n"
"  * To set the date with long year format:\r\n"
"    fs0:\> date 01/01/2050\r\n"
" \r\n"
"  * To set the date with short year format:\r\n"
"    fs0:\> date 06/18/01\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  SHELL_SUCCESS             The action was completed as requested.\r\n"
"  SHELL_DEVICE_ERROR        There was a hardware error preventing the\r\n"
"                            completion of this command.\r\n"
"  SHELL_SECURITY_VIOLATION  This function was not performed due to a security\r\n"
"                            violation.\r\n"
"  SHELL_INVALID_PARAMETER   One of the passed-in parameters was incorrectly\r\n"
"                            formatted or its value was out of bounds.\r\n"

#string STR_GET_HELP_TIME         #language en-US ""
".TH time 0 "Displays or sets the time for the system."\r\n"
".SH NAME\r\n"
"Displays or sets the current time for the system.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"TIME [hh:mm[:ss]] [-tz tz] [-d dl]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  -d  - Sets or displays a daylight savings time value.\r\n"
"  -tz - Specifies a time zone adjustment, measured in minutes offset from UTC. Valid values\r\n"
"        are between -1440 and 1440 or 2047. If not present or set to 2047,\r\n"
"        time is interpreted as local time.\r\n"
"  hh  - Specifies a new hour (0-23) (required).\r\n"
"  mm  - Specifies a new minute (0-59) (required).\r\n"
"  ss  - Specifies a new second (0-59). If not specified, zero is used.\r\n"
"  dl  - Specifies a daylight saving time value to set.\r\n"
"          0 : Time is not affected.\r\n"
"          1 : Time is affected, and has not been adjusted for daylight\r\n"
"              savings.\r\n"
"          3 : Time is affected, and has been adjusted for daylight savings.\r\n"
"        All other values are invalid. If no value follows -d, the\r\n"
"        current daylight savings time is displayed.\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. This command displays or sets the current time for the system.\r\n"
"     If no parameters are used, it shows the current time. If valid hours, \r\n"
"     minutes, and seconds are provided, the system time is\r\n"
"     updated. Note the following rules:\r\n"
"       - Except for numeric characters and the : character, all other\r\n"
"         characters in the argument are invalid.\r\n"
"       - The Shell reports an error if the number is in the wrong \r\n"
"         hour/minute/second range.\r\n"
"       - Spaces before or after the numeric character and spaces inserted into\r\n"
"         the number are not allowed.\r\n"
"       - Repeated zeros are allowed before the number. For example:\r\n"
"           Shell> time 00000017:000004:0000\r\n"
"           Shell> time\r\n"
"           17:04:00 (UTC+08:00)\r\n"
"  2. The seconds parameter is optional. If none is specified, it is\r\n"
"     set to zero.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To display current system time:\r\n"
"    fs0:\> time\r\n"
" \r\n"
"  * To set the system time:\r\n"
"    fs0:\> time 9:51:30\r\n"
" \r\n"
"  * To display the system time, including daylight savings time:\r\n"
"    fs0:\> time -d\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  SHELL_SUCCESS             The action was completed as requested.\r\n"
"  SHELL_DEVICE_ERROR        There was a hardware error preventing the\r\n"
"                            completion of this command\r\n"
"  SHELL_SECURITY_VIOLATION  This function was not performed due to a security\r\n"
"                            violation.\r\n"
"  SHELL_INVALID_PARAMETER   One of the passed-in parameters was incorrectly\r\n"
"                            formatted or its value was out of bounds.\r\n"

#string STR_GET_HELP_TIMEZONE     #language en-US ""
".TH timezone 0 "Displays or sets time zone information."\r\n"
".SH NAME\r\n"
"Displays or sets time zone information.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"TIMEZONE [-s hh:mm | -l] [-b] [-f]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  -s - Sets the time zone associated with hh:mm offset from UTC.\r\n"
"  -l - Displays a list of all time zones.\r\n"
"  -b - Displays one screen at a time.\r\n"
"  -f - Displays full information for the specified time zone.\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. This command displays and sets the current time zone for the system.\r\n"
"  2. If no parameters are used, it shows the current time zone.\r\n"
"  3. If a valid hh:mm parameter is provided, the time zone\r\n"
"     information is updated.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To display all available time zones:\r\n"
"    Shell> timezone -l\r\n"
" \r\n"
"  * To set the time zone:\r\n"
"    Shell> timezone -s -7:00\r\n"
" \r\n"
"  * To display detailed information for the current time zone:\r\n"
"    Shell> timezone -f\r\n"

#string STR_GET_HELP_LS           #language en-US ""
".TH ls 0 "Lists the contents of a directory or file information."\r\n"
".SH NAME\r\n"
"Lists the contents of a directory or file information.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"LS [-r] [-a[attrib]][-sfo][file]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  -r     - Displays recursively (including subdirectories).\r\n"
"  -a     - Displays files with a specified attribute. If \r\n"
"           attribute is not specified, all files are listed. If -a is not\r\n"
"           specified, all non-system and non-hidden files are listed.\r\n"
"  -sfo   - Displays information in Standard-Format Output.\r\n"
"  attrib - Specifies a file attribute list value:\r\n"
"             a - Archive\r\n"
"             s - System\r\n"
"             h - Hidden\r\n"
"             r - Read-only\r\n"
"             d - Directory\r\n"
"  file   - Specifies a name of a file or directory (wildcards are permitted).\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. This command lists directory contents or file information. If no file\r\n"
"     name or directory name is specified, the current working directory\r\n"
"     is assumed.\r\n"
"  2. The contents of a directory are listed if all of the following are true:\r\n"
"       - If option -r is not specified.\r\n"
"       - If no wildcard characters are specified in the file parameter.\r\n"
"       - If the file specified represents an existing directory.\r\n"
"  3. In all other cases, the command functions as follows:\r\n"
"       - All files/directories that match the specified name are displayed.\r\n"
"       - The -r flag determines whether a recursive search is performed.\r\n"
"       - The option flag -a[attrib] only displays those\r\n"
"         files with the attributes that are specified.\r\n"
"       - If more than one attribute is specified, only the files that have all\r\n"
"         those attributes are listed.\r\n"
"       - If -a is followed by nothing, then all files/directories are\r\n"
"         displayed, regardless of their attributes.\r\n"
"       - If -a itself is not specified, then all files except system and\r\n"
"         hidden files are displayed.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To hide files by adding the hidden or system attribute to them:\r\n"
"    fs0:\> attrib +s +h *.efi\r\n"
" \r\n"
"  * To display all, except the files/directories with 'h' or 's' attribute:\r\n"
"    fs0:\> ls\r\n"
" \r\n"
"  * To display files with all attributes in the current directory:\r\n"
"    fs0:\> ls -a\r\n"
" \r\n"
"  * To display files with read-only attributes in the current directory:\r\n"
"    fs0:\> ls -ar\r\n"
" \r\n"
"  * To display the files with attribute of 's':\r\n"
"    fs0:\> ls -as isabus.efi\r\n"
" \r\n"
"  * To display all in fs0:\efi directory recursively:\r\n"
"    fs0:\> ls -r -a efi\r\n"
" \r\n"
"  * To display files with a specified type in the current directory: \r\n"
"    recursively:\r\n"
"    fs0:\> ls -r -a *.efi -b\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  SHELL_SUCCESS             The action was completed as requested.\r\n"
"  SHELL_INVALID_PARAMETER   One of the passed-in parameters was incorrectly\r\n"
"                            formatted or its value was out of bounds.\r\n"
"  SHELL_SECURITY_VIOLATION  This function was not performed due to a security\r\n"
"                            violation.\r\n"
"  SHELL_NOT_FOUND           The requested file or directory was not found.\r\n"

#string STR_GET_HELP_LOAD         #language en-US ""
".TH load 0 "Loads a UEFI driver into memory."\r\n"
".SH NAME\r\n"
"Loads a UEFI driver into memory.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"LOAD [-nc] file [file...]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  -nc  - Loads the driver, but does not connect the driver.\r\n"
"  File - Specifies a file that contains the image of the UEFI driver (wildcards are\r\n"
"         permitted).\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. This command loads a driver into memory. It can load multiple files at\r\n"
"     one time. The file name supports wildcards.\r\n"
"  2. If the -nc flag is not specified, this command attempts to connect the\r\n"
"     driver to a proper device. It might also cause previously loaded drivers\r\n"
"     to be connected to their corresponding devices.\r\n"
"  3. Use the 'UNLOAD' command to unload a driver.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To load a driver:\r\n"
"    fs0:\> load Isabus.efi\r\n"
" \r\n"
"  * To load multiple drivers:\r\n"
"    fs0:\> load Isabus.efi IsaSerial.efi\r\n"
" \r\n"
"  * To load multiple drivers using file name wildcards:\r\n"
"    fs0:\> load Isa*.efi\r\n"
" \r\n"
"  * To load a driver without connecting it to a device:\r\n"
"    fs0:\> load -nc IsaBus.efi\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  SHELL_SUCCESS              The action was completed as requested.\r\n"
"  SHELL_INVALID_PARAMETER    One of the passed-in parameters was incorrectly\r\n"
"                             formatted or its value was out of bounds.\r\n"
"  SHELL_SECURITY_VIOLATION   This function was not performed due to a security\r\n"
"                             violation.\r\n"
"  SHELL_NOT_FOUND            The requested file was not found.\r\n"

#string STR_GET_HELP_VOL         #language en-US ""
".TH vol 0 "Displays or modifies information about a disk volume."\r\n"
".SH NAME\r\n"
"Displays or modifies information about a disk volume.\r\n"
".SH SYNOPSIS\r\n"
" \r\n"
"VOL [fs] [-n <VolumeLabel>]\r\n"
"VOL [fs] [-d]\r\n"
".SH OPTIONS\r\n"
" \r\n"
"  -n          - Displays or modifies a new volume label.\r\n"
"  -d          - Displays or modifies an empty volume label.\r\n"
"  fs          - Specifies the name of the file system.\r\n"
"  VolumeLabel - Specifies a volume label.\r\n"
".SH DESCRIPTION\r\n"
" \r\n"
"NOTES:\r\n"
"  1. The following characters cannot be used in a volume label:\r\n"
"     % ^ * + = [ ] | : ; \" < > ? / . \r\n"
"  2. No spaces are allowed in a volume label.\r\n"
"  3. This command displays the volume information for the specified file\r\n"
"     system. If fs is not specified, the current file system is used.\r\n"
"  4. If -n is specified, the volume label for fs is set to\r\n"
"     VolumeLabel.\r\n"
"  5. The maximum length for volume label is 11 characters.\r\n"
".SH EXAMPLES\r\n"
" \r\n"
"EXAMPLES:\r\n"
"  * To display the volume of the current file system:\r\n"
"    fs0:\> vol\r\n"
" \r\n"
"  * To change the label of fs0:\r\n"
"    Shell> vol fs0 -n help_test\r\n"
" \r\n"
"  * To delete the volume label of fs0:\r\n"
"    fs0:\> vol fs0 -d\r\n"
".SH RETURNVALUES\r\n"
" \r\n"
"RETURN VALUES:\r\n"
"  SHELL_SUCCESS              The action was completed as requested.\r\n"
"  SHELL_INVALID_PARAMETER    One of the passed-in parameters was incorrectly\r\n"
"                             formatted or its value was out of bounds.\r\n"
"  SHELL_SECURITY_VIOLATION   This function was not performed due to a security\r\n"
"                             violation.\r\n"
"  SHELL_NOT_FOUND            The target file-system was not found.\r\n"

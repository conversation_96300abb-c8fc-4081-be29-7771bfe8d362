## @file
#  Custom FDT Node Parse Library.
#
#  Copyright (c) 2024, Intel Corporation. All rights reserved.<BR>
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = CustomFdtNodeParserLibNull
  FILE_GUID                      = 386496E4-37DB-4531-BA0C-16D126E63C55
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = CustomFdtNodeParserLib

#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources]
  CustomFdtNodeParserNullLib.c

[Packages]
  MdePkg/MdePkg.dec

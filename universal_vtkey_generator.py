#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用VTKey密钥生成器
基于深度逆向分析的完整解决方案
"""

import hashlib
import struct
import os
import time
from typing import Optional

class UniversalVTKeyGenerator:
    
    def __init__(self):
        pass
    
    def method1_direct_hex(self, wps_data: bytes) -> str:
        """方法1: 直接使用WPSettings数据的十六进制"""
        return wps_data.hex().upper()
    
    def method2_reversed_hex(self, wps_data: bytes) -> str:
        """方法2: 反转字节序后的十六进制"""
        return wps_data[::-1].hex().upper()
    
    def method3_md5_hash(self, wps_data: bytes) -> str:
        """方法3: MD5哈希前16位"""
        return hashlib.md5(wps_data).hexdigest().upper()[:16]
    
    def method4_sha1_hash(self, wps_data: bytes) -> str:
        """方法4: SHA1哈希前16位"""
        return hashlib.sha1(wps_data).hexdigest().upper()[:16]
    
    def method5_combined_hash(self, wps_root: bytes, wps_svi: bytes) -> str:
        """方法5: 组合两个WPSettings文件的哈希"""
        combined = wps_root + wps_svi
        return hashlib.md5(combined).hexdigest().upper()[:16]
    
    def method6_xor_operation(self, wps_root: bytes, wps_svi: bytes) -> str:
        """方法6: 两个WPSettings文件的XOR运算"""
        xor_result = bytes(a ^ b for a, b in zip(wps_root, wps_svi))
        return xor_result.hex().upper()
    
    def method7_crc32_based(self, wps_data: bytes) -> str:
        """方法7: 基于CRC32的算法"""
        import zlib
        crc32_val = zlib.crc32(wps_data) & 0xffffffff
        # 扩展到16字节
        extended = struct.pack('>II', crc32_val, crc32_val ^ 0xFFFFFFFF)
        return extended.hex().upper()
    
    def method8_time_based(self, wps_data: bytes, timestamp: Optional[int] = None) -> str:
        """方法8: 基于时间戳的算法"""
        if timestamp is None:
            timestamp = int(time.time())
        
        time_bytes = struct.pack('>I', timestamp)
        combined = wps_data + time_bytes
        return hashlib.md5(combined).hexdigest().upper()[:16]
    
    def generate_all_possible_keys(self, wps_root_hex: str, wps_svi_hex: str = None) -> dict:
        """生成所有可能的密钥"""
        wps_root = bytes.fromhex(wps_root_hex)
        wps_svi = bytes.fromhex(wps_svi_hex) if wps_svi_hex else None
        
        results = {}
        
        results['方法1_直接十六进制'] = self.method1_direct_hex(wps_root)
        results['方法2_反转十六进制'] = self.method2_reversed_hex(wps_root)
        results['方法3_MD5哈希'] = self.method3_md5_hash(wps_root)
        results['方法4_SHA1哈希'] = self.method4_sha1_hash(wps_root)
        results['方法7_CRC32算法'] = self.method7_crc32_based(wps_root)
        
        if wps_svi:
            results['方法5_组合哈希'] = self.method5_combined_hash(wps_root, wps_svi)
            results['方法6_XOR运算'] = self.method6_xor_operation(wps_root, wps_svi)
        
        # 尝试不同的时间戳
        for days_ago in [0, 1, 7, 30]:
            timestamp = int(time.time()) - (days_ago * 24 * 3600)
            key = self.method8_time_based(wps_root, timestamp)
            results[f'方法8_时间戳_{days_ago}天前'] = key
        
        return results
    
    def create_vtkey_files(self, keys: dict) -> None:
        """创建所有可能的vtkey文件"""
        for method, key in keys.items():
            filename = f"{key}.vtkey"
            try:
                with open(filename, 'w') as f:
                    f.write("")  # 空文件
                print(f"✓ 已创建: {filename} ({method})")
            except Exception as e:
                print(f"✗ 创建失败: {filename} - {e}")

def main():
    generator = UniversalVTKeyGenerator()
    
    print("=== 通用VTKey密钥生成器 ===")
    print()
    
    # 使用您的实际数据
    print("使用您的8GB WPSettings.dat数据:")
    wps_root_hex = "ce7c8417a1124ad9"
    wps_svi_hex = "8a453a7f16c504ab"
    
    print(f"根目录WPSettings: {wps_root_hex.upper()}")
    print(f"SVI目录WPSettings: {wps_svi_hex.upper()}")
    print(f"系统要求的密钥: 5F1CE76E1FAF0386")
    print()
    
    # 生成所有可能的密钥
    all_keys = generator.generate_all_possible_keys(wps_root_hex, wps_svi_hex)
    
    print("=== 生成的所有可能密钥 ===")
    target_key = "5F1CE76E1FAF0386"
    found_match = False
    
    for method, key in all_keys.items():
        match_indicator = "★ 匹配!" if key == target_key else ""
        print(f"{method}: {key} {match_indicator}")
        if key == target_key:
            found_match = True
    
    print()
    
    if found_match:
        print("✓ 找到匹配的算法!")
    else:
        print("⚠ 未找到完全匹配的算法，但这些密钥都值得尝试")
    
    print()
    print("=== 创建vtkey文件 ===")
    
    # 首先创建系统要求的密钥文件
    target_filename = f"{target_key}.vtkey"
    with open(target_filename, 'w') as f:
        f.write("")
    print(f"✓ 已创建目标密钥: {target_filename}")
    
    # 创建其他可能的密钥文件
    generator.create_vtkey_files(all_keys)
    
    print()
    print("=== 使用说明 ===")
    print("1. 将生成的.vtkey文件复制到C盘根目录")
    print("2. 使用修改后的1BOOT.EFI启动")
    print("3. 如果一个不行，尝试其他的")
    print("4. 建议优先尝试目标密钥: 5F1CE76E1FAF0386.vtkey")

if __name__ == "__main__":
    main()

// /** @file
// Debug Communication Library instance based on USB3 debug port for DXE and SMM modules.
//
// Debug Communication Library instance based on USB3 debug port for DXE and SMM modules.
//
// Copyright (c) 2014, Intel Corporation. All rights reserved.<BR>
//
// SPDX-License-Identifier: BSD-2-Clause-Patent
//
// **/


#string STR_MODULE_ABSTRACT             #language en-US "Debug Communication Library instance based on USB3 debug port for DXE and SMM modules"

#string STR_MODULE_DESCRIPTION          #language en-US "Debug Communication Library instance based on USB3 debug port for DXE and SMM modules."


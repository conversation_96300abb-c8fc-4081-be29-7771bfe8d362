## @file
# Bootloader Payload Package
#
# Provides drivers and definitions to create uefi payload for bootloaders.
#
# Copyright (c) 2014 - 2020, Intel Corporation. All rights reserved.<BR>
# Copyright (c) 2024, Google LLC.
# SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
DEFINE FD_BASE       = 0x00800000
DEFINE FD_BLOCK_SIZE = 0x00001000

!if $(TARGET) == "NOOPT"
DEFINE FD_SIZE     = 0x00850000
DEFINE NUM_BLOCKS  = 0x850
!else

DEFINE FD_SIZE     = 0x00700000
DEFINE NUM_BLOCKS  = 0x700
!endif

################################################################################
[FD.UefiPayload]
BaseAddress   = $(FD_BASE) | gUefiPayloadPkgTokenSpaceGuid.PcdPayloadFdMemBase
Size          = $(FD_SIZE) | gUefiPayloadPkgTokenSpaceGuid.PcdPayloadFdMemSize
ErasePolarity = 1
BlockSize     = $(FD_BLOCK_SIZE)
NumBlocks     = $(NUM_BLOCKS)

0x00000000|$(FD_SIZE)
FV = PLDFV

################################################################################
[FV.PLDFV]
FvNameGuid         = 96E75986-6FDD-491E-9FD5-35E21AC45B45
BlockSize          = $(FD_BLOCK_SIZE)
FvAlignment        = 16
ERASE_POLARITY     = 1
MEMORY_MAPPED      = TRUE
STICKY_WRITE       = TRUE
LOCK_CAP           = TRUE
LOCK_STATUS        = TRUE
WRITE_DISABLED_CAP = TRUE
WRITE_ENABLED_CAP  = TRUE
WRITE_STATUS       = TRUE
WRITE_LOCK_CAP     = TRUE
WRITE_LOCK_STATUS  = TRUE
READ_DISABLED_CAP  = TRUE
READ_ENABLED_CAP   = TRUE
READ_STATUS        = TRUE
READ_LOCK_CAP      = TRUE
READ_LOCK_STATUS   = TRUE

!if $(UNIVERSAL_PAYLOAD) == FALSE
INF UefiPayloadPkg/UefiPayloadEntry/UefiPayloadEntry.inf
!endif
FILE FV_IMAGE = 4E35FD93-9C72-4c15-8C4B-E77F1DB2D793 {
    SECTION FV_IMAGE = DXEFV
}
!if $(UNIVERSAL_PAYLOAD) == TRUE
!if $(NETWORK_DRIVER_ENABLE) == TRUE
################################################################################
[FV.NETWORKFV]
FvNameGuid         = 3CF5CB69-C439-4B84-AF11-8167B22D7221
BlockSize          = $(FD_BLOCK_SIZE)
FvForceRebase      = FALSE
FvAlignment        = 16
ERASE_POLARITY     = 1
MEMORY_MAPPED      = TRUE
STICKY_WRITE       = TRUE
LOCK_CAP           = TRUE
LOCK_STATUS        = TRUE
WRITE_DISABLED_CAP = TRUE
WRITE_ENABLED_CAP  = TRUE
WRITE_STATUS       = TRUE
WRITE_LOCK_CAP     = TRUE
WRITE_LOCK_STATUS  = TRUE
READ_DISABLED_CAP  = TRUE
READ_ENABLED_CAP   = TRUE
READ_STATUS        = TRUE
READ_LOCK_CAP      = TRUE
READ_LOCK_STATUS   = TRUE

#
# UEFI network modules
#
!include NetworkPkg/Network.fdf.inc
!endif

################################################################################
[FV.BDSFV]
FvNameGuid         = CA5590AF-9558-4822-B5EA-BE2E876CD3EC
BlockSize          = $(FD_BLOCK_SIZE)
FvForceRebase      = FALSE
FvAlignment        = 16
ERASE_POLARITY     = 1
MEMORY_MAPPED      = TRUE
STICKY_WRITE       = TRUE
LOCK_CAP           = TRUE
LOCK_STATUS        = TRUE
WRITE_DISABLED_CAP = TRUE
WRITE_ENABLED_CAP  = TRUE
WRITE_STATUS       = TRUE
WRITE_LOCK_CAP     = TRUE
WRITE_LOCK_STATUS  = TRUE
READ_DISABLED_CAP  = TRUE
READ_ENABLED_CAP   = TRUE
READ_STATUS        = TRUE
READ_LOCK_CAP      = TRUE
READ_LOCK_STATUS   = TRUE

INF MdeModulePkg/Universal/BdsDxe/BdsDxe.inf
INF RuleOverride = UI MdeModulePkg/Application/UiApp/UiApp.inf
INF MdeModulePkg/Application/BootManagerMenuApp/BootManagerMenuApp.inf
!endif

[FV.DXEFV]
FvNameGuid         = 8063C21A-8E58-4576-95CE-089E87975D23
BlockSize          = $(FD_BLOCK_SIZE)
FvForceRebase      = FALSE
FvAlignment        = 16
ERASE_POLARITY     = 1
MEMORY_MAPPED      = TRUE
STICKY_WRITE       = TRUE
LOCK_CAP           = TRUE
LOCK_STATUS        = TRUE
WRITE_DISABLED_CAP = TRUE
WRITE_ENABLED_CAP  = TRUE
WRITE_STATUS       = TRUE
WRITE_LOCK_CAP     = TRUE
WRITE_LOCK_STATUS  = TRUE
READ_DISABLED_CAP  = TRUE
READ_ENABLED_CAP   = TRUE
READ_STATUS        = TRUE
READ_LOCK_CAP      = TRUE
READ_LOCK_STATUS   = TRUE

APRIORI DXE {
  INF  MdeModulePkg/Universal/DevicePathDxe/DevicePathDxe.inf
  INF  MdeModulePkg/Universal/PCD/Dxe/Pcd.inf
  INF  MdeModulePkg/Universal/ReportStatusCodeRouter/RuntimeDxe/ReportStatusCodeRouterRuntimeDxe.inf
  INF  MdeModulePkg/Universal/StatusCodeHandler/RuntimeDxe/StatusCodeHandlerRuntimeDxe.inf
  INF  UefiPayloadPkg/BlSupportDxe/BlSupportDxe.inf
}

#
# DXE Phase modules
#
INF MdeModulePkg/Core/Dxe/DxeMain.inf
INF MdeModulePkg/Universal/PCD/Dxe/Pcd.inf
INF MdeModulePkg/Universal/ReportStatusCodeRouter/RuntimeDxe/ReportStatusCodeRouterRuntimeDxe.inf
INF MdeModulePkg/Universal/StatusCodeHandler/RuntimeDxe/StatusCodeHandlerRuntimeDxe.inf

!if "X64" IN "$(ARCH)"
  INF UefiCpuPkg/CpuDxe/CpuDxe.inf
  INF MdeModulePkg/Universal/Metronome/Metronome.inf
  INF PcAtChipsetPkg/PcatRealTimeClockRuntimeDxe/PcatRealTimeClockRuntimeDxe.inf

  !if $(CRYPTO_PROTOCOL_SUPPORT) == TRUE
    !if $(CRYPTO_DRIVER_EXTERNAL_SUPPORT) == FALSE
      INF CryptoPkg/Driver/CryptoDxe.inf
    !endif
  !endif

  !if $(TIMER_SUPPORT) == "HPET"
    INF PcAtChipsetPkg/HpetTimerDxe/HpetTimerDxe.inf
  !elseif $(TIMER_SUPPORT) == "LAPIC"
    INF OvmfPkg/LocalApicTimerDxe/LocalApicTimerDxe.inf
  !endif
!elseif "AARCH64" IN "$(ARCH)"
  INF ArmPkg/Drivers/CpuDxe/CpuDxe.inf
  INF ArmPkg/Drivers/ArmGicDxe/ArmGicDxe.inf
  INF ArmPkg/Drivers/TimerDxe/TimerDxe.inf

  INF EmbeddedPkg/RealTimeClockRuntimeDxe/RealTimeClockRuntimeDxe.inf
  INF EmbeddedPkg/MetronomeDxe/MetronomeDxe.inf
  INF OvmfPkg/PlatformHasAcpiDtDxe/PlatformHasAcpiDtDxe.inf
  INF UefiCpuPkg/CpuMmio2Dxe/CpuMmio2Dxe.inf
  INF OvmfPkg/Virtio10Dxe/Virtio10.inf
  INF OvmfPkg/VirtioPciDeviceDxe/VirtioPciDeviceDxe.inf
  INF OvmfPkg/VirtioBlkDxe/VirtioBlk.inf
  INF OvmfPkg/VirtioGpuDxe/VirtioGpu.inf

  INF OvmfPkg/QemuRamfbDxe/QemuRamfbDxe.inf
  INF OvmfPkg/PlatformDxe/Platform.inf
!endif

!if $(SECURITY_STUB_ENABLE) == TRUE
  INF MdeModulePkg/Universal/SecurityStubDxe/SecurityStubDxe.inf
!endif

INF MdeModulePkg/Core/RuntimeDxe/RuntimeDxe.inf
INF MdeModulePkg/Universal/CapsuleRuntimeDxe/CapsuleRuntimeDxe.inf
INF MdeModulePkg/Universal/MonotonicCounterRuntimeDxe/MonotonicCounterRuntimeDxe.inf
INF MdeModulePkg/Universal/WatchdogTimerDxe/WatchdogTimer.inf

!if $(DISABLE_RESET_SYSTEM) == FALSE
INF MdeModulePkg/Universal/ResetSystemRuntimeDxe/ResetSystemRuntimeDxe.inf
!endif
!if $(PERFORMANCE_MEASUREMENT_ENABLE)
  INF MdeModulePkg/Universal/Acpi/FirmwarePerformanceDataTableDxe/FirmwarePerformanceDxe.inf
!endif

#
# SMM Support
#
!if $(SMM_SUPPORT) == TRUE
  INF UefiPayloadPkg/SmmAccessDxe/SmmAccessDxe.inf
  INF UefiPayloadPkg/SmmControlRuntimeDxe/SmmControlRuntimeDxe.inf
  INF UefiPayloadPkg/BlSupportSmm/BlSupportSmm.inf
  INF MdeModulePkg/Core/PiSmmCore/PiSmmIpl.inf
  INF MdeModulePkg/Core/PiSmmCore/PiSmmCore.inf
  INF UefiCpuPkg/CpuIo2Smm/CpuIo2Smm.inf
  INF UefiCpuPkg/PiSmmCpuDxeSmm/PiSmmCpuDxeSmm.inf
  INF UefiPayloadPkg/PchSmiDispatchSmm/PchSmiDispatchSmm.inf
!if $(PERFORMANCE_MEASUREMENT_ENABLE)
  INF  MdeModulePkg/Universal/Acpi/FirmwarePerformanceDataTableSmm/FirmwarePerformanceSmm.inf
!endif
!endif

!if $(VARIABLE_SUPPORT) == "EMU"
  INF MdeModulePkg/Universal/Variable/RuntimeDxe/VariableRuntimeDxe.inf
!elseif $(VARIABLE_SUPPORT) == "SMMSTORE"
  INF UefiPayloadPkg/SmmStoreFvb/SmmStoreFvbRuntimeDxe.inf
  INF MdeModulePkg/Universal/FaultTolerantWriteDxe/FaultTolerantWriteDxe.inf
  INF MdeModulePkg/Universal/Variable/RuntimeDxe/VariableRuntimeDxe.inf
!elseif $(VARIABLE_SUPPORT) == "SPI"
  INF UefiPayloadPkg/FvbRuntimeDxe/FvbSmm.inf
  INF MdeModulePkg/Universal/FaultTolerantWriteDxe/FaultTolerantWriteSmm.inf
  INF MdeModulePkg/Universal/Variable/RuntimeDxe/VariableSmm.inf
  INF MdeModulePkg/Universal/Variable/RuntimeDxe/VariableSmmRuntimeDxe.inf
!endif

!if $(UNIVERSAL_PAYLOAD) == FALSE
  !if $(SECURE_BOOT_ENABLE) == TRUE
    INF SecurityPkg/VariableAuthenticated/SecureBootConfigDxe/SecureBootConfigDxe.inf
  !endif
!endif

INF UefiCpuPkg/CpuIo2Dxe/CpuIo2Dxe.inf
INF MdeModulePkg/Universal/DevicePathDxe/DevicePathDxe.inf
!if $(MEMORY_TEST) == "GENERIC"
INF  MdeModulePkg/Universal/MemoryTest/GenericMemoryTestDxe/GenericMemoryTestDxe.inf
!elseif $(MEMORY_TEST) == "NULL"
INF  MdeModulePkg/Universal/MemoryTest/NullMemoryTestDxe/NullMemoryTestDxe.inf
!endif
INF MdeModulePkg/Universal/HiiDatabaseDxe/HiiDatabaseDxe.inf
INF MdeModulePkg/Universal/SetupBrowserDxe/SetupBrowserDxe.inf
INF MdeModulePkg/Universal/DisplayEngineDxe/DisplayEngineDxe.inf
INF MdeModulePkg/Universal/PlatformDriOverrideDxe/PlatformDriOverrideDxe.inf
INF MdeModulePkg/Universal/EbcDxe/EbcDxe.inf
INF UefiPayloadPkg/BlSupportDxe/BlSupportDxe.inf

INF MdeModulePkg/Universal/SmbiosDxe/SmbiosDxe.inf
!if $(BOOTSPLASH_IMAGE)
INF MdeModulePkg/Logo/LogoDxe.inf
!endif

#
# PCI Support
#
INF MdeModulePkg/Bus/Pci/PciBusDxe/PciBusDxe.inf
INF MdeModulePkg/Bus/Pci/PciHostBridgeDxe/PciHostBridgeDxe.inf

#
# ISA Support
#
!if $(SERIAL_DRIVER_ENABLE) == TRUE
INF MdeModulePkg/Universal/SerialDxe/SerialDxe.inf
!endif
!if $(SIO_BUS_ENABLE) == TRUE
INF OvmfPkg/SioBusDxe/SioBusDxe.inf
!endif
!if $(PS2_KEYBOARD_ENABLE) == TRUE
INF MdeModulePkg/Bus/Isa/Ps2KeyboardDxe/Ps2KeyboardDxe.inf
!endif
!if $(PS2_MOUSE_ENABLE) == TRUE
INF MdeModulePkg/Bus/Isa/Ps2MouseDxe/Ps2MouseDxe.inf
!endif

#
# Console Support
#
INF MdeModulePkg/Universal/Console/ConPlatformDxe/ConPlatformDxe.inf
INF MdeModulePkg/Universal/Console/ConSplitterDxe/ConSplitterDxe.inf
INF MdeModulePkg/Universal/Console/GraphicsConsoleDxe/GraphicsConsoleDxe.inf
!if $(DISABLE_SERIAL_TERMINAL) == FALSE
INF MdeModulePkg/Universal/Console/TerminalDxe/TerminalDxe.inf
!endif
INF UefiPayloadPkg/GraphicsOutputDxe/GraphicsOutputDxe.inf

#
# SCSI/ATA/IDE/DISK Support
#
INF MdeModulePkg/Universal/Disk/DiskIoDxe/DiskIoDxe.inf
INF MdeModulePkg/Universal/Disk/PartitionDxe/PartitionDxe.inf
INF MdeModulePkg/Universal/Disk/UnicodeCollation/EnglishDxe/EnglishDxe.inf
!if $(ATA_ENABLE) == TRUE
INF MdeModulePkg/Bus/Pci/SataControllerDxe/SataControllerDxe.inf
INF MdeModulePkg/Bus/Ata/AtaBusDxe/AtaBusDxe.inf
!endif
INF MdeModulePkg/Bus/Ata/AtaAtapiPassThru/AtaAtapiPassThru.inf
INF MdeModulePkg/Bus/Scsi/ScsiBusDxe/ScsiBusDxe.inf
INF MdeModulePkg/Bus/Scsi/ScsiDiskDxe/ScsiDiskDxe.inf
!if $(NVME_ENABLE) == TRUE
INF MdeModulePkg/Bus/Pci/NvmExpressDxe/NvmExpressDxe.inf
!endif
!if $(RAM_DISK_ENABLE) == TRUE
INF MdeModulePkg/Universal/Disk/RamDiskDxe/RamDiskDxe.inf
!endif
INF FatPkg/EnhancedFatDxe/Fat.inf

#
# SD/eMMC Support
#
!if $(SD_ENABLE) == TRUE
INF MdeModulePkg/Bus/Pci/SdMmcPciHcDxe/SdMmcPciHcDxe.inf
INF MdeModulePkg/Bus/Sd/EmmcDxe/EmmcDxe.inf
INF MdeModulePkg/Bus/Sd/SdDxe/SdDxe.inf
!endif

#
# Usb Support
#
INF MdeModulePkg/Bus/Pci/UhciDxe/UhciDxe.inf
INF MdeModulePkg/Bus/Pci/EhciDxe/EhciDxe.inf
INF MdeModulePkg/Bus/Pci/XhciDxe/XhciDxe.inf
INF MdeModulePkg/Bus/Usb/UsbBusDxe/UsbBusDxe.inf
INF MdeModulePkg/Bus/Usb/UsbKbDxe/UsbKbDxe.inf
INF MdeModulePkg/Bus/Usb/UsbMassStorageDxe/UsbMassStorageDxe.inf
INF MdeModulePkg/Bus/Usb/UsbMouseDxe/UsbMouseDxe.inf

#
# ACPI Support
#
INF  MdeModulePkg/Universal/Acpi/AcpiTableDxe/AcpiTableDxe.inf
!if $(BOOTSPLASH_IMAGE)
INF  MdeModulePkg/Universal/Acpi/AcpiPlatformDxe/AcpiPlatformDxe.inf
INF  MdeModulePkg/Universal/Acpi/BootGraphicsResourceTableDxe/BootGraphicsResourceTableDxe.inf
!endif

!if $(UNIVERSAL_PAYLOAD) == FALSE
INF MdeModulePkg/Universal/BdsDxe/BdsDxe.inf
INF RuleOverride = UI MdeModulePkg/Application/UiApp/UiApp.inf
INF MdeModulePkg/Application/BootManagerMenuApp/BootManagerMenuApp.inf
#
# UEFI network modules
#
!if $(NETWORK_DRIVER_ENABLE) == TRUE
  !include NetworkPkg/Network.fdf.inc
!endif
!endif

#
# Shell
#
!if $(SHELL_TYPE) == BUILD_SHELL
INF ShellPkg/DynamicCommand/TftpDynamicCommand/TftpDynamicCommand.inf
!if $(PERFORMANCE_MEASUREMENT_ENABLE) == TRUE
INF ShellPkg/DynamicCommand/DpDynamicCommand/DpDynamicCommand.inf
!endif
INF ShellPkg/Application/Shell/Shell.inf
!endif

!if $(UNIVERSAL_PAYLOAD) == TRUE
!if $(SECURE_BOOT_ENABLE) == TRUE

[FV.SECFV]
FvNameGuid         = 2700E2F3-19D2-4E2D-9F13-BC891B9FC62C
BlockSize          = $(FD_BLOCK_SIZE)
FvForceRebase      = FALSE
FvAlignment        = 16
ERASE_POLARITY     = 1
MEMORY_MAPPED      = TRUE
STICKY_WRITE       = TRUE
LOCK_CAP           = TRUE
LOCK_STATUS        = TRUE
WRITE_DISABLED_CAP = TRUE
WRITE_ENABLED_CAP  = TRUE
WRITE_STATUS       = TRUE
WRITE_LOCK_CAP     = TRUE
WRITE_LOCK_STATUS  = TRUE
READ_DISABLED_CAP  = TRUE
READ_ENABLED_CAP   = TRUE
READ_STATUS        = TRUE
READ_LOCK_CAP      = TRUE
READ_LOCK_STATUS   = TRUE

INF SecurityPkg/VariableAuthenticated/SecureBootConfigDxe/SecureBootConfigDxe.inf

!endif
!endif


################################################################################
#
# Rules are use with the [FV] section's module INF type to define
# how an FFS file is created for a given INF file. The following Rule are the default
# rules for the different module type. User can add the customized rules to define the
# content of the FFS file.
#
################################################################################

[Rule.Common.SEC]
  FILE SEC = $(NAMED_GUID) {
    PE32     PE32   Align=32    $(INF_OUTPUT)/$(MODULE_NAME).efi
  }

[Rule.Common.PEI_CORE]
  FILE PEI_CORE = $(NAMED_GUID) {
    PE32     PE32   Align=Auto    $(INF_OUTPUT)/$(MODULE_NAME).efi
    UI       STRING ="$(MODULE_NAME)" Optional
    VERSION  STRING ="$(INF_VERSION)" Optional BUILD_NUM=$(BUILD_NUMBER)
  }

[Rule.Common.PEIM]
  FILE PEIM = $(NAMED_GUID) {
     PEI_DEPEX PEI_DEPEX Optional        $(INF_OUTPUT)/$(MODULE_NAME).depex
     PE32      PE32   Align=Auto           $(INF_OUTPUT)/$(MODULE_NAME).efi
     UI       STRING="$(MODULE_NAME)" Optional
     VERSION  STRING="$(INF_VERSION)" Optional BUILD_NUM=$(BUILD_NUMBER)
  }

[Rule.Common.DXE_CORE]
  FILE DXE_CORE = $(NAMED_GUID) {
    PE32     PE32           $(INF_OUTPUT)/$(MODULE_NAME).efi
    UI       STRING="$(MODULE_NAME)" Optional
    VERSION  STRING="$(INF_VERSION)" Optional BUILD_NUM=$(BUILD_NUMBER)
  }

[Rule.Common.DXE_DRIVER]
  FILE DRIVER = $(NAMED_GUID) {
    DXE_DEPEX    DXE_DEPEX Optional      $(INF_OUTPUT)/$(MODULE_NAME).depex
    PE32     PE32                    $(INF_OUTPUT)/$(MODULE_NAME).efi
    UI       STRING="$(MODULE_NAME)" Optional
    VERSION  STRING="$(INF_VERSION)" Optional BUILD_NUM=$(BUILD_NUMBER)
  }

[Rule.Common.DXE_RUNTIME_DRIVER]
  FILE DRIVER = $(NAMED_GUID) {
    DXE_DEPEX    DXE_DEPEX Optional      $(INF_OUTPUT)/$(MODULE_NAME).depex
    PE32     PE32                    $(INF_OUTPUT)/$(MODULE_NAME).efi
    UI       STRING="$(MODULE_NAME)" Optional
    VERSION  STRING="$(INF_VERSION)" Optional BUILD_NUM=$(BUILD_NUMBER)
  }
[Rule.Common.DXE_SMM_DRIVER]
  FILE SMM = $(NAMED_GUID) {
    SMM_DEPEX SMM_DEPEX Optional       $(INF_OUTPUT)/$(MODULE_NAME).depex
    PE32      PE32                     $(INF_OUTPUT)/$(MODULE_NAME).efi
    UI        STRING="$(MODULE_NAME)" Optional
    VERSION   STRING="$(INF_VERSION)" Optional BUILD_NUM=$(BUILD_NUMBER)
  }

[Rule.Common.SMM_CORE]
  FILE SMM_CORE = $(NAMED_GUID) {
    PE32     PE32                    $(INF_OUTPUT)/$(MODULE_NAME).efi
    UI       STRING="$(MODULE_NAME)" Optional
    VERSION  STRING="$(INF_VERSION)" Optional BUILD_NUM=$(BUILD_NUMBER)
  }

[Rule.Common.UEFI_DRIVER]
  FILE DRIVER = $(NAMED_GUID) {
    DXE_DEPEX    DXE_DEPEX Optional      $(INF_OUTPUT)/$(MODULE_NAME).depex
    PE32     PE32                    $(INF_OUTPUT)/$(MODULE_NAME).efi
    UI       STRING="$(MODULE_NAME)" Optional
    VERSION  STRING="$(INF_VERSION)" Optional BUILD_NUM=$(BUILD_NUMBER)
  }

[Rule.Common.UEFI_DRIVER.BINARY]
  FILE DRIVER = $(NAMED_GUID) {
    DXE_DEPEX DXE_DEPEX Optional      |.depex
    PE32      PE32                    |.efi
    UI        STRING="$(MODULE_NAME)" Optional
    VERSION   STRING="$(INF_VERSION)" Optional BUILD_NUM=$(BUILD_NUMBER)
  }

[Rule.Common.UEFI_APPLICATION]
  FILE APPLICATION = $(NAMED_GUID) {
    PE32     PE32                    $(INF_OUTPUT)/$(MODULE_NAME).efi
    UI       STRING="$(MODULE_NAME)" Optional
    VERSION  STRING="$(INF_VERSION)" Optional BUILD_NUM=$(BUILD_NUMBER)
  }

[Rule.Common.UEFI_APPLICATION.BINARY]
  FILE APPLICATION = $(NAMED_GUID) {
    PE32      PE32                    |.efi
    UI        STRING="$(MODULE_NAME)" Optional
    VERSION   STRING="$(INF_VERSION)" Optional BUILD_NUM=$(BUILD_NUMBER)
  }

[Rule.Common.USER_DEFINED.ACPITABLE]
  FILE FREEFORM = $(NAMED_GUID) {
    RAW ACPI               |.acpi
    RAW ASL                |.aml
  }

[Rule.Common.USER_DEFINED.CSM]
  FILE FREEFORM = $(NAMED_GUID) {
    RAW BIN                |.bin
  }

[Rule.Common.SEC.RESET_VECTOR]
  FILE RAW = $(NAMED_GUID) {
    RAW RAW                |.raw
  }

[Rule.Common.UEFI_APPLICATION.UI]
  FILE APPLICATION = $(NAMED_GUID) {
    PE32      PE32                     $(INF_OUTPUT)/$(MODULE_NAME).efi
    UI        STRING="Enter Setup"
    VERSION   STRING="$(INF_VERSION)" Optional BUILD_NUM=$(BUILD_NUMBER)
  }

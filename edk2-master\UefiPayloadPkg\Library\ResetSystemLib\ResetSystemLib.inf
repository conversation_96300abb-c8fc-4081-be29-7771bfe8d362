## @file
#  Library instance for ResetSystem library class for bootloader
#
#  Copyright (c) 2014 - 2019, Intel Corporation. All rights reserved.<BR>
#  SPDX-License-Identifier: BSD-2-Clause-Patent
#
##

[Defines]
  INF_VERSION                    = 0x00010005
  BASE_NAME                      = ResetSystemLib
  FILE_GUID                      = C5CD4EEE-527F-47df-9C92-B41414AF7479
  MODULE_TYPE                    = BASE
  VERSION_STRING                 = 1.0
  LIBRARY_CLASS                  = ResetSystemLib

  CONSTRUCTOR                    = ResetSystemLibConstructor
#
# The following information is for reference only and not required by the build tools.
#
#  VALID_ARCHITECTURES           = IA32 X64
#

[Sources]
  ResetSystemLib.c

[Packages]
  MdePkg/MdePkg.dec
  MdeModulePkg/MdeModulePkg.dec
  UefiPayloadPkg/UefiPayloadPkg.dec

[LibraryClasses]
  DebugLib
  IoLib
  HobLib
  BaseMemoryLib

[Guids]
  gUefiAcpiBoardInfoGuid


/** @file
  Key Verification and Boot.efi Loading

  This module provides functionality to load and execute the original
  boot.efi file after successful authentication.

  Copyright (c) 2024, Auth Wrapper Project. All rights reserved.
  SPDX-License-Identifier: BSD-2-Clause-Patent

**/

#include "AuthWrapper.h"

/**
  Find the boot.efi file in the current directory.

  @param[out] BootEfiPath         Path to boot.efi file.

  @retval EFI_SUCCESS             Boot.efi found successfully.
  @retval other                   Error occurred during search.

**/
EFI_STATUS
FindBootEfiFile (
  OUT CHAR16 **BootEfiPath
  )
{
  EFI_STATUS                Status;
  EFI_LOADED_IMAGE_PROTOCOL *LoadedImage;
  EFI_SIMPLE_FILE_SYSTEM_PROTOCOL *FileSystem;
  EFI_FILE_PROTOCOL         *Root;
  EFI_FILE_PROTOCOL         *CurrentDir;
  EFI_FILE_PROTOCOL         *BootEfiFile;
  CHAR16                    *DevicePath;
  CHAR16                    *DirectoryPath;
  CHAR16                    *FullPath;

  if (BootEfiPath == NULL) {
    return EFI_INVALID_PARAMETER;
  }

  *BootEfiPath = NULL;

  //
  // Get the loaded image protocol to find current location
  //
  Status = gBS->HandleProtocol (
                  gImageHandle,
                  &gEfiLoadedImageProtocolGuid,
                  (VOID **) &LoadedImage
                  );
  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_ERROR, "Failed to get loaded image protocol: %r\n", Status));
    return Status;
  }

  //
  // Get file system protocol from the device
  //
  Status = gBS->HandleProtocol (
                  LoadedImage->DeviceHandle,
                  &gEfiSimpleFileSystemProtocolGuid,
                  (VOID **) &FileSystem
                  );
  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_ERROR, "Failed to get file system protocol: %r\n", Status));
    return Status;
  }

  //
  // Open root directory
  //
  Status = FileSystem->OpenVolume (FileSystem, &Root);
  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_ERROR, "Failed to open root directory: %r\n", Status));
    return Status;
  }

  //
  // Get the directory path from loaded image file path
  //
  DevicePath = ConvertDevicePathToText (LoadedImage->FilePath, FALSE, FALSE);
  if (DevicePath == NULL) {
    Root->Close (Root);
    return EFI_OUT_OF_RESOURCES;
  }

  //
  // Extract directory path (remove filename)
  //
  DirectoryPath = AllocateCopyPool (StrSize (DevicePath), DevicePath);
  if (DirectoryPath == NULL) {
    FreePool (DevicePath);
    Root->Close (Root);
    return EFI_OUT_OF_RESOURCES;
  }

  //
  // Find last backslash and truncate
  //
  CHAR16 *LastSlash = StrStr (DirectoryPath, L"\\");
  CHAR16 *CurrentSlash = LastSlash;
  while (CurrentSlash != NULL) {
    LastSlash = CurrentSlash;
    CurrentSlash = StrStr (LastSlash + 1, L"\\");
  }
  
  if (LastSlash != NULL) {
    *(LastSlash + 1) = L'\0';  // Keep the trailing backslash
  } else {
    DirectoryPath[0] = L'\0';  // Root directory
  }

  //
  // Try to find 1boot.efi in the same directory
  //
  FullPath = AllocatePool (StrSize (DirectoryPath) + StrSize (L"1boot.efi"));
  if (FullPath == NULL) {
    FreePool (DevicePath);
    FreePool (DirectoryPath);
    Root->Close (Root);
    return EFI_OUT_OF_RESOURCES;
  }

  StrCpyS (FullPath, (StrSize (DirectoryPath) + StrSize (L"1boot.efi")) / sizeof (CHAR16), DirectoryPath);
  StrCatS (FullPath, (StrSize (DirectoryPath) + StrSize (L"1boot.efi")) / sizeof (CHAR16), L"1boot.efi");

  //
  // Try to open 1boot.efi
  //
  Status = Root->Open (
                   Root,
                   &BootEfiFile,
                   FullPath,
                   EFI_FILE_MODE_READ,
                   0
                   );

  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_ERROR, "Failed to find 1boot.efi at %s: %r\n", FullPath, Status));
    
    //
    // Try alternative name: boot.efi
    //
    StrCpyS (FullPath, (StrSize (DirectoryPath) + StrSize (L"boot.efi")) / sizeof (CHAR16), DirectoryPath);
    StrCatS (FullPath, (StrSize (DirectoryPath) + StrSize (L"boot.efi")) / sizeof (CHAR16), L"boot.efi");
    
    Status = Root->Open (
                     Root,
                     &BootEfiFile,
                     FullPath,
                     EFI_FILE_MODE_READ,
                     0
                     );
  }

  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_ERROR, "Failed to find boot.efi: %r\n", Status));
    FreePool (DevicePath);
    FreePool (DirectoryPath);
    FreePool (FullPath);
    Root->Close (Root);
    return Status;
  }

  //
  // Close the file (we just needed to verify it exists)
  //
  BootEfiFile->Close (BootEfiFile);
  Root->Close (Root);

  //
  // Return the full path
  //
  *BootEfiPath = FullPath;

  FreePool (DevicePath);
  FreePool (DirectoryPath);

  DEBUG ((DEBUG_INFO, "Found boot.efi at: %s\n", FullPath));
  return EFI_SUCCESS;
}

/**
  Load and execute the original boot.efi file.

  @retval EFI_SUCCESS             Boot.efi loaded and executed successfully.
  @retval other                   Error occurred during loading/execution.

**/
EFI_STATUS
LoadAndExecuteBootEfi (
  VOID
  )
{
  EFI_STATUS                Status;
  CHAR16                    *BootEfiPath = NULL;
  EFI_DEVICE_PATH_PROTOCOL  *DevicePath;
  EFI_HANDLE                ImageHandle;
  EFI_LOADED_IMAGE_PROTOCOL *LoadedImage;

  DEBUG ((DEBUG_INFO, "Loading boot.efi...\n"));

  //
  // Find the boot.efi file
  //
  Status = FindBootEfiFile (&BootEfiPath);
  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_ERROR, "Failed to find boot.efi: %r\n", Status));
    return Status;
  }

  //
  // Convert file path to device path
  //
  DevicePath = FileDevicePath (NULL, BootEfiPath);
  if (DevicePath == NULL) {
    DEBUG ((DEBUG_ERROR, "Failed to create device path for boot.efi\n"));
    FreePool (BootEfiPath);
    return EFI_OUT_OF_RESOURCES;
  }

  //
  // Load the image
  //
  Status = gBS->LoadImage (
                  FALSE,
                  gImageHandle,
                  DevicePath,
                  NULL,
                  0,
                  &ImageHandle
                  );
  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_ERROR, "Failed to load boot.efi: %r\n", Status));
    FreePool (DevicePath);
    FreePool (BootEfiPath);
    return Status;
  }

  //
  // Get loaded image protocol to set command line if needed
  //
  Status = gBS->HandleProtocol (
                  ImageHandle,
                  &gEfiLoadedImageProtocolGuid,
                  (VOID **) &LoadedImage
                  );
  if (!EFI_ERROR (Status)) {
    //
    // Copy command line arguments from current image
    //
    EFI_LOADED_IMAGE_PROTOCOL *CurrentImage;
    Status = gBS->HandleProtocol (
                    gImageHandle,
                    &gEfiLoadedImageProtocolGuid,
                    (VOID **) &CurrentImage
                    );
    if (!EFI_ERROR (Status) && CurrentImage->LoadOptions != NULL) {
      LoadedImage->LoadOptions = CurrentImage->LoadOptions;
      LoadedImage->LoadOptionsSize = CurrentImage->LoadOptionsSize;
    }
  }

  DEBUG ((DEBUG_INFO, "Starting boot.efi...\n"));
  Print (L"正在启动boot.efi...\n");

  //
  // Start the image
  //
  Status = gBS->StartImage (ImageHandle, NULL, NULL);
  if (EFI_ERROR (Status)) {
    DEBUG ((DEBUG_ERROR, "Failed to start boot.efi: %r\n", Status));
    gBS->UnloadImage (ImageHandle);
  }

  //
  // Clean up
  //
  FreePool (DevicePath);
  FreePool (BootEfiPath);

  return Status;
}
